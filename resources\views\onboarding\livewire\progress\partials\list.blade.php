<ul class="fs-base text-gray-700">
    @foreach ($groups as $group => $items)
        @php
            $groupSummary = $collection->summary($group);
        @endphp
        <li class="mb-3 text-sm"
            x-data="{ expanded: {{ $currentGroup == $group ? 'true' : 'false' }} }">
            <div class="flex items-center gap-2 leading-5" x-on:click="expanded=!expanded;">
                <span
                      class="{{ $groupSummary->isCompleted() ? 'bg-blue-500 border-blue-300 ' : 'border-gray-300 ' }} flex h-[20px] w-[20px] shrink-0 items-center justify-center rounded-full border">
                    <svg width="12"
                         height="10"
                         viewBox="0 0 12 10"
                         fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.259 0.991227C11.4934 1.22564 11.625 1.54352 11.625 1.87498C11.625 2.20643 11.4934 2.52432 11.259 2.75873L5.00901 9.00873C4.7746 9.24307 4.45672 9.37471 4.12526 9.37471C3.79381 9.37471 3.47592 9.24307 3.24152 9.00873L0.741515 6.50873C0.513817 6.27297 0.387824 5.95722 0.390672 5.62948C0.39352 5.30173 0.524981 4.98821 0.756742 4.75645C0.988502 4.52469 1.30202 4.39323 1.62976 4.39038C1.95751 4.38754 2.27326 4.51353 2.50901 4.74123L4.12526 6.35748L9.49152 0.991227C9.72592 0.756889 10.0438 0.625244 10.3753 0.625244C10.7067 0.625244 11.0246 0.756889 11.259 0.991227Z"
                              fill="white" />
                    </svg>
                </span>
                <a
                   class="cursor-pointer text-gray-700 hover:text-gray-500">{{ @config('onboarding.forms.' . $group . '.label') }}</a>
                @if (!$locked)
                    <span class="ml-auto cursor-pointer"
                          >
                        <i class="fa"
                           x-bind:class="{ 'fa-angle-right': !expanded, 'fa-angle-down': expanded }"></i>
                    </span>
                @endif
            </div>
            @if (!$locked)
                <ul class="mt-2 text-xs"
                    x-show="expanded"
                    x-cloak
                    x-collapse>
                    @foreach ($items as $item)
                        <li
                            class="{{ $item->isCompleted() ? '' : '' }} {{ $item->isActive($form) ? 'bg-blue-50 [&>a]:hover:no-underline' : '' }} flex items-center gap-2 py-2 pl-6 pr-2 text-gray-700">
                            <span
                                  class="{{ $item->isCompleted() ? 'bg-blue-500 border-blue-300 ' : 'border-gray-300 ' }} flex h-[16px] w-[16px] shrink-0 items-center justify-center rounded-full border">
                                <svg width="8"
                                     height="8"
                                     viewBox="0 0 12 10"
                                     fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11.259 0.991227C11.4934 1.22564 11.625 1.54352 11.625 1.87498C11.625 2.20643 11.4934 2.52432 11.259 2.75873L5.00901 9.00873C4.7746 9.24307 4.45672 9.37471 4.12526 9.37471C3.79381 9.37471 3.47592 9.24307 3.24152 9.00873L0.741515 6.50873C0.513817 6.27297 0.387824 5.95722 0.390672 5.62948C0.39352 5.30173 0.524981 4.98821 0.756742 4.75645C0.988502 4.52469 1.30202 4.39323 1.62976 4.39038C1.95751 4.38754 2.27326 4.51353 2.50901 4.74123L4.12526 6.35748L9.49152 0.991227C9.72592 0.756889 10.0438 0.625244 10.3753 0.625244C10.7067 0.625244 11.0246 0.756889 11.259 0.991227Z"
                                          fill="white" />
                                </svg>
                            </span>
                            <a href="{{ route($item->route) }}"
                               class="{{ $item->isCompleted() ? 'line-through' : 'hover:underline' }} text-gray-600 hover:text-blue-500">{{ $item->label }}</a>
                        </li>
                    @endforeach
                </ul>
            @endif
        </li>
    @endforeach
</ul>
