<!DOCTYPE html>
<html>

<head>
    <title>Be right back.</title>

    <link href="https://fonts.googleapis.com/css?family=Lato:100"
          rel="stylesheet"
          type="text/css">
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"
          rel="stylesheet"
          type="text/css">

    <style>
        html,
        body {
            height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            width: 100%;
            color: #B0BEC5;
            display: table;
            font-weight: 100;
            font-family: 'Lato', sans-serif;
        }

        .container {
            text-align: center;
            display: table-cell;
            vertical-align: middle;
        }

        .content {
            text-align: center;
            display: inline-block;
        }

        .title {
            font-size: 24px;
            margin: 40px;
            font-weight: 700;
            max-width: 70%;
            background-color: #c13e3e;
            padding: 40px 70px 40px 260px;
            border-radius: 10px;
            color: #fff;
            line-height: 36px;
            position: relative;
            min-height: 100px;
            display: flex;
            align-items: center;
            margin: auto;
        }

        .title:after,
        .title:before {
            content: "";
            position: absolute;
            height: 140px;
            width: 8px;
            background: #fff;
            left: 100px;
            top: 50%;
            transform: translateY(-50%) rotate(45deg);
        }

        .title:before {
            transform: translateY(-50%) rotate(-45deg);
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="content">
            @if (config('app.debug'))
                <div class="title">{{ $message }} Be right back.</div>
                <div class="title" style="background:#B0BEC5; color: black; padding:2rem;">
                    {!! isset($trace) ? $trace : 'No Trace Found' !!}
                </div>
            @else
                <div class="title">Something Went Wrong.</div>
            @endif
        </div>
    </div>
</body>

</html>
