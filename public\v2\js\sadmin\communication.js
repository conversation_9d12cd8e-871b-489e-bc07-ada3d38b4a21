var myEditor1;
var myEditor2;
var emailSubject = '';
var emailContent = '';
var existEmailAttachmentId = [];
var confirmEmail = false;
let attachedFiles = [];
let attachedStaffFiles = [];

$(document).ready(function () {
    let selectedStudent = [];
    $(document).find('html').addClass('overflow-hidden');
    customLoader();
    $.ajaxSetup({
        headers: {
            Authorization: api_token,
        },
    });

    CKEDITOR.ClassicEditor.create(document.querySelector('#ckeditor1'), {
        ckfinder: {
            uploadUrl: site_url + 'api/upload-file-email-text-editor',
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: window.tagJson,
                    minimumCharacters: 0,
                },
            ],
        },
        removePlugins: [
            'RealTimeCollaborativeComments',
            'RealTimeCollaborativeTrackChanges',
            'RealTimeCollaborativeRevisionHistory',
            'PresenceList',
            'Comments',
            'TrackChanges',
            'TrackChangesData',
            'RevisionHistory',
            'Pagination',
            'WProofreader',
            'MathType',
        ],
    })
        .then((editor) => {
            editor.ui.view.editable.element.style.height = '300px';
            myEditor1 = editor;
        })
        .catch((error) => {
            console.error(error);
        });
    CKEDITOR.ClassicEditor.create(document.querySelector('#ckeditor2'), {
        ckfinder: {
            uploadUrl: site_url + 'api/upload-file-email-text-editor',
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: window.tagJson,
                    minimumCharacters: 0,
                },
            ],
        },
        removePlugins: [
            'RealTimeCollaborativeComments',
            'RealTimeCollaborativeTrackChanges',
            'RealTimeCollaborativeRevisionHistory',
            'PresenceList',
            'Comments',
            'TrackChanges',
            'TrackChangesData',
            'RevisionHistory',
            'Pagination',
            'WProofreader',
            'MathType',
        ],
    })
        .then((editor) => {
            editor.ui.view.editable.element.style.height = '300px';
            myEditor2 = editor;
        })
        .catch((error) => {
            console.error(error);
        });

    $('#tabstrip')
        .kendoTabStrip({
            //tabPosition: "left",
            // animation: { open: { effects: "fadeIn" } },
            animation: { open: { effects: '' } },
        })
        .data('kendoTabStrip');

    $('#studentList').kendoGrid({
        dataSource: customDataSource(
            'api/communication-student-data',
            {
                student_name: { type: 'string' },
                course: { type: 'string' },
                email: { type: 'string' },
            },
            {
                batch: $(document).find('#batch').val(),
            }
        ),
        height: getGridTableHeight('#studentList'),
        pageable: customPageableArr(),
        dataBound: function (e) {
            customDataBoundWithCheckbox('#studentList', e);
        },
        change: onChangestudent,
        filterable: false,
        sortable: true,
        columns: [
            {
                selectable: true,
                width: '50px',
            },
            {
                template: function (dataItem) {
                    return manageProfilePic(
                        dataItem.student_id,
                        dataItem.secure_id,
                        dataItem.profile_pic,
                        dataItem.student_name
                    );
                },
                field: 'student_name',
                title: 'FULL NAME',
                filterable: {
                    cell: {
                        operator: 'contains',
                        suggestionOperator: 'contains',
                    },
                },
                attributes: {
                    class: 'border-b border-slate-100 dark:border-slate-700 p-4 pl-8 text-slate-500 dark:text-slate-400',
                },
                headerAttributes: {
                    class: 'bg-gray-100 border-b text-sm dark:border-slate-600 font-medium p-4 pl-8 pt-0 pb-3 text-slate-400 dark:text-slate-200 text-left',
                },
            },
            {
                template: function (dataItem) {
                    return manageCourseWithCss(dataItem.course);
                },
                //   template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-500'>#: course #</div>",
                field: 'course',
                title: 'CURRENT COURSE',
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: email #</div>",
                field: 'email',
                title: 'EMAIL',
            },
        ],
        noRecords: noRecordTemplate(),
    });

    customGridHtml('#studentList');

    function manageCourseWithCss(course) {
        let coursename = course.substr(0, 35);
        let courseHtml =
            "<div class='flex items-center text-sm leading-5 font-normal text-gray-500'>" +
            coursename +
            '..</div>';
        return courseHtml;
    }

    function onChangestudent(e) {
        let studCourseIds = [];
        $('#mailToUser').html('');
        var rows = e.sender.select();
        selectedStudent = [];
        rows.each(function (e) {
            var grid = $('#studentList').data('kendoGrid');
            var dataItem = grid.dataItem(this);
            studCourseIds.push(dataItem.id);
            selectedStudent.push({
                id: dataItem.id,
                student_id: dataItem.student_id,
                name: dataItem.student_name,
                mobile: dataItem.mobile,
            });
        });
        if (studCourseIds.length > 2) {
            $('.studentNameList').addClass('h-16');
        } else {
            $('.studentNameList').removeClass('h-16');
        }
        $(document).find('.studCourseIds').val(studCourseIds);
        $(document).find('.studentNameList').html(getStudentStrData(selectedStudent));
        let selectedCount = this.selectedKeyNames().length;
        let selectedTitle = 'No student selected';
        if (selectedCount > 0) {
            selectedTitle =
                selectedCount == 1 ? '1 student selected' : selectedCount + ' students selected';
            $('#actionstudent').addClass('bottomaction').removeClass('heightzero');
            $('#selectedstudents').prop('checked', true);
        } else {
            $('#actionstudent').removeClass('bottomaction').addClass('heightzero');
        }
        $(document).find('#selected_student_title').text(selectedTitle);
    }

    function getStudentStrData(selectedStudent) {
        let prev = {};
        let studentNameListStr = '';
        selectedStudent.filter(function (arr) {
            var key = arr['student_id'];
            if (prev[key]) return false;
            if ((prev[key] = true)) {
                studentNameListStr +=
                    '<div class="inline-flex items-center justify-center px-2.5 py-1 gap-1 bg-gray-200 rounded-full"><span>' +
                    arr['name'] +
                    '</span>&nbsp;<span class="cursor-pointer k-icon k-i-close remove_stud" data-sid="' +
                    arr['student_id'] +
                    '"></span></div>';
            }
        });
        return studentNameListStr;
    }

    $('#filterStudentModal').kendoWindow(defaultWindowSlideFormat('All Filters'));

    $('#sendMailStudentModal').kendoWindow(defaultWindowSlideFormat('Send Email', 60));

    $('#sendMailStaffModal').kendoWindow(defaultWindowSlideFormat('Send Email', 60));

    $('#loaderForEmail').kendoWindow(windowGenerateLetter('Sending Email'));

    $('#statusForSendEmailModal').kendoWindow(openCenterWindow('Email Send Status', 33, 15, 30));
    $('#statusForSendEmailCommunicationQueueModal').kendoWindow(
        openCenterWindow('Email Send Status', 33, 15, 30)
    );

    $('#sendMailStudentModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            if (!confirmEmail) {
                kendowindowOpen('#closeModelConformation');
                confirmEmail = false;
                e.preventDefault();
            }
        });
    $('#sendMailStaffModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            if (!confirmEmail) {
                kendowindowOpen('#closeModelConformation');
                confirmEmail = false;
                e.preventDefault();
            }
        });

    $('#statusForSendEmailModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            confirmEmail = true;
            $('.resetEmailStudent').trigger('click');
            $('.resetEmailStaff').trigger('click');
            $('#selectedstudents').trigger('click');
            $('#sendMailStudentModal').data('kendoWindow').close();
            $('#sendMailStaffModal').data('kendoWindow').close();
        });

    $('#sendMailStudentModal')
        .data('kendoWindow')
        .bind('open', function (e) {
            attachedFiles = [];
            confirmEmail = false;
        });

    $('#sendMailStaffModal')
        .data('kendoWindow')
        .bind('open', function (e) {
            attachedFiles = [];
            confirmEmail = false;
        });

    $('#closeModelConformation').kendoWindow(openCenterWindow('Discard Email'));

    $('body').on('click', '.discard-yes', function (e) {
        confirmEmail = true;
        $('#closeModelConformation').data('kendoWindow').close();
        $('#sendMailStudentModal').data('kendoWindow').close();
        $('#sendMailStaffModal').data('kendoWindow').close();
        $('#selectedstudents').trigger('click');
        $('.resetEmailStudent').trigger('click');
        $('.resetEmailStaff').trigger('click');
    });

    $('body').on('click', '.discard-no', function (e) {
        $('#closeModelConformation').data('kendoWindow').close();
    });

    $('#sendSmsStudentModal').kendoWindow(defaultWindowSlideFormat('Send SMS'));

    $('#emailTemplatesModal').kendoWindow(openCenterWindow('Email Templates', 70, 15, 15));

    addModalClassToWindows([
        '#emailTemplatesModal',
        '#closeModelConformation',
        '#statusForSendEmailModal',
    ]);

    var insertTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
        transport: {
            read: {
                url: site_url + 'api/get-mail-template-list',
                dataType: 'json',
                type: 'POST',
            },
        },
        schema: {
            data: 'data',
            model: {
                id: 'id',
                hasChildren: 'hasChildren',
                children: 'sub_list',
            },
        },
    });

    $('#insertTemplatePanelBar').kendoPanelBar({
        template: kendo.template($('#email-panelbar-template').html()),
        dataSource: insertTemplateSidebarMenu,
    });

    $('body').on('click', '.selectedStudentSendSms', function () {
        kendowindowOpen('#sendSmsStudentModal');
    });

    $('body').on('click', '.selectedStudentSendEmail', function () {
        myEditor1.setData('');
        setFromEmail('#sendMailStudentModal');
        $(document).find('.email_subject').val('');
        $(document).find('.email_attachment').val('');
        $(document).find('.selected_file').text('');
        var input = document.querySelector('input[name=email_bcc]');
        let tagifyEmailBCC = new Tagify(input, {
            pattern:
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            callbacks: {
                invalid: onInvalidTag,
            },
            templates: {
                tag: tagTemplate,
            },
        });
        tagifyEmailBCC.removeAllTags();
        var input = document.querySelector('input[name=email_cc]');
        let tagifyEmailCC = new Tagify(input, {
            pattern:
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            callbacks: {
                invalid: onInvalidTag,
            },
            templates: {
                tag: tagTemplate,
            },
        });
        tagifyEmailCC.removeAllTags();

        kendowindowOpen('#sendMailStudentModal');
    });

    function onInvalidTag(e) {
        console.log('invalid', e.detail);
        notificationDisplay('Enter email only ', '', 'error');
    }

    function tagTemplate(tagData) {
        return `<tag title="${tagData.value}" contenteditable='false' spellcheck='false' tabIndex="-1" class="tagify__tag bg-gray-300">
                    <x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>&nbsp;
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${tagData.value}</div>
                    </div>
                </tag>`;
    }

    function getCourseTypeList() {
        ajaxActionV2('api/get-course-type-list', 'POST', {}, function (response) {
            if (response.success) {
                var courseList = response.data;
                var mappedData = courseList.map(function (item) {
                    return {
                        label: item.Name,
                        value: item.Id,
                    };
                });
                $('#course_type').kendoRadioGroup({
                    items: mappedData,
                    value: mappedData[0].value,
                });
                addClassAfterBind('#course_type', ['dt1']);
            }
        });
    }

    getCourseTypeList();

    $('body').on('click', '.studentFilter', function () {
        setTimeout(function () {
            setDropdownList('campus', 'communication-campus-list');
            setDropdownList('course_status', 'get-course-status');
            $('input[type=radio][name=distribution_type]:checked').trigger('change');
        }, 500);
        // getCourseTypeList();
        kendowindowOpen('#filterStudentModal');
    });

    $('body').on('click', '#insertTemplate', function () {
        $(document).find('.email_subject').html('');
        $(document).find('.email_content').html('');
        $(document).find('.exist_attachment').html('');
        $(document).find('.exist_attachment_div').hide();
        $('#searchTemplateCommunication').val('').trigger('keyup');
        kendowindowOpen('#emailTemplatesModal');
    });

    $('body').on('click', '.email_template', function () {
        let templateID = $(this).attr('data-id');
        myEditor1.setData('');
        myEditor2.setData('');

        ajaxActionV2(
            'api/get-mail-content',
            'POST',
            { template_id: templateID },
            function (response) {
                emailSubject = response.data[0].email_subject;
                emailContent = response.data[0].content;

                let emailAttachment = kendo.template($('#attachmentList').html())({
                    files: response.data[0].files,
                });

                $(document).find('.email_template_id').val(templateID);
                $(document).find('.exist_attachment_div').show();
                $(document).find('.exist_attachment').html('').html(emailAttachment);
                manageExistEmailAttachmentId();

                $(document).find('.email_subject').html('').html(emailSubject);
                $(document).find('.email_content').html('').html(emailContent);
            }
        );
    });

    $('body').on('click', '.existing_attachment', function () {
        $(this).closest('div').remove();
        manageExistEmailAttachmentId();
    });

    $('body').on('click', '.useEmailTemplateBtn', function () {
        $(document).find('.email_subject').val(emailSubject);
        myEditor1.setData(emailContent);
        myEditor2.setData(emailContent);
        $(document).find('.isTemplateSelect').text(emailSubject);
        $(document).find('.existing_attachment_id').val(existEmailAttachmentId);
        $(document).find('#emailTemplatesModal').getKendoWindow().close();
    });

    $('body').on('click', '.remove_stud', function () {
        let studId = $(this).attr('data-sid');
        let dataRow = $(document)
            .find('.stud_' + studId)
            .parents('tr');
        dataRow.each(function () {
            if ($(this).hasClass('k-state-selected')) {
                $(this).trigger('click');
            }
        });
    });

    $('body').on('change', '#selectedstudents', function () {
        $('.k-checkbox').each(function () {
            if ($(this).closest('tr').is('.k-state-selected')) {
                $(this).click();
            }
        });
    });

    $('body').on('click', '.sendmailToStudent', function () {
        let modalDiv = $(this).parent('.k-window');
        let totalImageCount = emailTotalAttachmentAllowed;
        let formTag = $('#emailFormstudent');
        let formData = new FormData(formTag[0]);

        if ($('.studCourseIds').val().length == 0) {
            notificationDisplay('Select at least one recipient to send the email.', '', 'error');
            return false;
        }

        if (formTag.find('#email_subject').val().length == 0) {
            notificationDisplay('Email subject is required', '', 'error');
            return false;
        }

        if (myEditor1.getData().length == 0) {
            notificationDisplay('Email content is required', '', 'error');
            return false;
        }

        if (attachedFiles.length <= totalImageCount) {
            $.each(attachedFiles, function (index, fileName) {
                formData.append('attachment_file[]', fileName);
            });
        } else {
            notificationDisplay(
                'Please select Only ' + totalImageCount + ' attachments files',
                '',
                'error'
            );
            return false;
        }

        formData.append('email_content', myEditor1.getData());
        formData.append('log_type', 'offer');
        formData.append('mailSendFromModule', 'communication');
        formData.append('email_type', 'course');
        formData.append('is_orientation', '1');
        if (formData.get('email_cc')) {
            let tags_cc = JSON.parse(formData.get('email_cc'));
            formData.set('email_cc', tags_cc.map((item) => item.value).toString());
        }
        if (formData.get('email_bcc')) {
            let tags_bccc = JSON.parse(formData.get('email_bcc'));
            formData.set('email_bcc', tags_bccc.map((item) => item.value).toString());
        }

        modalDiv.addClass('blur-modal');
        ajaxCallWithMethodFileKendoEmail(
            'api/student-send-email-communication',
            formData,
            'POST',
            function (output) {
                // console.log(output);
                // refreshGridData();
                modalDiv.removeClass('blur-modal');
            }
        );
    });

    $('#email_attachment').change(function (event) {
        const files = event.target.files;
        const attachedFilesContainer = $('#attachedFilesContainer');

        $.each(files, function (index, file) {
            const fileName = file;
            // Create a new element to display the file name
            const fileElement = $('<div>').addClass(
                'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
            );
            const fileNameElement = $('<span>')
                .addClass('truncate')
                .attr('title', fileName.name)
                .text(fileName.name);
            const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                // Remove the file from the list, array, and update UI
                fileElement.remove();
                attachedFiles.splice(attachedFiles.indexOf(fileName), 1);
                $('#email_attachment').val(''); // Reset the file
                return false;
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.addClass('mt-2').append(fileElement);

            // Add the file name to the array
            attachedFiles.push(fileName);
        });
    });

    $('#email_attachment_staff').change(function (event) {
        const files = event.target.files;
        const attachedFilesContainer = $('#attachedStaffFilesContainer');

        $.each(files, function (index, file) {
            const fileName = file;
            // Create a new element to display the file name
            const fileElement = $('<div>').addClass(
                'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
            );
            const fileNameElement = $('<span>').addClass('truncate').text(fileName.name);
            const removeButton = $('<button>').addClass('ml-2 text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                // Remove the file from the list, array, and update UI
                fileElement.remove();
                attachedStaffFiles.splice(attachedStaffFiles.indexOf(fileName), 1);
                $('#email_attachment_staff').val(''); // Reset the file input
                return false;
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.append(fileElement);

            // Add the file name to the array
            attachedStaffFiles.push(fileName);
        });
    });

    function windowGenerateLetter(title) {
        return {
            title: title,
            width: '30%',
            height: '30%',
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: '30%',
                left: '35%',
            },
            animation: {
                close: {
                    effects: 'fade:out',
                },
            },
        };
    }

    function openCenterWindow(titleText, widthVal = 34, topVal = 25, leftVal = 33) {
        return {
            title: titleText,
            width: widthVal + '%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: {
                open: {
                    effects: 'fade:in',
                    duration: 300,
                },
                close: {
                    effects: 'fade:out',
                    duration: 300,
                },
            },
        };
    }

    function ajaxCallWithMethodFileKendoV2(url, data, method, callback) {
        var rtrn = $.ajax({
            type: method,
            url: site_url + url,
            data: data,
            processData: false,
            contentType: false,
            headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
            beforeSend: function () {
                kendo.ui.progress($('#loaderEmail'), true);
                $('#sendEmailStatusList').html('');
                $('#titleEmailSuccessMsg').text('');
                $('#titleEmailFailMsg').text('');
                // $("#sendMailStudentModal").data("kendoWindow").close();
                kendowindowOpen('#loaderForEmail');
            },
            success: function (res) {
                $('#loaderForEmail').data('kendoWindow').close();
                kendo.ui.progress($('#loaderEmail'), false);
                kendowindowOpen('#statusForSendEmailModal');
                if (res.success_msg) {
                    $('.checkIcon').show();
                } else {
                    $('.checkIcon').hide();
                }
                $('#titleEmailSuccessMsg').text(res.success_msg);
                $('#titleEmailFailMsg').text(res.fail_msg);
                if (res.fail_msg) {
                    manageSendStatusGrid('#sendEmailStatusList', res.statusData);
                }
                //refreshGridData();
            },
            error: function (result) {
                // kendo.ui.progress($(document.body), false);
                callback(result);
            },
        });
        return rtrn;
    }

    function ajaxCallWithMethodFileKendoEmail(url, data, method, callback) {
        var rtrn = $.ajax({
            type: method,
            url: site_url + url,
            data: data,
            processData: false,
            contentType: false,
            headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
            beforeSend: function () {
                kendo.ui.progress($('#loaderEmail'), true);
                $('#sendEmailStatusList').html('');
                $('#titleEmailSuccessMsg').text('');
                $('#titleEmailFailMsg').text('');
                // $("#sendMailStudentModal").data("kendoWindow").close();
                kendowindowOpen('#loaderForEmail');
            },
            success: function (res) {
                $('#loaderForEmail').data('kendoWindow').close();
                kendo.ui.progress($('#loaderEmail'), false);
                kendowindowOpen('#statusForSendEmailCommunicationQueueModal');
                console.log(res.data.success_msg);
                console.log(res.data);
                if (res.data.success_msg) {
                    $('.checkIcon').show();
                } else {
                    $('.checkIcon').hide();
                }
                $('#statusForSendEmailCommunicationQueueModal')
                    .find('#titleEmailSuccessMsg')
                    .text(res.data.success_msg);
                $('#statusForSendEmailCommunicationQueueModal')
                    .find('#titleEmailFailMsg')
                    .text(res.data.fail_msg);
                // manageSendStatusGrid("#sendEmailStatusList", res.statusData);
                //refreshGridData();
            },
            error: function (result) {
                // kendo.ui.progress($(document.body), false);
                callback(result);
            },
        });
        return rtrn;
    }

    $(document).on('click', '.closeAndRefreshGridCommunication', function () {
        $('#statusForSendEmailCommunicationQueueModal').data('kendoWindow').close();
        $('.k-i-close').trigger('click');
    });
    function manageSendStatusGrid(sendDataGrid, sendData) {
        $(sendDataGrid).kendoGrid({
            dataSource: sendData,
            groupable: false,
            sortable: true,
            height: getGridTableHeight(sendDataGrid),
            columns: [
                {
                    field: 'name',
                    title: 'Name',
                    template: function (dataItem) {
                        return manageProfilePic(
                            dataItem.id,
                            dataItem.secure_id,
                            dataItem.profile_pic,
                            dataItem.name
                        );
                    },
                },
                {
                    field: 'status',
                    title: 'Status',
                },
                {
                    field: 'description',
                    title: 'Description',
                },
            ],
        });
    }

    function manageProfilePic(normalId, id, profile_pic, student_name) {
        let html = '';
        let profileUrl = id ? `${site_url}student-profile-view/${id}` : 'javascript:void();';

        let studProfileUrl = `<a href="${profileUrl}" class="hover:text-blue-500" target="_blank">${student_name}</a>`;

        if (profile_pic == '') {
            let sname = student_name.toUpperCase().split(/\s+/);
            html =
                "<div class='flex items-center stud_" +
                normalId +
                "'><div class='student-profile-pic h-8 w-8 flex items-center justify-center rounded-full bg-blue-500'><span class='text-xs font-medium leading-6'>" +
                sname[0].charAt(0) +
                sname[1].charAt(0) +
                "</span></div>&nbsp;<div class='student-first-name text-sm leading-4 text-gray-600 truncate'>" +
                studProfileUrl +
                '</div></div>';
        } else {
            html =
                "<div class='flex items-center stud_" +
                normalId +
                "'><img class='h-8 w-8 flex items-center justify-center rounded-full' src='" +
                profile_pic +
                "' alt=''>&nbsp;<div class='student-first-name text-sm leading-4 text-gray-600 truncate'>" +
                studProfileUrl +
                '</div></div>';
        }
        return html;
    }

    $('body').on('click', '.k-tabstrip-item', function () {
        $('.closeAction').trigger('click');
    });

    $('body').on('click', '#ccmail', function () {
        $(document).find('.emailccbox').toggle();
    });

    $('body').on('click', '#bccmail', function () {
        $(document).find('.emailbccbox').toggle();
    });

    $('body').on('click', '#ccEmailStaff', function () {
        $(document).find('.staffemailccbox').toggle();
    });

    $('body').on('click', '#bccEmailStaff', function () {
        $(document).find('.staffemailbccbox').toggle();
    });

    $('#staffList').kendoGrid({
        dataSource: customDataSource(
            'api/communication-staff-data',
            {
                staff_name: { type: 'string' },
                position: { type: 'string' },
                email: { type: 'string' },
            },
            {
                position: $(document).find('#position').val(),
            }
        ),
        dataBound: function (e) {
            customDataBoundWithCheckbox('#staffList', e);
        },
        change: onChangestaff,
        height: $(window).height() - 240,
        pageable: customPageableArr(),
        filterable: false,
        sortable: true,
        columns: [
            {
                selectable: true,
                width: '50px',
            },
            {
                template: function (dataItem) {
                    return manageProfilePic(
                        dataItem.id,
                        null,
                        dataItem.profile_pic,
                        dataItem.staff_name
                    );
                },
                field: 'staff_name',
                title: 'FULL NAME',
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: position #</div>",
                field: 'position',
                title: 'POSITION',
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: email #</div>",
                field: 'email',
                title: 'EMAIL',
            },
        ],
        noRecords: noRecordTemplate(),
    });

    customGridHtml('#staffList');

    function onChangestaff(e) {
        let selectedStaff = [];
        let staffIds = [];
        $('#mailToUser').html('');
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $('#staffList').data('kendoGrid');
            var dataItem = grid.dataItem(this);
            staffIds.push(dataItem.id);
            selectedStaff.push({
                id: dataItem.id,
                name: dataItem.staff_name,
                mobile: dataItem.mobile,
            });
        });
        if (staffIds.length > 2) {
            $('.staffNameList').addClass('h-20');
        } else {
            $('.staffNameList').removeClass('h-20');
        }
        $(document).find('.staffIds').val(staffIds);
        $(document).find('.staffNameList').html(getStaffStrData(selectedStaff));
        let selectedCount = this.selectedKeyNames().length;
        let selectedTitle = 'No staff selected';
        if (selectedCount > 0) {
            selectedTitle =
                selectedCount == 1 ? '1 staff selected' : selectedCount + ' staff selected';
            $('#actionstaff').addClass('bottomaction').removeClass('heightzero');
            $('#selectedstaff').prop('checked', true);
        } else {
            $('#actionstaff').removeClass('bottomaction').addClass('heightzero');
        }
        $(document).find('#selected_staff_title').text(selectedTitle);
    }

    function getStaffStrData(selectedStaff) {
        let prev = {};
        let staffNameListStr = '';
        selectedStaff.filter(function (arr) {
            var key = arr['id'];
            if (prev[key]) return false;
            if ((prev[key] = true)) {
                staffNameListStr +=
                    '<div class="inline-flex items-center justify-center px-2.5 py-1 m-0.5 bg-gray-200 rounded-full"><span>' +
                    arr['name'] +
                    '</span>&nbsp;<span class="cursor-pointer k-icon k-i-close remove_stud" data-sid="' +
                    arr['id'] +
                    '"></span></div>';
            }
        });
        return staffNameListStr;
    }

    $('#sms_template').kendoDropDownList({
        dataTextField: 'name',
        dataValueField: 'id',
        optionLabel: 'Select Template',
        dataSource: {
            schema: {
                data: 'data',
            },
            transport: {
                read: {
                    url: site_url + 'api/sms-template-list',
                    dataType: 'json',
                    type: 'POST',
                },
            },
        },
    });

    function refreshGridData() {
        $('#studentList').data('kendoGrid').refresh();
        $('#studentList').data('kendoGrid').dataSource.read();
        $('.k-i-close').trigger('click');
        $('.closeAction').trigger('click');
    }

    $('body').on('click', '.selectedStaffSendEmail', function () {
        setFromEmail('#sendMailStaffModal');
        myEditor2.setData('');
        $(document).find('.email_subject').val('');
        $(document).find('.email_attachment').val('');
        $(document).find('.selected_file').text('');
        var input = document.querySelector('input[name=email_bcc_staff]');
        let tagifyEmailBCCStaff = new Tagify(input, {
            pattern:
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            callbacks: {
                invalid: onInvalidTag,
            },
            templates: {
                tag: tagTemplate,
            },
        });
        tagifyEmailBCCStaff.removeAllTags();
        var input = document.querySelector('input[name=email_cc_staff]');
        let tagifyEmailCCStaff = new Tagify(input, {
            pattern:
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            callbacks: {
                invalid: onInvalidTag,
            },
            templates: {
                tag: tagTemplate,
            },
        });
        tagifyEmailCCStaff.removeAllTags();
        kendowindowOpen('#sendMailStaffModal');
    });

    $('body').on('change', '#selectedstaff', function () {
        $('.k-checkbox').each(function () {
            if ($(this).closest('tr').is('.k-state-selected')) {
                $(this).click();
            }
        });
    });

    $('body').on('click', '.resetEmailStaff', function () {
        myEditor2.setData('');
        $('.isTemplateSelect').text('No Template Selected');
        $('#sendMailStaffModal').find('#student_comm_log_2').prop('checked', false);
        $('#sendMailStaffModal').find('#email_subject').val('');
        $('.existing_attachment_id').val('');
        $('.email_template_id').val('');
        $('#templateStaffFilesContainer').text('');
        $('#attachedStaffFilesContainer').text('');
        var input = document.querySelector('input[name=email_bcc_staff]');
        let tagifyEmailBCCStaff = new Tagify(input);
        tagifyEmailBCCStaff.removeAllTags();
        var input = document.querySelector('input[name=email_cc_staff]');
        let tagifyEmailCCStaff = new Tagify(input);
        tagifyEmailCCStaff.removeAllTags();
    });

    $('body').on('click', '.resetEmailStudent', function () {
        myEditor1.setData('');
        $('.isTemplateSelect').text('No Template Selected');
        $('#sendMailStudentModal').find('#student_comm_log_1').prop('checked', false);
        $('#sendMailStudentModal').find('#email_subject').val('');
        $('.existing_attachment_id').val('');
        $('.email_template_id').val('');
        $('#attachedFilesContainer').text('');
        $('#templateFilesContainer').text('');
        var input = document.querySelector('input[name=email_bcc_staff]');
        let tagifyEmailBCCStaff = new Tagify(input);
        tagifyEmailBCCStaff.removeAllTags();
        var input = document.querySelector('input[name=email_cc_staff]');
        let tagifyEmailCCStaff = new Tagify(input);
        tagifyEmailCCStaff.removeAllTags();
    });

    $('body').on('click', '.sendmailToStaff', function () {
        let modalDiv = $(this).parent('.k-window');
        let totalImageCount = emailTotalAttachmentAllowed;
        let formTag = $('#emailFormstaff');
        let formData = new FormData(formTag[0]);

        if ($('.staffIds').val().length == 0) {
            notificationDisplay('Select at least one recipient to send the email.', '', 'error');
            return false;
        }
        if (formTag.find('#email_subject').val().length == 0) {
            notificationDisplay('Email subject is required', '', 'error');
            return false;
        }

        if (myEditor2.getData().length == 0) {
            notificationDisplay('Email content is required', '', 'error');
            return false;
        }

        if (attachedStaffFiles.length <= totalImageCount) {
            $.each(attachedStaffFiles, function (index, fileName) {
                formData.append('attachment_file[]', fileName);
            });
        } else {
            notificationDisplay(
                'Please select Only ' + totalImageCount + ' attachments files',
                '',
                'error'
            );
            return false;
        }

        formData.append('email_content', myEditor2.getData());
        formData.append('log_type', 'offer');
        formData.append('mailSendFromModule', 'staff-comm');
        if (formData.get('email_cc_staff')) {
            let tags_cc_staff = JSON.parse(formData.get('email_cc_staff'));
            formData.set('email_cc', tags_cc_staff.map((item) => item.value).toString());
        }
        if (formData.get('email_bcc_staff')) {
            let tags_bccc_staff = JSON.parse(formData.get('email_bcc_staff'));
            formData.set('email_bcc', tags_bccc_staff.map((item) => item.value).toString());
        }
        modalDiv.addClass('blur-modal');
        ajaxCallWithMethodFileKendoEmail(
            'api/staff-send-email-communication',
            formData,
            'POST',
            function (output) {
                //notificationDisplay(output.message, '', output.status);
                //refreshGridData();
                modalDiv.removeClass('blur-modal');
            }
        );
    });

    $('body').on('click', '.closeAction', function () {
        $('#actionstaff').removeClass('bottomaction').addClass('heightzero');
        $('#actionstudent').removeClass('bottomaction').addClass('heightzero');
        $('#selectedstudents').trigger('click');
        $('#selectedstaff').trigger('click');
    });

    $('body').on('change', '#sms_template', function () {
        let template_id = $(this).val();
        if (template_id != '' && template_id != 'undefined') {
            let dataArr = { template_id: template_id };
            ajaxcallwithMethod(
                site_url + 'api/get-sms-template-contain',
                dataArr,
                'POST',
                function (output) {
                    $(document).find('#sms_text').val(output.data);
                }
            );
        }
    });

    $('body').on('click', '.sendMsg', function () {
        let sms_text = $('.sms_text').val();
        let modalDiv = $(this).parent('.k-window');
        let url = site_url + 'api/student-add-sms-details';
        let studentData = {};
        ajaxcallwithMethod(url, { studentData }, 'POST', function (output) {
            notificationDisplay('Send SMS', 'Send SMS', 'success');
            $('#sendSmsStudentModal').data('kendoWindow').close();
            modalDiv.removeClass('blur-modal');
        });
        // TODO:: refresh Grid;
    });

    $('body').on('click', '.resetSMS', function () {
        $(document).find('.sms_text').val('');
    });

    $('body').on('click', '.clear_stud_applied_filter', function () {
        let filterId = $(this).attr('data-filter-id');
        // if (filterId == 'all') {
        $(document).find('#appliedStudentFilterList').html('');
        $('#studentList').data('kendoGrid').dataSource.filter([]);
        $('.communicationFilterCountDiv').hide();
        // } else {
        //$(document).find('#'+filterId).prop('checked', false).trigger('change');
        //$(document).find('#applyFilter').trigger('click');
        // }
    });

    function sendSmsUsingApi(phoneNumber, smsContent) {
        var xhr = new XMLHttpRequest();
        xhr.open(
            'GET',
            sendSmsUrl +
                '?apiKey=' +
                SMSApiKey +
                '&to=' +
                phoneNumber +
                '&content=' +
                smsContent +
                '',
            true
        );
        xhr.onreadystatechange = function () {
            if (xhr.readyState == 4 && xhr.status == 200) {
                console.log('success');
            }
            var data = JSON.parse(xhr.response);
            if (data.messages[0].accepted == false) {
                var errorMsg =
                    data.messages[0].error == 'null' || data.messages[0].error == ''
                        ? 'Invalid destination address'
                        : data.messages[0].error;
                notificationDisplay(errorMsg, 'SMS Error', 'error');
            } else {
                notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
            }
        };
        xhr.send();
        return true;
    }

    $('#filterStudentForm').kendoForm({
        //orientation: "vertical",
        validatable: defaultErrorTemplate(),
        formData: {
            campus: '',
            distribution_type: 'dt1',
            course_type: 1,
            days: '',
            fromdate: '',
            todate: '',
            student_id: '',
        },
        type: 'group',
        layout: 'grid',
        grid: { cols: 2, gutter: 16 },
        items: [
            {
                field: 'distribution_type',
                label: 'Distribution Type',
                colSpan: 2,
                editor: 'RadioGroup',
                validation: { required: true },
                editorOptions: {
                    change: function (e) {
                        $(document).find('.dt-fields').closest('.k-form-field').hide();
                        $(document)
                            .find('.' + e.newValue)
                            .closest('.k-form-field')
                            .show();
                        distributionTypeChange(e.newValue);
                        setRequiredFields(e.newValue);
                    },
                    items: [
                        { value: 'dt1', label: 'Student Class List' },
                        { value: 'dt2', label: 'Student Course' },
                        { value: 'dt3', label: 'Student Course Between Dates' },
                        { value: 'dt4', label: 'Particular Student' },
                        {
                            value: 'dt5',
                            label: 'Student Course Finish Between Dates',
                        },
                        { value: 'dt6', label: 'All Student' },
                    ],
                    layout: 'horizontal',
                },
            },
            {
                field: 'campus',
                editor: 'DropDownList',
                label: 'Campus',
                colSpan: 2,
                editorOptions: {
                    dataBound: function (e) {
                        addClassAfterBind('#campus', ['dt1', 'dt6']);
                    },
                },
            },
            {
                field: 'course_type',
                editor: 'RadioGroup',
                label: 'Course Type:',
                colSpan: 2,
                validation: { required: true },
                editorOptions: {
                    // items: [
                    //     {
                    //         label: "ELICOS",
                    //         value: 1,
                    //     },
                    //     {
                    //         label: "VET",
                    //         value: 2,
                    //     },
                    //     {
                    //         label: "Short Course v2",
                    //         value: 16,
                    //     },
                    //     {
                    //         label: "HigherEd",
                    //         value: 17,
                    //     },
                    // ],
                    // value: 1,
                    layout: 'horizontal',
                    labelPosition: 'after',
                    change: function (e) {
                        courseTypeChange(e.newValue);
                    },
                },
            },
            // {
            //     field: "course_type",
            //     label: "Course Type",
            //     colSpan: 2,
            //     editor: "DropDownList",
            //     editorOptions: {
            //         dataBound: function (e) {
            //             addClassAfterBind("#course_type", ["dt1"]);
            //         },
            //         change: function (e) {
            //             courseTypeChange(
            //                 $("#course_type").data("kendoDropDownList").value(),
            //             );
            //         },
            //         dataSource: getDropdownDataSource(
            //             "get-course-type-list",
            //             [],
            //         ),
            //         dataValueField: "Id",
            //         dataTextField: "Name",
            //     },
            // },
            {
                field: 'semester',
                editor: 'DropDownList',
                label: 'Semester',
                colSpan: 1,
                validation: { required: customValidation1() },
                editorOptions: {
                    dataBound: function (e) {
                        addClassAfterBind('#semester', ['dt1']);
                    },
                    optionLabel: 'Select Semester',
                },
            },
            {
                field: 'term',
                editor: 'DropDownList',
                label: 'Term',
                colSpan: 1,
                validation: { required: customValidation1() },
                editorOptions: {
                    dataBound: function (e) {
                        addClassAfterBind('#term', ['dt1']);
                    },
                    optionLabel: 'Select Term',
                },
            },
            {
                field: 'subject',
                editor: 'DropDownList',
                label: 'Subject',
                colSpan: 2,
                validation: { required: customValidation1() },
                editorOptions: {
                    dataBound: function (e) {
                        addClassAfterBind('#subject', ['dt1']);
                    },
                    optionLabel: 'Select Subject',
                },
            },
            {
                field: 'course',
                editor: 'DropDownList',
                label: 'Course',
                colSpan: 2,
                validation: { required: customValidation2() },
                editorOptions: {
                    dataBound: function (e) {
                        addClassAfterBind('#course', ['dt2', 'dt3', 'dt5']);
                    },
                    optionLabel: 'Select Course',
                },
            },
            {
                field: 'course_status',
                editor: 'DropDownList',
                label: 'Course Status',
                colSpan: 2,
                validation: { required: true },
                editorOptions: {
                    dataBound: function (e) {
                        addClassAfterBind('#course_status', ['dt2', 'dt5', 'dt6']);
                    },
                    optionLabel: 'Select Status',
                },
            },
            {
                field: 'fromdate',
                //editor: "DatePicker",
                label: 'Course Duration Between',
                colSpan: 1,
                editor: function (container, options) {
                    $(
                        "<input class='k-input k-valid dateField dt-fields dt3 dt5' id='fromdate' placeholder='Enter From Date' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "' />"
                    ).appendTo(container);
                },
                validation: { required: true },
            },
            {
                field: 'todate',
                // editor: "DatePicker",
                label: ' ',
                colSpan: 1,
                editor: function (container, options) {
                    $(
                        "<input class='k-input k-valid dateField dt-fields dt3 dt5' id='todate' placeholder='Enter End Date' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "' />"
                    ).appendTo(container);
                },
                validation: { required: true },
            },
            {
                field: 'student_id',
                label: 'Student Id',
                colSpan: 2,
                validation: { required: true },
                editor: function (container, options) {
                    $(
                        "<input class='k-input k-valid dt-fields dt4' id='student_id' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "' />"
                    ).appendTo(container);
                },
            },
        ],
        /*submit: function(ev) {
            //console.log(ev.model);
        }*/
    });

    handleCalendar(['#todate', '#fromdate']);

    $('#filterStudentForm').submit(function (e) {
        var form = $('#filterStudentForm').data('kendoForm');
        if (form) {
            form.validate();
        }
        e.preventDefault();
        let extFilterArr = {};
        let appliedFilterArr = [];
        let formData = $(e.target).serializeArray();
        jQuery.each(formData, function (i, field) {
            extFilterArr[field.name] = field.value;
            if (field.value == 'dt1') {
                appliedFilterArr.push({
                    id: 1,
                    value: 'Subject: ' + $('#subject').data('kendoDropDownList').text(),
                });
            } else if (field.value == 'dt2') {
                appliedFilterArr.push({
                    id: 1,
                    value: 'Course Status: ' + $('#course_status').data('kendoDropDownList').text(),
                });
            } else if (field.value == 'dt3') {
                appliedFilterArr.push({
                    id: 1,
                    value:
                        'Course Duration Between: ' +
                        formatDate(new Date($('#fromdate').data('kendoDatePicker').value())) +
                        ' - ' +
                        formatDate(new Date($('#todate').data('kendoDatePicker').value())),
                });
            } else if (field.value == 'dt4') {
                appliedFilterArr.push({
                    id: 1,
                    value: 'Student Id: ' + $('#student_id').val(),
                });
            } else if (field.value == 'dt5') {
                appliedFilterArr.push({
                    id: 1,
                    value:
                        'Course Duration Between: ' +
                        $('#fromdate').data('kendoDatePicker').value() +
                        ' - ' +
                        $('#todate').data('kendoDatePicker').value(),
                });
            } else if (field.value == 'dt6') {
                appliedFilterArr.push({
                    id: 1,
                    value: 'Course Status: ' + $('#course_status').data('kendoDropDownList').text(),
                });
            }
            $('.communicationFilterCountDiv').show();

            // appliedFilterArr.push({ 'id': field.value, 'value': field.value });
        });
        $(document).find('#appliedStudentFilterList').html(setAppliedFilterData(appliedFilterArr));
        manageFilterOnGrid('#studentList', 'extra', extFilterArr);
        $('#filterStudentModal').getKendoWindow().close();
    });

    function formatDate(date) {
        var day = date.getDate();
        var month = date.getMonth() + 1; // Month indexes start from 0
        var year = date.getFullYear();

        // Add leading zeros if day or month is a single digit
        if (day < 10) {
            day = '0' + day;
        }
        if (month < 10) {
            month = '0' + month;
        }

        return day + '-' + month + '-' + year;
    }

    function setRequiredFields(radioValue) {
        $('#course_type').removeAttr('required');
        $('#campus').removeAttr('required');
        $('#semester').removeAttr('required');
        $('#term').removeAttr('required');
        $('#subject').removeAttr('required');
        $('#course').removeAttr('required');
        $('#course_status').removeAttr('required');
        $('#fromdate').removeAttr('required');
        $('#todate').removeAttr('required');
        $('#student_id').removeAttr('required');
        if (radioValue == 'dt1') {
            $('#course_type').attr('required', 'required');
            $('#campus').attr('required', 'required');
            $('#semester').attr('required', 'required');
            $('#term').attr('required', 'required');
            $('#subject').attr('required', 'required');
        } else if (radioValue == 'dt2') {
            $('#course_type').attr('required', 'required');
            $('#course').attr('required', 'required');
            $('#course_status').attr('required', 'required');
        } else if (radioValue == 'dt3') {
            $('#course_type').attr('required', 'required');
            $('#course').attr('required', 'required');
            $('#fromdate').attr('required', 'required');
            $('#todate').attr('required', 'required');
        } else if (radioValue == 'dt4') {
            $('#student_id').attr('required', 'required');
        } else if (radioValue == 'dt5') {
            $('#course_type').attr('required', 'required');
            $('#course').attr('required', 'required');
            $('#course_status').attr('required', 'required');
            $('#fromdate').attr('required', 'required');
            $('#todate').attr('required', 'required');
        } else if (radioValue == 'dt6') {
            $('#course_type').attr('required', 'required');
            $('#course_status').attr('required', 'required');
        }
    }

    function customValidation1() {
        let distVal = $('input[name="distribution_type"]:checked').val();
        if (!distVal) return false; // Return false if no selection is made
        return distVal !== 'dt1'; // Return false for "dt1", true for others
    }

    function customValidation2() {
        let distVal = $('input[name="distribution_type"]:checked').val();
        if (!distVal) return false; // Return false if no selection is made
        return jQuery.inArray(distVal, ['dt2', 'dt3', 'dt5']) === -1;
    }

    $('#course_type').closest('.k-form-field').addClass('dt-fields dt1 dt2 dt3 dt5');

    function setAppliedFilterData(appliedFilterArr) {
        let filterHtml = '';
        if (appliedFilterArr.length > 0) {
            appliedFilterArr.filter(function (arr) {
                filterHtml +=
                    '<div class="inline-flex items-center justify-center space-x-2 px-2 py-1 m-1 bg-gray-100 rounded-full"><span class="text-xs leading-none text-center text-gray-800">' +
                    arr['value'] +
                    '</span><span class="cursor-pointer k-icon k-i-close clear_stud_applied_filter text-blue-500" data-filter-id="' +
                    arr['id'] +
                    '"></span></div>';
            });
            filterHtml +=
                '<div class="inline-flex items-center justify-center space-x-2 m-1"><button class="text-xs leading-tight text-blue-500 clear_stud_applied_filter" data-filter-id="all">Clear Filters</button></div>';
        }
        return filterHtml;
    }

    // /* common function here */
    $('.dateField').kendoDatePicker({
        format: 'dd-MM-yyyy',
    });

    function kendowindowOpen(windowID) {
        let kendoWindow = $(document).find(windowID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    }

    function addClassAfterBind(fieldId, classList) {
        let fieldTag = $(document).find('.k-form-field').find(fieldId);
        let classStr = 'dt-fields ' + classList.join(' ');
        fieldTag.addClass(classStr);
    }

    function distributionTypeChange(distVal) {
        let courseType = $('#course_type').data('kendoRadioGroup').value();
        console.log('ct', courseType);
        if (distVal == 'dt1') {
            let postArr = { course_type: courseType };
            setDropdownList('semester', 'communication-semester-data', postArr);
        } else if (jQuery.inArray(distVal, ['dt2', 'dt3', 'dt5']) !== -1) {
            let postArr = { course_type: courseType };
            setDropdownList('course', 'communication-course-data', postArr);
        } else {
            //
        }
    }

    function courseTypeChange(ctVal) {
        console.log('value', ctVal);
        let distVal = $('input[name="distribution_type"]:checked').val();
        let postArr = { course_type: ctVal };
        if (distVal == 'dt1') {
            setDropdownList('semester', 'communication-semester-data', postArr);
        } else if (jQuery.inArray(distVal, ['dt2', 'dt3', 'dt5']) !== -1) {
            setDropdownList('course', 'communication-course-data', postArr);
        } else {
            //
        }
    }

    function setDropdownList(fieldID, api_url, postArr = {}) {
        $('#' + fieldID).kendoDropDownList({
            autoWidth: true,
            dataTextField: 'text',
            dataValueField: 'value',
            dataSource: getDropdownDataSource(api_url, postArr),
            optionLabel: 'Select ' + fieldID.replace('_', ' '),
            //index: 0,
            dataBound: function (e) {
                //if (this.select() === -1) { //check whether any item is selected
                // this.select(0);
                // this.trigger("change");
                // this.trigger("select");
                onchangeSetDropdownList(
                    fieldID,
                    $('#' + fieldID)
                        .data('kendoDropDownList')
                        .value()
                );
                //}
            },
            select: function (e) {
                if (e.dataItem) {
                    onchangeSetDropdownList(fieldID, e.dataItem.value);
                }
                //$("#"+ fieldID).data("kendoDropDownList").value('');
                //$("#"+ fieldID).data("kendoDropDownList").text('Select');
            },
        });
    }

    function onchangeSetDropdownList(fieldID, value) {
        if (fieldID == 'semester') {
            let postArr = { semester_id: value };
            setDropdownList('term', 'communication-term-data', postArr);
        } else if (fieldID == 'term') {
            let postArr = {
                semester_id: $('#semester').data('kendoDropDownList').value(),
                term: value,
            };
            setDropdownList('subject', 'communication-subject-data', postArr);
        } else if (fieldID == 'subject') {
            //
        } else {
            //
        }
    }

    $('#staffFilterDropdownMenu').kendoDropDownList({
        //autoWidth: true,
        dataTextField: 'text',
        dataValueField: 'value',
        dataSource: [
            { text: 'All Active Staff', value: 'all' },
            { text: 'Staff Position', value: 'position' },
        ],
        index: 0,
        dataBound: function (e) {
            $('#staffFilterDropdownSubMenu').closest('.k-dropdown').hide();
        },
        select: function (e) {
            $('#staffFilterDropdownSubMenu').closest('.k-dropdown').hide();
            if (e.dataItem && e.dataItem.value == 'position') {
                $('#staffFilterDropdownSubMenu').closest('.k-dropdown').show();
            }
            onChangeStaffFilter();
        },
    });

    function onChangeStaffFilter() {
        setTimeout(function () {
            let extFilterArr = {
                type: $('#staffFilterDropdownMenu').val(),
                position: $('#staffFilterDropdownSubMenu').val(),
            };
            manageFilterOnGrid('#staffList', 'extra', extFilterArr);
        }, 200);
    }

    $('body').on('click', '.getMoreStudentListView', function () {});

    function manageExistEmailAttachmentId() {
        let existDocDiv = $(document).find('.existing_attachment');
        var wnd = $('#sendMailStudentModal').data('kendoWindow');
        var checkIsStudentModelClose = wnd.element.is(':hidden');

        existEmailAttachmentId = [];
        if (existDocDiv.length == 0) {
            let noEmailAttachmentHtml =
                '<p class="text-xs leading-tight text-center text-gray-800"> No attachments available </p>';
            $(document).find('.exist_attachment').html('').html(noEmailAttachmentHtml);
        } else {
            existDocDiv.each(function () {
                existEmailAttachmentId.push($(this).attr('data-file-id'));
            });
        }

        $(document).find('.existing_attachment_id').val(existEmailAttachmentId);

        if (!checkIsStudentModelClose) {
            var attachedFilesContainer = $('#templateFilesContainer');
        } else {
            var attachedFilesContainer = $('#templateStaffFilesContainer');
        }

        attachedFilesContainer.html('');
        $.each(existDocDiv, function () {
            const that = $(this);
            const fileName = $(this).attr('data-file-name');

            // Create a new element to display the file name
            const fileElement = $('<div>').addClass('flex items-center');
            const fileNameElement = $('<span>').addClass('truncate').text(fileName);
            const removeButton = $('<button>').addClass('ml-2 text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                fileElement.remove();
                that.closest('div').remove();
                manageExistEmailAttachmentId();
                return false;
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.append(fileElement);
        });
    }
});
