/* Remove tooltip bling */
.tooltip {
    transition: none;
}

/* Start left-sidebar panel */

.sidebar-menu li > a {
    position: relative;
}

.sidebar-menu li > a > .pull-right-container {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translate(0, -50%);
}

.sidebar-menu .treeview-menu {
    display: none;
    list-style: none;
    padding: 0;
    margin: 0;
    padding-left: 10px;
}
input:focus {
    outline: none !important;
}

/*search box */

/* .searchdata input[type="text"],
.searchdata input[type="text"]:focus {
    border: none;
    width: 100%;
    padding: 0px;
    border: none;
    box-shadow: none;
} */

/* Kendo Grid header*/
.k-grid {
    border: none;
}

.k-grid-header .k-grid-filter.k-state-active,
.k-grid-header .k-header-column-menu.k-state-active,
.k-grid-header .k-hierarchy-cell .k-icon.k-state-active {
    background-color: transparent !important;
}

/*checkbox */

.k-checkbox {
    border-radius: 4px;
}

.k-grid td > .k-checkbox {
    vertical-align: inherit;
}
/* 
.k-grid-header th .k-checkbox {
    margin-left: 3px !important;
} */

.k-checkbox:checked {
    border-color: #1890ff;
    color: #fff;
    background-color: #1890ff;
}

.multiselect-wrapper {
}

/* td */

.k-grid tr.k-state-selected > td {
    background-color: #e6f7ff;
}

.demo-section .tabstrip-container {
    width: 500px;
}

/* Tab */

.k-tabstrip-items-wrapper,
.k-tabstrip-items-wrapper .k-item.k-state-active,
.k-tabstrip-items-wrapper .k-item.k-state-selected,
.k-tabstrip-items .k-item,
.k-tabstrip > .k-content {
    border: none;
}

.k-tabstrip-items-wrapper .k-item.k-state-active span,
.k-tabstrip-items-wrapper .k-item.k-state-active svg,
.k-tabstrip-items-wrapper .k-item.k-state-selected span {
    color: rgb(59 130 246);
}

.k-tabstrip-items-wrapper .k-item span {
    color: rgb(107 114 128);
}

.k-tabstrip {
    padding: 4px 0px;
}

.k-tabstrip-content,
.k-tabstrip > .k-content {
    padding: 4px 0px;
}

/* Pagination */

.k-pager-wrap {
    background-color: white;
    color: #6b7280;
}

.k-grid-pager {
    border-top: 1px solid #e5e7eb;
}

.k-pager-numbers .k-link {
    color: #6b7280;
}

.k-pager-numbers .k-link.k-state-selected {
    color: #1890ff;
    background-color: #e6f7ff;
}

.k-pager-numbers .k-link:hover {
    color: #1890ff;
    background-color: #e6f7ff;
}

.gridInfo .k-pager-info {
    /* display: contents; */
    width: 80%;
}

.gridPagination {
    border: none;
}

.k-pager-numbers .k-link,
.k-pager-numbers .k-link:hover,
.k-pager-numbers .k-state-selected {
    border-radius: 0;
    /*Use squares instead of circles*/
    border: 1px solid #bbbbbb;
}

/*Selected page number*/

.k-pager-numbers .k-state-selected {
    border-color: #bbbbbb;
}

/*Pager links*/

.k-pager-wrap > .k-link {
    border-radius: 0;
    border: 1px solid #bbbbbb;
}

.k-pager-wrap > .k-link:hover {
    border-color: #4f4f4f;
}

.k-pager-last {
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
}

.k-pager-first {
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
}

.k-grid-pager .k-link,
.k-grid-pager .k-pager-numbers {
    float: none;
}

/* Page Info */

.gridInfo .k-pager-info {
    /* display: contents; */
    width: 80%;
}

.gridInfo .k-pager-sizes {
    display: inline-flex;
    /* width: 24%; */
    /* border: 1px solid #c9c9c9; */
    border-radius: 5px;
    /* height: 35px; */
}

.gridPagination {
    border: none;
}

/* user profile pic or 2 character white */

.user-profile-pic {
    border-radius: 50%;
    /*background: #337ab7;*/
    color: #fff !important;
    text-align: center;
    flex-shrink: 0;
    font-weight: 500;
}

/* Copy data icon blue */
.copy_data.active span {
    color: #1890ff;
}

/* On click of grid list select box selected */
[type="checkbox"]:checked:hover,
[type="checkbox"]:checked:focus,
[type="radio"]:checked:hover,
[type="radio"]:checked:focus {
    border-color: #1890ff;
    background-color: #1890ff;
}

/*Custom loader set for grid list */
.k-loading-mask .k-loading-image {
    background-image: none !important;
}
div.k-loading-image {
    display: none;
}
span.k-loading-text {
    text-indent: 0;
    top: 50%;
    left: 50%;
    /* background-color: #0F0; */
    /* width: 100%;
    height: 100%; */
    z-index: 9999;
}
.k-loading-mask {
    z-index: 99999;
}
.k-widget.k-window.k-display-inline-flex.blur-modal {
    z-index: 9999;
}
.k-loading-color {
    z-index: 99;
}

/* icon-size */
.k-icon-32 {
    font-size: 32px;
}
.k-icon-48 {
    font-size: 48px;
}
.k-icon-64 {
    font-size: 64px;
}
.icon-color-blue {
    color: #1890ff;
}

.icon-color-orange {
    color: orange;
}

.hideprofiledropdown {
    z-index: 999;
}

/* search auto complete */

.k-autocomplete {
    /* width: 385px; */
    /* padding-left: 35px; */
    min-height: 2.5rem;
    display: flex;
    align-self: center;
}
.k-autocomplete.findStudent {
    width: 385px;
    padding-left: 35px;
    min-height: 40px;
    display: flex;
    align-self: center;
}

.k-autocomplete .k-input {
    background-color: transparent !important;
}

.k-autocomplete .k-input.k-focus,
.k-autocomplete .k-input:focus {
    border-color: transparent;
    box-shadow: none;
}

.student-profile-pic {
    border-radius: 50%;
    color: #fff !important;
    text-align: center;
}

.k-item.k-state-hover .k-state-default .text-sm {
    color: #fff !important;
}

.k-autocomplete > .k-clear-value {
    top: 46%;
}

.k-autocomplete .k-i-loading {
    top: 45%;
    transform: translateY(-50%);
}
/*  */

.sidebar-menu li.active .treeview-menu {
    display: block;
}

.k-menu-scroll-wrapper {
    background-color: #1f2937 !important;
}

.sidebar-menu > li > a {
    /* padding: 0.5rem !important; */
    display: flex !important;
    color: rgb(208, 226, 255) !important;
}

.treeview-menu.second-level {
    background-color: #1f2937 !important;
}

.k-menu-group .k-item > .k-link:hover {
    background-color: transparent !important;
    color: rgb(208, 226, 255) !important;
}
.treeview-menu.second-level .menuli {
    margin-top: 5px !important;
}

.treeview-menu.second-level .menuli a i {
    margin-right: 5px !important;
}

.sidebar-menu .k-menu-scroll-button.k-scroll-up,
.sidebar-menu .k-menu-scroll-button.k-scroll-up {
    display: none !important;
}

.pull-right-container {
    position: absolute !important;
    right: 10px !important;
}

.sidebar-menu li > a > .pull-right-container {
    margin-top: 0px !important;
}

/* DatePicker */

.k-picker-wrap .k-input[type="text"] {
    border-radius: 10px;
    height: 36px !important;
}

.k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

.k-label.k-form-label {
    color: #374151;
    font-weight: 500;
}

/*Student Search Autocomplate*/

.autocomplateremove-margin {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}
/* globalsearch  */
.searchtext {
    /* Heading */
    width: 122px;
    height: 16px;
    /* text-xs / leading-4 / font-medium / uppercase / tracking-wider */
    /* font-family: 'Rubik'; */
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    /* identical to box height, or 133% */
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-feature-settings:
        "kern" off,
        "calt" off;
    /* gray/500 */
    color: #6b7280;
}

.required-field {
    color: red;
}

/* .images_2_upload_button{
    display: none
} */
.file_btn_border {
    width: 84%;
    border: 1px solid #dfe3e9 !important;
    /*border-color: #dfe3e9 !important;*/
}
.upload_file_btn {
    margin-left: -99px !important;
}
.number-only {
    padding: 7px;
    width: 100%;
    border-width: 1px;
    border-radius: 0.5rem;
    height: 2.25rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
/* action Tooltip */
.k-widget.k-tooltip.k-popup.k-group.k-reset {
    padding: 0px;
    background-color: #ffffff;
}
[type="text"]:focus,
[type="email"]:focus,
[type="password"]:focus {
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
    border-color: #1890ff;
}
.errors {
    border: 1px solid #fca5a5 !important;
}

.header .k-autocomplete.findStudent {
    width: 385px;
    padding-left: 35px;
    min-height: 40px;
    display: flex;
    align-self: center;
}
