var agentSyncOption = 'directSync';
var pCommissionFlag = true;
var pBonusFlag = true;
var agentCommissionGridId = '#agentCommissionList';
var agentBonusGridId = '#agentBonusList';
var modifyAgentCommissionModalId = '#modifyAgentCommissionModal';
var approveAgentCommissionModalId = '#approveAgentCommissionModal';
var bulkApproveAgentCommissionModalId = '#bulkApproveAgentCommissionModal';
var syncToXeroAgentCommissionModalId = '#syncToXeroAgentCommissionModal';
var syncFromXeroAgentCommissionModalId = '#syncFromXeroAgentCommissionModal';
var createPOForAgentCommissionModalId = '#createPOForAgentCommissionModal';
var bulkCreatePOForAgentCommissionModalId = '#bulkCreatePOForAgentCommissionModal';

// TODO:INIT include in payment main js
function initializeAgentCommission() {
    $('#manageAgentCommissionModal').kendoWindow(defaultWindowSlideFormat('Agent Commission', 80));
    $(modifyAgentCommissionModalId).kendoWindow(
        defaultWindowSlideFormat('Modify Agent Commission', 50)
    );
    $('#viewAgentCommissionInfoModal').kendoWindow(
        defaultWindowSlideFormat('View Agent Commission Information', 50)
    );

    $(approveAgentCommissionModalId).kendoWindow(
        openCenterWindow('Approve Commission', 36, 10, 32)
    );
    $(bulkApproveAgentCommissionModalId).kendoWindow(
        openCenterWindow('Bulk Approve Commission', 36, 10, 32)
    );

    $(createPOForAgentCommissionModalId).kendoDialog({
        width: '400px',
        title: 'Generate Purchase Order',
        content:
            "Are you sure you want to create purchase order for this selected Agent Commission? <input type='hidden' name='id' id='poForAgentCommissionId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    createPOforAgentCommissionSchedule(
                        $(createPOForAgentCommissionModalId).find('#poForAgentCommissionId').val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(createPOForAgentCommissionModalId),
        visible: false,
    });

    $(bulkCreatePOForAgentCommissionModalId).kendoDialog({
        width: '400px',
        title: 'Bulk Create PO',
        content:
            "Are you sure you want to create purchase order for this selected Agent Commission? <input type='hidden' name='id' id='poForAgentCommissionIds' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    bulkCreatePOforAgentCommissionSchedule(
                        $(bulkCreatePOForAgentCommissionModalId)
                            .find('#poForAgentCommissionIds')
                            .val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(bulkCreatePOForAgentCommissionModalId),
        visible: false,
    });

    $(syncToXeroAgentCommissionModalId).kendoDialog({
        width: '400px',
        title: 'Sync',
        content:
            "Are you sure you want to sync this Agent Commission? <input type='hidden' name='id' id='syncToXeroAgentCommissionId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    syncAgentCommissionSchedule(
                        $(syncToXeroAgentCommissionModalId)
                            .find('#syncToXeroAgentCommissionId')
                            .val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(syncToXeroAgentCommissionModalId),
        visible: false,
    });

    $(syncFromXeroAgentCommissionModalId).kendoDialog({
        width: '400px',
        title: 'Sync',
        content:
            "Are you sure you want to sync from this Agent Commission? <input type='hidden' name='id' id='syncFromXeroAgentCommissionId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    syncFromAgentCommissionSchedule(
                        $(syncFromXeroAgentCommissionModalId)
                            .find('#syncFromXeroAgentCommissionId')
                            .val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(syncFromXeroAgentCommissionModalId),
        visible: false,
    });

    $('#modifyAgentCommissionModal #bonus_gst').kendoSwitch({
        change: function (e) {
            $(document)
                .find(modifyAgentCommissionModalId)
                .find('#bonus_gst')
                .closest('.customSwitchButton')
                .siblings('div')
                .find('.switchText')
                .text(e.checked ? 'Yes' : 'No');
        },
    });
}

function setAgentCommissionData() {
    manageAgentCommission();
    ajaxActionV2('api/get-agent-commission', 'POST', selectedDataArr, function (response) {
        let data = response.data;
        if (data.agentName) {
            let name = data.agentName.toUpperCase().split(/\s+/);
            let shortName =
                name.length >= 2 ? name[0].charAt(0) + name[1].charAt(0) : name[0].substring(0, 2);
            let agentHtml = `<div class='flex items-center gap-1'>
                            <div class='user-profile-pic flex items-center justify-center h-8 w-8 rounded-full bg-blue-500'>
                                <span class='text-xs leading-none tracking-wider'>${shortName}</span>
                            </div>&nbsp;
                        </div>`;
            $(document).find('.agencyProfile').html(agentHtml);
        }
        // if(data.totalPayAmount > 0){
        //     $('.manageAgentCommissionHtml').show();
        // }else{
        //     $('.manageAgentCommissionHtml').hide();
        // }
        $(document).find('.totalStudentPayAmount').text(currencyFormat(data.totalPayAmount));
        $(document)
            .find('.totalAgentCommissionPayable')
            .text(currencyFormat(data.agentCommissionPayable));
        $(document)
            .find('.totalAgentCommissionPaid')
            .text(currencyFormat(data.agentCommissionPaid));
        $(document).find('.totalAgentCommissionToRefund').text(currencyFormat(0));
        $(document).find('.agency_name').text(data.agentName);
        $(document).find('#exportAgentStudentData').attr('data-agent-id', data.agentId);
        manageAgentBonusData();
    });

    $(agentCommissionGridId).kendoTooltip({
        filter: 'td .action-schedule',
        position: 'bottom',
        showOn: 'click',
        width: 180,
        show: function (e) {
            // Remove the arrow element from the tooltip
            e.sender.popup.element.find('.k-callout').remove();
            e.sender.popup.wrapper.addClass('tw-fadein');
            e.sender.popup.wrapper.css({
                width: '225px',
                right: '44px',
                left: 'unset',
            });
        },
        content: function (e) {
            let dataItem = $(agentCommissionGridId)
                .data('kendoGrid')
                .dataItem(e.target.closest('tr'));
            return kendo.template($('#agentCommissionActionTemplate').html())({
                id: dataItem.id,
                is_approve: dataItem.is_approved,
                comm_valid_range: dataItem.comm_valid_range,
                agent_commission_id: dataItem.agent_commission_id,
                is_payment_past_validity: dataItem.is_payment_past_validity,
                formatted_invoice_number: dataItem.formatted_invoice_number,
                xero_invoice: null,
                xero_connect: isXeroConnectVal,
                is_agent_sync: isAgentSync,
                agent_comm_sync_option: agentSyncOption,
            });
        },
    });
}

function onSelectAgentTab(e) {
    let currentTabName = $(e.item).find('> .k-link').text();
    if (currentTabName == 'Agent Commission') {
        manageAgentCommissionData();
    } else if (currentTabName == 'Agent Bonus') {
        manageAgentBonusData();
    }
}

function manageAgentCommission() {
    manageAgentCommissionData();
    $('#agentCommissionTabStrip').kendoTabStrip({
        animation: defaultOpenAnimation(),
        select: onSelectAgentTab,
    });
}

function manageAgentCommissionData() {
    let pCommissionBound = false;
    // let isXeroConnectVal = false;
    let agentSyncOption = false;
    // let isAgentSync = false;
    if (pCommissionFlag) {
        // TODO:FLAG
        pCommissionFlag = false;
        // pCommissionFlag = true;
        $(agentCommissionGridId).kendoGrid({
            dataSource: {
                type: 'json',
                transport: {
                    read: {
                        url: site_url + 'api/get-agent-commission-data',
                        dataType: 'json',
                        type: 'POST',
                        data: selectedDataArr,
                    },
                },
                requestEnd: function (e) {
                    if (e.type === 'read' && e.response) {
                        isXeroConnectVal = e.response.data.xeroConnect;
                        agentSyncOption = e.response.data.syncOption;
                        isAgentSync = e.response.data.isAgentSync;
                        manageXeroColumnsForAgentGrid(agentCommissionGridId);
                    }
                },
                schema: defaultSchema({
                    invoice_no: { type: 'string' },
                    formatted_invoice_number: { type: 'string' },
                    paid_date: { type: 'date' },
                    due_date: { type: 'date' },
                    //commission_payable: { type: "string" },
                    //gst_amount: { type: "string" },
                    //commission_paid: { type: "string" },
                    //refund_amount: { type: "string" },
                    //GST_to_refund: { type: "string" },
                    //comm_to_refund: { type: "string" },
                }),
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            // pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    selectable: true,
                    width: '44px',
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((formatted_invoice_number !=null) ? formatted_invoice_number :'NA') #</div>",
                    field: 'invoice_no',
                    title: 'Agent Invoice',
                    minWidth: 100,
                    minResizableWidth: 80,
                },
                {
                    template: function (dataItem) {
                        return managePaymentMode(dataItem.payment_mode);
                    },
                    field: 'mode',
                    title: 'Payment Mode',
                    minWidth: 100,
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#: ((paid_date != null) ? kendo.toString(paid_date, displayDateFormatJS) : 'NA') #</div>",
                    field: 'paid_date',
                    title: 'Paid Date',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#: ((due_date != null) ? kendo.toString(due_date, displayDateFormatJS) : 'NA') #</div>",
                    field: 'due_date',
                    title: 'Due Date',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((commission_payable != null) ? currencyFormat(commission_payable)  :'0')#</div>",
                    field: 'commission_payable',
                    title: 'Commission',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((gst_amount != null) ?  currencyFormat(gst_amount) : '0')#</div>",
                    field: 'gst_amount',
                    title: 'GST',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((commission_payable_with_gst != null) ? currencyFormat(commission_payable_with_gst)  :'0')#</div>",
                    field: 'commission_payable_with_gst',
                    title: 'Comm.Payable',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((commission_paid != null) ? currencyFormat(commission_paid) : '0')#</div>",
                    field: 'commission_paid',
                    title: 'Comm.Paid',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((comm_to_refund != null) ?  currencyFormat(comm_to_refund) : '0')#</div>",
                    field: 'refund_amount',
                    title: 'Refund Amount',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#:((GST_to_refund != null) ?  currencyFormat(GST_to_refund) : '0')#</div>",
                    field: 'GST_to_refund',
                    title: 'GST Refund',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template: function (dataItem) {
                        return manageXeroStatus(dataItem.xero_invoice); //Same as paymentTab.js
                    },
                    field: 'xero_invoice',
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Xero Sync</a>",
                    minWidth: 80,
                    minResizableWidth: 60,
                    sortable: false,
                },
                {
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Xero Sync Time</a>",
                    //template: "<div class='start_date flex items-center text-13 leading-4 text-gray-600 action-div'>#: (typeof(xero_invoice) != 'undefined' && xero_invoice !== null && xero_invoice.xero_synced_at !== null) ? convertJsDateTimeFormat(xero_invoice.xero_synced_at) : '--' #</div>",
                    template: function (dataItem) {
                        return manageAgentCommissionSyncedAt(dataItem.xero_invoice);
                    },
                    field: 'xero_synced_at',
                    minWidth: 90,
                    minResizableWidth: 75,
                    sortable: false,
                },
                {
                    template: function (dataItem) {
                        return manageAgentCommissionStatus(dataItem);
                    },
                    field: 'is_approved',
                    title: 'Status',
                    minWidth: 100,
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#: ((remarks !=null) ? remarks : '---') #</div>",
                    field: 'remarks',
                    title: 'Remarks',
                    minWidth: 90,
                    minResizableWidth: 75,
                },
                // {
                //     template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: agency_name #</div>",
                //     field: "agency_name",
                //     title: "AGENT NAME",
                // },
                {
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                    template: function (dataItem) {
                        return agentCommissionManageActionTd(dataItem.id);
                    },
                    field: 'action',
                    title: 'Action',
                    sortable: false,
                    filterable: false,
                    minWidth: 80,
                    minResizableWidth: 60,
                },
            ],
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                //onBoundAgentCommission(e);
                togglePagination(agentCommissionGridId);
                manageRowBindForAgentCommissionGrid(e);
                pCommissionBound = true;
            },
            change: function (e) {
                //let isRowSelected = (isAgentSync == 1 && this.select().length > 0) ? true : false;
                handleAgentButtonTitleAttr('.syncAndPOBtn', this.select().length > 0);
            },
        });

        customGridHtml(agentCommissionGridId);
    } else {
        refreshGrid(agentCommissionGridId, selectedDataArr, true);
        pCommissionBound = true;
    }

    let intervalId = setInterval(function () {
        if (pCommissionBound) {
            onBoundAgentCommission();
            clearInterval(intervalId);
        }
    }, 1000);
}

function manageAgentCommissionSyncedAt(xero_invoice) {
    let xero_synced_at = '--';
    if (typeof xero_invoice == 'undefined' || xero_invoice == null) {
        xero_synced_at = '--';
    } else if (xero_invoice.xero_synced_at !== null) {
        xero_synced_at = convertJsDateTimeFormat(xero_invoice.xero_synced_at);
    } else if (xero_invoice.xero_failed_at !== null) {
        xero_synced_at = convertJsDateTimeFormat(xero_invoice.xero_failed_at);
    } else if (
        typeof xero_invoice.xero_data.purchase_order != 'undefined' &&
        xero_invoice.xero_data.purchase_order !== null
    ) {
        xero_synced_at = convertJsDateTimeFormat(xero_invoice.created_at);
    }

    return `<div class='start_date flex items-center text-13 leading-4 text-gray-600 action-div'>${xero_synced_at}</div>`;
}

function handleAgentButtonTitleAttr(selector, isRowSelected) {
    if (isRowSelected) {
        //$(selector).removeAttr("disabled");
        $('.bulkPOCreateBtn').attr('title', 'Create PO for selected items');
        $('.bulkSyncPaymentBtn').attr('title', 'Sync selected items to xero');
    } else {
        //$(selector).attr("disabled", true);
        $(selector).attr('title', 'Select at least one row');
    }
}

function manageXeroColumnsForAgentGrid(gridId) {
    let currentGrid = $(gridId).data('kendoGrid');
    let agentDiv = $(document).find('.agentCommissionListDiv');
    agentDiv.find('.syncAndPOBtn').hide();
    if (isXeroConnectVal == 1) {
        if (agentSyncOption == 'createPO') {
            agentDiv.find('.bulkPOCreateBtn').show();
            //agentDiv.find(".bulkPOCreateBtn").show().prop("disabled", true);
        } else {
            agentDiv.find('.bulkSyncPaymentBtn').show();
            //agentDiv.find(".bulkSyncPaymentBtn").show().prop("disabled", true);
        }
        currentGrid.showColumn('xero_invoice');
        currentGrid.showColumn('xero_synced_at');
    } else {
        currentGrid.hideColumn('xero_invoice');
        currentGrid.hideColumn('xero_synced_at');
    }
}

function manageAgentBonusData() {
    let pBonusBound = false;
    if (pBonusFlag) {
        // TODO:FLAG
        pBonusFlag = false;
        // pBonusFlag = true;
        $(agentBonusGridId).kendoGrid({
            dataSource: customDataSource(
                'api/get-agent-bonus-data',
                {
                    invoice_no: { type: 'integer' },
                    formatted_invoice_number: { type: 'integer' },
                    GST_to_refund: { type: 'integer' },
                    remarks: { type: 'string' },
                    paid_date: { type: 'date' },
                },
                selectedDataArr
            ),
            // pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    selectable: true,
                    width: '44px',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#:((formatted_invoice_number != null) ? formatted_invoice_number :'NA') #</div>",
                    field: 'invoice_no',
                    title: 'Agent Invoice',
                    minwidth: 100,
                    minResizableWidth: 80,
                },
                {
                    template: function (dataItem) {
                        return managePaymentMode(dataItem.payment_mode);
                    },
                    field: 'mode',
                    title: 'Payment Mode',
                    minWidth: 100,
                    minResizableWidth: 80,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: ((paid_date != null) ? kendo.toString(paid_date, displayDateFormatJS) : 'NA') #</div>",
                    field: 'paid_date',
                    title: 'Paid Date',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#:((bonus_amount != null) ? currencyFormat(bonus_amount) : '0')#</div>",
                    field: 'bonus_amount',
                    title: 'Bonus Amount',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template: function (dataItem) {
                        return manageGST(dataItem.bonus_gst, dataItem.bonus_amount);
                    },
                    field: 'gst_amount',
                    title: 'Bonus GST',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#:((GST_to_refund != null) ? currencyFormat(GST_to_refund) : '0')#</div>",
                    field: 'GST_to_refund',
                    title: 'GST Refund',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: ((remarks !=null) ? remarks :'---') #</div>",
                    field: 'remarks',
                    title: 'Remarks',
                    minWidth: 80,
                    minResizableWidth: 60,
                },
            ],
            noRecords: noRecordTemplate(),
            //dataBound: onBoundAgentBonus,
            dataBound: function (e) {
                //onBoundAgentBonus(e);
                togglePagination(agentBonusGridId);
                manageRowBindForAgentBonusGrid(e);
                pBonusBound = true;
            },
        });
        customGridHtml(agentBonusGridId);
    } else {
        refreshGrid(agentBonusGridId, selectedDataArr, true);
        pBonusBound = true;
    }

    let intervalId = setInterval(function () {
        if (pBonusBound) {
            onBoundAgentBonus();
            clearInterval(intervalId);
        }
    }, 1000);
}

function onBoundAgentCommission(e) {
    $(document).find('.agentCommissionLoadingDiv').hide();
    //togglePagination(agentCommissionGridId);
    $('.searchInputField').val('');
    setTimeout(function () {
        //$(agentCommissionGridId).data("kendoGrid").dataSource.filter([]);
        setTimeout(function () {
            let hasAgentCommission =
                $(agentCommissionGridId).data('kendoGrid').dataSource.view().length > 0;
            $(document).find('.noDataAgentCommission').toggleClass('hidden', hasAgentCommission);
            $(document).find('.agentCommissionListDiv').toggleClass('hidden', !hasAgentCommission);
        });
    });
}

function agentCommissionManageActionTd(id) {
    return `<div class="action-schedule tw-btn-action tw-action--autohide glob-tooltip" data-id="${id}" title="More options">
            <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
        </div>`;
}

function onBoundAgentBonus(e) {
    $(document).find('.agentBonusLoadingDiv').hide();
    //togglePagination(agentBonusGridId);
    $('.searchInputField').val('');
    setTimeout(function () {
        //$(agentBonusGridId).data("kendoGrid").dataSource.filter([]);
        setTimeout(function () {
            let hasAgentBonus = $(agentBonusGridId).data('kendoGrid').dataSource.view().length > 0;
            $(document).find('.noDataAgentBonus').toggleClass('hidden', hasAgentBonus);
            $(document).find('.agentBonusListDiv').toggleClass('hidden', !hasAgentBonus);
        }, 2000);
    }, 1000);
}

function manageGST(GST, bonusAmount) {
    let bonus_gst = '';
    let bonusGST = 0.0;
    if (GST == 'GST') {
        bonusGST = bonusAmount * 0.1;
    } else {
        bonusGST = 0.0;
    }
    //bonus_gst = parseInt(bonusGST);
    bonus_gst = currencyFormat(bonusGST);
    return (
        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>" +
        bonus_gst +
        '</div>'
    );
}

function manageRowBindForAgentCommissionGrid(e) {
    $(e.sender.tbody)
        .find('tr')
        .each(function () {
            //TODO::GNG-3022 (Add class for red highlight data as cancelled)
            $(this).toggleClass('inActiveData', !isActiveCourse);

            $(this)
                .find('td:not(:first-child, :last-child)')
                .click(function () {
                    let rowData = e.sender.dataItem(this.parentElement);
                    rowData.xero_connect = isXeroConnectVal;
                    rowData.is_agent_sync = isAgentSync;
                    rowData.agent_comm_sync_option = agentSyncOption;
                    viewAgentCommission(rowData);
                });
        });
}

function manageRowBindForAgentBonusGrid(e) {
    $(e.sender.tbody)
        .find('tr')
        .each(function () {
            //TODO::GNG-3022 (Add class for red highlight data as cancelled)
            $(this).toggleClass('inActiveData', !isActiveCourse);
        });
}

function viewAgentCommission(dataItem) {
    dataItem.gst_amount = parseInt(dataItem.gst_amount);
    dataItem.commission_payable = parseInt(dataItem.commission_payable);
    dataItem.commission_paid = parseInt(dataItem.commission_paid);
    kendoWindowOpen('#viewAgentCommissionInfoModal');
    $(document)
        .find('#viewAgentCommissionInfo')
        .html(kendo.template($('#viewAgentCommissionInfoTemplate').html())(dataItem));
}

function syncFromAgentCommissionSchedule(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2(
            'api/sync-agent-commission-from-xero',
            'POST',
            { id: primaryID },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    reloadDataAfterSyncAgentCommission();
                }
            }
        );
    }
}

function syncAgentCommissionSchedule(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2('api/sync-agent-commission', 'POST', { id: primaryID }, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                reloadDataAfterSyncAgentCommission();
            }
        });
    }
}

function createPOforAgentCommissionSchedule(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2(
            'api/create-po-for-agent-commission',
            'POST',
            { id: primaryID, type: 'single' },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    reloadGrid(agentCommissionGridId);
                }
            }
        );
    }
}

function bulkCreatePOforAgentCommissionSchedule(primaryIDs) {
    if (primaryIDs.length > 0) {
        ajaxActionV2(
            'api/create-po-for-agent-commission',
            'POST',
            { ids: primaryIDs, type: 'bulk' },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    reloadGrid(agentCommissionGridId);
                }
            }
        );
    }
}

function reloadDataAfterSyncAgentCommission() {
    $('#viewAgentCommissionInfoModal').data('kendoWindow').close();
    setTimeout(function () {
        reloadGrid(agentCommissionGridId);
        paymentsCardUpdate();
    }, 1000);
}

function approveAgentCommission(dataArr) {
    ajaxActionV2('api/approve-agent-commission', 'POST', dataArr, function (response) {
        if (response.status == 'success') {
            handleNotificationForApprove(response, dataArr['approveType']);
            refreshGrid2(agentCommissionGridId);
        } else {
            notificationDisplay(response.message, '', response.status);
        }
    });
}

function handleNotificationForApprove(response, type = 'single') {
    if (type == 'bulk') {
        const { success_count, error_count, success_msg, error_msg } = response.data;
        if (success_count > 0) {
            notificationDisplay(success_msg, '', 'success');
            if (error_count > 0) {
                setTimeout(() => notificationDisplay(error_msg, '', 'error'), 4000);
            }
        } else if (error_count > 0) {
            notificationDisplay(error_msg, '', 'error');
        }
        $(bulkApproveAgentCommissionModalId).data('kendoWindow').close();
    } else {
        $(approveAgentCommissionModalId).data('kendoWindow').close();
        notificationDisplay(response.message, '', response.status);
    }
}

function manageAgentCommissionStatus(dataItem) {
    const { status, bgColor } = getAgentCommissionCurrentStatus(dataItem);
    return `<div class='inline-flex items-center justify-center px-3 py-1 bg-${bgColor}-100 rounded-full border'>
                <span class='text-xs leading-5 text-center text-${bgColor}-800 whitespace-nowrap'>
                    ${status}
                </span>
            </div>`;
}

function getAgentCommissionCurrentStatus(dataItem) {
    let isApprove = dataItem.is_approved;
    let isProcess = dataItem.is_process;
    let isReverse = dataItem.is_reversed;

    let payableAmount = dataItem.commission_payable;
    let paidAmount = dataItem.commission_paid;

    let status = 'NA';
    let bgColor = 'gray';

    if (isApprove == 0) {
        bgColor = 'red';
        status = 'Not Approved';
    } else if (isApprove == 1 && isProcess == 0) {
        bgColor = 'green';
        status = 'Approved';
    } else if (isProcess == 1 && paidAmount >= payableAmount) {
        bgColor = 'green';
        status = 'Paid';
    } else if (isProcess == 1 && payableAmount > 0) {
        bgColor = 'yellow';
        status = 'Partially Paid';
    } else if (isProcess == 1 && payableAmount == 0) {
        bgColor = 'gray';
        status = 'Unpaid';
    } else if (isProcess == 0) {
        bgColor = 'primary-blue';
        status = 'In Process';
    }

    return { status, bgColor };
}

function getExportAgentCommissionData(gridId) {
    let grid = $(gridId).data('kendoGrid');
    let selectedRows = grid.select();
    let selectedData = [];

    selectedRows.each(function (index, row) {
        let dataItem = grid.dataItem(row);

        let { status } = getAgentCommissionCurrentStatus(dataItem);
        dataItem.agent_comm_status = status;
        dataItem.mode = dataItem.payment_mode;
        dataItem.invoice_no = dataItem.formatted_invoice_number;

        dataItem.paid_date = dateFormatForExport(dataItem.paid_date, displayDateFormatJS);
        dataItem.due_date = dateFormatForExport(dataItem.due_date, displayDateFormatJS);

        if (isXeroConnectVal == 1 && dataItem.xero_invoice !== undefined) {
            const { status, icon } = getXeroStatus(dataItem.xero_invoice);
            dataItem.xero_status = status;

            if (dataItem.xero_invoice == null) {
                dataItem.xero_synced_at = '--';
            } else if (dataItem.xero_invoice.xero_synced_at !== null) {
                dataItem.xero_synced_at = dateFormatForExport(
                    dataItem.xero_invoice.xero_synced_at,
                    displayDateTimeFormatJS
                );
            } else if (dataItem.xero_invoice.xero_failed_at !== null) {
                dataItem.xero_synced_at = dateFormatForExport(
                    dataItem.xero_invoice.xero_failed_at,
                    displayDateTimeFormatJS
                );
            } else if (dataItem.xero_invoice.xero_data.purchase_order !== null) {
                dataItem.xero_synced_at = dateFormatForExport(
                    dataItem.xero_invoice.created_at,
                    'yyyy-mm-dd hh:mm:ss'
                );
            } else {
                dataItem.xero_synced_at = '--';
            }
        }

        selectedData.push(dataItem);
    });

    if (selectedData.length === 0) {
        notificationDisplay('Please select at least one row.', '', 'error');
        return false;
    }

    let columns = grid.columns
        .map(function (column) {
            switch (column.field) {
                case 'is_approved':
                    return { field: 'agent_comm_status', title: 'Status' };
                case 'xero_invoice':
                case 'xero_synced_at':
                    return checkAndReturnXeroColumn(column.field);
                    break;
                case 'action':
                    return undefined;
                default:
                    return column;
            }
        })
        .filter((column) => column !== undefined);

    return { selectedData, columns };
}

function getExportAgentBonusData(gridId) {
    let grid = $(gridId).data('kendoGrid');
    let selectedRows = grid.select();
    let selectedData = [];

    selectedRows.each(function (index, row) {
        let dataItem = grid.dataItem(row);
        dataItem.mode = dataItem.payment_mode;
        dataItem.invoice_no = dataItem.formatted_invoice_number;
        dataItem.paid_date =
            dataItem.paid_date != null
                ? kendo.toString(kendo.parseDate(dataItem.paid_date), 'yyyy-MM-dd')
                : 'NA';
        selectedData.push(dataItem);
    });

    if (selectedData.length === 0) {
        notificationDisplay('Please select at least one row.', '', 'error');
        return false;
    }
    let columns = grid.columns.filter((column) => column !== undefined);

    return { selectedData, columns };
}

$('body').on('click', '#exportAgentCommissionList', function (e) {
    e.preventDefault();
    let result = getExportAgentCommissionData(agentCommissionGridId);
    if (result) {
        const { selectedData, columns } = result;
        exportToExcel(selectedData, columns, 'Agent Commission List');
    }
    //gridExportExcelData(agentCommissionGridId, "agentCommissionList");
});

$('body').on('click', '#exportAgentBonusList', function (e) {
    e.preventDefault();
    let result = getExportAgentBonusData(agentBonusGridId);
    if (result) {
        const { selectedData, columns } = result;
        exportToExcel(selectedData, columns, 'Agent Bonus List');
    }
    //gridExportExcelData(agentBonusGridId, "agentBonusList");
});

$('body').on('click', '.manageAgentCommissionBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-agent-commission', 'POST', selectedDataArr, function (response) {
        let data = response.data;
        kendoWindowOpen('#manageAgentCommissionModal');
        $(document).find('.totalStudentPayAmount').text(currencyFormat(data.totalPayAmount));
        $(document)
            .find('.totalAgentCommissionPayable')
            .text(currencyFormat(data.agentCommissionPayable));
        $(document)
            .find('.totalAgentCommissionPaid')
            .text(currencyFormat(data.agentCommissionPaid));
        $(document).find('.totalAgentCommissionToRefund').text(currencyFormat(0));
        $(document).find('.agency_name').text(data.agentName);
        manageAgentCommissionData();
        manageAgentBonusData();
    });
});

$('body').on(
    'click',
    '.modifyAgentCommission',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        if (primaryID > 0) {
            ajaxActionV2(
                'api/modify-agent-commission-data',
                'POST',
                { agent_commission_id: primaryID },
                function (response) {
                    let data = response.data[0];
                    var commissionRate = data.commission_value + '% + ' + data.commission_gst;
                    kendoWindowOpen(modifyAgentCommissionModalId);
                    let modifyAgentCommission = $(document).find(modifyAgentCommissionModalId);
                    //$(document).find(".agentTransaction").text(data.transaction_no);
                    $(document).find('.agencyName').text(data.agency_name);
                    $(document).find('.commissionRate').text(commissionRate);

                    let agentCommissionForm = $('#agentCommissionForm')
                        .html('')
                        .kendoForm({
                            validatable: defaultErrorTemplate(),
                            orientation: 'vertical',
                            layout: 'grid',
                            type: 'group',
                            grid: { cols: 12, gutter: 16 },
                            items: [
                                {
                                    field: 'commission_payable',
                                    label: 'Commission Payable',
                                    colSpan: 6,
                                    editor: customFloatNumberInput,
                                },
                                {
                                    field: 'gst_amount',
                                    label: 'GST',
                                    colSpan: 6,
                                    validation: { required: true },
                                    editor: customFloatNumberInput,
                                },
                                {
                                    field: 'commission_paid',
                                    label: 'Commission Paid',
                                    colSpan: 6,
                                    editor: customFloatNumberInput,
                                },
                                {
                                    field: 'payment_mode',
                                    editor: 'DropDownList',
                                    id: 'agent_commission_payment_mode',
                                    label: 'Payment Mode',
                                    colSpan: 6,
                                    validation: { required: true },
                                    editorOptions: {
                                        dataSource: getDropdownDataSource(
                                            'get-payment-mode-list',
                                            []
                                        ),
                                        dataValueField: 'Id', //Id
                                        dataTextField: 'Name',
                                        filter: 'contains',
                                        filterInput: {
                                            width: '100%',
                                        },
                                    },
                                },
                                {
                                    field: 'invoice_no',
                                    editor: customNumberInput,
                                    label: 'Invoice Number',
                                    colSpan: 6,
                                    //validation: { required: true },
                                },
                                {
                                    field: 'paid_date',
                                    label: 'Payment Date',
                                    editor: customDateEditor,
                                    dateFormat: dateFormatFrontSideJS,
                                    colSpan: 6,
                                    validation: { required: true },
                                },
                                {
                                    field: 'remarks',
                                    id: 'agent_commission_remarks',
                                    colSpan: 12,
                                    editor: 'TextArea',
                                    label: 'Remarks',
                                    validation: { required: true },
                                    editorOptions: {
                                        placeholder: 'Write Notes',
                                        rows: 5,
                                    },
                                },
                            ],
                            buttonsTemplate: '',
                            // submit: function (ev) {
                            //     // saveTrainingPlanData(tpAddFormId, tpAddModelId);
                            //     ev.preventDefault();
                            // }
                        });
                    agentCommissionForm.data('kendoForm').setOptions({
                        formData: {
                            commission_payable: data.commission_payable,
                            gst_amount: data.gst_amount,
                            commission_paid: data.commission_paid,
                            payment_mode: data.mode,
                            // invoice_no: data.invoice_no,
                            invoice_no: data.formatted_invoice_number,
                            paid_date: data.paid_date,
                            remarks: data.remarks,
                        },
                    });
                    agentCommissionForm.find("[name='commission_payable']").attr('disabled', true);
                    //agentCommissionForm.find("[name='commission_paid']").attr("disabled", true);
                    agentCommissionForm
                        .find("[name='commission_paid']")
                        .attr('disabled', isXeroConnectVal == 1);
                    agentCommissionForm.find("[name='invoice_no']").attr('disabled', true);

                    let agentBonusForm = $('#agentBonusForm')
                        .html('')
                        .kendoForm({
                            validatable: defaultErrorTemplate(),
                            orientation: 'vertical',
                            layout: 'grid',
                            type: 'group',
                            grid: { cols: 6, gutter: 16 },
                            items: [
                                {
                                    field: 'bonus_amount',
                                    editor: customFloatNumberInput,
                                    label: 'Bonus Amount',
                                    colSpan: 6,
                                },
                                {
                                    field: 'bonus_gst',
                                    editor: 'Switch',
                                    label: 'Add GST?',
                                    colSpan: 6,
                                },
                            ],
                            buttonsTemplate:
                                '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-4 bottom-0 right-0 fixed border-t bg-white px-6">\n' +
                                '<div class="float-right flex space-x-4 items-center justify-end"><input id="agent_commission_id" name="agent_commission_id" type="hidden" value=""/>\n' +
                                '<button type="button" class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn" type="button">\n' +
                                '<p type="button" class="text-sm font-medium leading-4 text-gray-700 cancelBtn">CANCEL</p>\n' +
                                '</button>\n' +
                                '<button type="button" class="updateAgentCommission flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
                                '<p class="text-sm font-medium leading-4 text-white">UPDATE</p>\n' +
                                '</button>\n' +
                                '</div>\n' +
                                '</div>',
                            submit: function (ev) {
                                ev.preventDefault();
                                //updateAgentCommission();
                            },
                        });
                    setTimeout(function () {
                        $('#agentBonusForm')
                            .find('.k-form-field-wrap')
                            .addClass('customSwitchButton');
                    }, 500);

                    agentBonusForm.data('kendoForm').setOptions({
                        formData: {
                            bonus_amount: data.bonus_amount,
                            bonus_paid_dt: data.paid_date,
                            bonus_gst: data.bonus_gst == 'GST' ? true : false,
                        },
                    });

                    modifyAgentCommission.find('#agent_commission_id').val(primaryID);
                }
            );
        }
    })
);

$('body').on('click', '.updateAgentCommission', function (e) {
    e.preventDefault();
    let payableAmount = parseFloat(
        $('#agentCommissionForm').find("[name='commission_payable']").val()
    );
    let paidAmount = parseFloat($('#agentCommissionForm').find("[name='commission_paid']").val());
    let gstAmount = parseFloat($('#agentCommissionForm').find("[name='gst_amount']").val());
    if (paidAmount > payableAmount + gstAmount) {
        notificationDisplay('Paid amount exceeds the limit.', '', 'error');
        return false;
    }
    let dataArr = formValidateAndReturnFormData(modifyAgentCommissionModalId);
    if (dataArr) {
        ajaxActionV2('api/save-agent-commission', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $(modifyAgentCommissionModalId).data('kendoWindow').close();
                refreshGrid2(agentCommissionGridId);
                refreshGrid2(agentBonusGridId);
                paymentsCardUpdate();
            }
        });
    }
});

$('body').on(
    'click',
    '.syncToXeroAgentCommissionBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        $(syncToXeroAgentCommissionModalId).data('kendoDialog').open();
        $(syncToXeroAgentCommissionModalId).find('#syncToXeroAgentCommissionId').val(primaryID);
    })
);

$('body').on(
    'click',
    '.syncFromXeroAgentCommissionBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        $(syncFromXeroAgentCommissionModalId).data('kendoDialog').open();
        $(syncFromXeroAgentCommissionModalId).find('#syncFromXeroAgentCommissionId').val(primaryID);
    })
);

$('body').on(
    'click',
    '.approveAgentCommissionBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        let invoiceNumber = $(e.currentTarget).attr('data-formatted-invoice');
        let isOvervalidity = $(e.currentTarget).attr('data-past-validity');
        let commDateRange = $(e.currentTarget).attr('data-commission-date-range');
        let agentCommId = $(e.currentTarget).attr('data-agent-commission-id');
        let editAgentCommissionUrl = site_url + 'edit-agent-commission/' + agentCommId;

        let $modal = $(approveAgentCommissionModalId);
        $modal.find('#approve_remarks').val('');
        $modal.find('#approveCommissionId').val(primaryID);
        $modal.find('#agentInvoiceNumber').html(invoiceNumber);

        let $warningContainer = $modal.find('#agentCommDateRangeWarningForSingle');
        if (isOvervalidity == 1) {
            $warningContainer
                .find('p')
                .text(`Agent commission valid date range : ${commDateRange}.`);
            $warningContainer.find('a').attr('href', editAgentCommissionUrl);
            $warningContainer.show();
        } else {
            $warningContainer.hide();
        }

        kendoWindowOpen(approveAgentCommissionModalId);
    })
);

$('body').on(
    'click',
    '.bulkApproveAgentCommissionBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let checkedValues = [];
        let checkedInvoice = [];
        let grid = $(agentCommissionGridId).data('kendoGrid');

        let isValidForApprove = false;
        let isOvervalidity = false;
        let commDateRange = '';
        let editAgentCommissionUrl = '#';

        grid.tbody.find("input[type='checkbox']:checked").each(function () {
            let dataItem = grid.dataItem($(this).closest('tr'));
            checkedValues.push(dataItem['id']);
            checkedInvoice.push(dataItem['formatted_invoice_number']);
            if (!isValidForApprove) {
                isValidForApprove = dataItem['is_approved'] == 1 ? true : false;
            }
            if (!isOvervalidity) {
                isOvervalidity = dataItem['is_payment_past_validity'] == 1 ? true : false;
                if (isOvervalidity) {
                    commDateRange = dataItem['comm_valid_range'];
                    editAgentCommissionUrl =
                        site_url + 'edit-agent-commission/' + dataItem['agent_commission_id'];
                }
            }
        });

        if (isValidForApprove) {
            notificationDisplay('Please select items that are not approved.', '', 'error');
            return false;
        }

        /*let checkedValues = grid.tbody .find("input[type='checkbox']:checked").map(function (){
        return grid.dataItem($(this).closest("tr"))['id];
    }).get();*/

        if (checkedValues.length == 0) {
            notificationDisplay('Please select at least one row', '', 'error');
            return false;
        }

        let $modal = $(bulkApproveAgentCommissionModalId);

        $modal.find('#approve_remarks').val('');
        $modal.find('#approveBulkCommissionIds').val(checkedValues);
        $modal.find('#bulkAgentInvoiceNumber').html(checkedInvoice.join('<br>'));

        let $warningContainer = $modal.find('#agentCommDateRangeWarningForBulk');
        if (isOvervalidity) {
            $warningContainer
                .find('p')
                .text(`Agent commission valid date range : ${commDateRange}.`);
            $warningContainer.find('a').attr('href', editAgentCommissionUrl);
            $warningContainer.show();
        } else {
            $warningContainer.hide();
        }

        kendoWindowOpen(bulkApproveAgentCommissionModalId);
    })
);

$('body').on(
    'click',
    '.createPOForAgentCommissionBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        $(createPOForAgentCommissionModalId).data('kendoDialog').open();
        $(createPOForAgentCommissionModalId).find('#poForAgentCommissionId').val(primaryID);
    })
);

$('body').on(
    'click',
    '.bulkPOCreateBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let closestGridId = $(e.currentTarget).attr('data-grid-id');
        let gridPrimaryId = $(e.currentTarget).attr('data-primary-id');
        let grid = $(`#${closestGridId}`).data('kendoGrid');
        let unapprovedFound = false;
        let checkedValues = grid.tbody
            .find("input[type='checkbox']:checked")
            .map(function () {
                let dataItem = grid.dataItem($(this).closest('tr'));
                if (dataItem.is_approved == 0) {
                    notificationDisplay('Please approve first', '', 'error');
                    unapprovedFound = true;
                    return false;
                }
                return dataItem[gridPrimaryId];
            })
            .get();

        if (unapprovedFound) {
            return false;
        }

        if (checkedValues.length == 0) {
            notificationDisplay('Please select at least one row', '', 'error');
            return false;
        }

        $(bulkCreatePOForAgentCommissionModalId).data('kendoDialog').open();
        $(bulkCreatePOForAgentCommissionModalId)
            .find('#poForAgentCommissionIds')
            .val(checkedValues);
        $(bulkCreatePOForAgentCommissionModalId)
            .find('#poForAgentCommissionIds')
            .attr('data-grid-id', closestGridId);
    })
);

$('body').on('click', '.saveApproveBtn', function (e) {
    e.preventDefault();
    /*let validator = defaultFormValidator(approveAgentCommissionModalId);
    if (!validator.validate()) {
        return false;
    }*/
    let dataArr = {
        id: $(approveAgentCommissionModalId).find('#approveCommissionId').val(),
        remarks: $(approveAgentCommissionModalId).find('#approve_remarks').val(),
        approveType: 'single',
    };
    approveAgentCommission(dataArr);
});

$('body').on('click', '.saveBulkApproveBtn', function (e) {
    e.preventDefault();
    /*let validator = defaultFormValidator(bulkApproveAgentCommissionModalId);
    if (!validator.validate()) {
        return false;
    }*/

    let agentCommPrimaryId = $(bulkApproveAgentCommissionModalId)
        .find('#approveBulkCommissionIds')
        .val();
    let dataArr = {
        ids: agentCommPrimaryId.split(','),
        remarks: $(bulkApproveAgentCommissionModalId).find('#approve_remarks').val(),
        approveType: 'bulk',
    };
    approveAgentCommission(dataArr);
});

$('body').on('click', '#exportAgentStudentData', function (e) {
    e.preventDefault();
    let agentId = $(this).attr('data-agent-id');
    if (agentId > 0) {
        let dataArr = {
            agent_id: agentId,
            stud_course_id: [selectedStudCourseID],
        };
        //let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
        //window.location.href = site_url + "api/export-agent-students/?data=" + encodedDataArr;

        ajaxActionV2('api/export-agent-students', 'POST', dataArr, function (response) {
            if (response.data.export_url) {
                //let decodedUrl = decodeURIComponent(response.data.export_url);
                let decodedUrl = atob(response.data.export_url);

                const link = document.createElement('a');
                link.setAttribute('href', decodedUrl);
                link.setAttribute('download', response.data.file_name);
                link.click();
            } else {
                notificationDisplay(response.message, '', response.status);
                //notificationDisplay("Something Will be Wrong. Please Try Again.", "", "error");
            }
        });
    } else {
        notificationDisplay('Agent not found', '', 'error');
    }
});

$('body').on('click', '.showStudentAgentCommInfo', function (e) {
    e.preventDefault();
    let primaryId = $(this).attr('data-student-agent-commission-id');
    $(viewPaymentInfoModalId).data('kendoWindow').close();

    if (primaryId > 0) {
        let dataArr = {
            college_id: collegeId,
            student_id: studentId,
            student_course_id: selectedStudCourseID,
            primary_id: primaryId,
        };
        ajaxActionV2(
            'api/get-agent-commission-data',
            'POST',
            dataArr,
            handleAgentCommissionResponse
        );
    }
});

function handleAgentCommissionResponse(response) {
    if (response.status === 'success') {
        const dataItem = {
            ...response.data.data[0],
            xero_connect: isXeroConnectVal,
            is_agent_sync: isAgentSync,
            agent_comm_sync_option: agentSyncOption,
        };
        viewAgentCommission(dataItem);
    } else {
        notificationDisplay(response.message, '', response.status);
    }
}
