/* bottom actionbar */

.bottomaction {
    position: sticky;
    bottom: 0px;
    z-index: 1000;
    /* overflow-y: auto; */
    width: 100%;
    left: 0;
    box-shadow:
        0px -4px 6px -1px rgba(0, 0, 0, 0.1),
        0px -2px 4px -1px rgba(0, 0, 0, 0.06);
}
#student_id.k-input {
    border-width: 1px;
}
.heightzero {
    overflow: hidden;
    height: 0px;
}

/* kendo window */
.titlebar-sms-modal .k-window-action {
    opacity: 1;
}

/* .k-window-titlebar {
    background-color: rgba(24, 144, 255, 1) !important;
} */

#sendsmsto {
    min-height: 35px;
}

.titlebar-sms-modal .k-window-action .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

.k-widget.k-window.k-display-inline-flex.blur-modal {
    z-index: 9999;
}

/* filter popup */

/* ====== STUDENT FILTER RADIO ====== */

/* ==== select box === */

/* #filterStudentForm .k-dropdown-wrap .k-input:before {
        content: '\f078';
        display: inline-block;
        position: absolute;
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        right: 40px;
        color: #9CA3AF;
    } */

/* === radio button === */

#filterStudentForm #distribution_type-form-label,
#filterStudentForm #campus-form-label,
#filterStudentForm #course_type-form-label,
#filterStudentForm .k-label.k-form-label {
    color: rgba(55, 65, 81, 1);
    font-weight: 500;
    font-size: 14px !important;
    margin-bottom: 10px;
}

#distribution_type .k-radio-item .k-radio[type="radio"]:checked,
#distribution_type .k-radio-item .k-radio[type="radio"]:hover {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    background-image: none !important;
    background-color: rgba(230, 247, 255, 1) !important;
}

#recurring_days .k-radio-item .k-radio[type="radio"]:checked,
#recurring_days .k-radio-item .k-radio[type="radio"]:hover {
    background-color: #1890ff !important;
    color: #fff;
}

#filterStudentForm #distribution_type .k-radio-item {
    position: relative !important;
    /* width: 150px !important; */
    /* height: 70px !important; */
    padding: 33px;
    margin: 0px !important;
    border: 0px !important;
}

#distribution_type.k-radio-list.k-list-horizontal {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}

#filterStudentForm #distribution_type .k-list-horizontal .k-radio-item {
    margin: 0px !important;
}

#filterStudentForm #distribution_type .k-radio-item .k-radio {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    border: 0px;
    background: transparent;
    left: 0;
    right: 0;
    top: 0;
    border-radius: 0.5rem;
    box-sizing: border-box !important;
    border: 1px solid rgba(209, 213, 219, 1) !important;
    margin: 0;
}

#filterStudentForm
    #distribution_type
    .k-radio-item
    .k-radio[type="radio"]:checked {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    background-color: rgba(230, 247, 255, 1);
    border-width: 2px !important;
}

#filterStudentForm
    #distribution_type
    .k-radio-item
    .k-radio[type="radio"]:checked::before {
    display: none !important;
}

#filterStudentForm #distribution_type .k-radio-item .k-radio-label {
    position: absolute;
    right: 0;
    left: 0;
    margin: 0 auto;
    top: 50%;
    text-align: center;
    transform: translate(0, -50%);
}

/* === course type ==== */

/* #filterStudentForm #course_type .k-radio-item .k-radio:checked:focus {
    box-shadow: 0 0 0 2px #2563eb40;
}

#filterStudentForm #course_type .k-radio-item .k-radio {
    margin-right: 5px !important;
}

#filterStudentForm #course_type .k-radio-item .k-radio[type='radio']:checked {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    background-image: none !important;
    background-color: #2563eb !important;
} */

#filterStudentForm #course_type .k-radio-item .k-radio::before {
    width: 7px;
    height: 7px;
}

#filterStudentForm #course_type .k-radio-item .k-radio-label {
    color: #111 !important;
}

#filterStudentForm .k-form-buttons {
    display: flex;
    flex-direction: row-reverse;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    width: 100%;
    margin-left: -1.5rem;
    position: absolute;
    bottom: 0;
    border-width: 0;
    box-shadow: 0px 1px 0px 0px #e5e7eb inset;
    background-color: white;
    margin-top: 1rem;
    padding: 1rem 1.5rem;
}

#filterStudentForm .k-form-buttons button.k-form-submit {
    background-color: rgba(24, 144, 255, 1) !important;
    color: #fff !important;
}

#filterStudentForm .k-form-buttons button.k-form-submit.k-form-clear {
    color: rgba(55, 65, 81, 1);
}

/* ========== */

.student-li-detail.k-tabstrip-content.k-content.k-state-active:focus,
.staff-li-detail.k-tabstrip-content.k-content.k-state-active:focus {
    border: 0px !important;
    outline: 0px !important;
}

/* ====== INPUT TAG ====== */

/* #filterStudentForm .k-input {
    font-size: 16px !important;
    line-height: 24px !important;
    border-radius: 5px;
    box-shadow: none;
    font-weight: 400 !important;
    border-color: #dfe3e9 !important;
    color: #354052 !important;
    height: 41px !important;
    border-radius: 4px !important;
    background-color: #fff;
    border: 1px solid #ccc;
} */

#filterStudentForm .k-input:focus {
    border-color: #2ea2f8 !important;
    box-shadow: none;
    color: #354052 !important;
}

/* / */

#fromdate-form-label,
#todate-form-label {
    min-height: 25px;
}

/* design tabstrip */

.k-content {
    border-top-right-radius: 8px !important;
    border-top-left-radius: 8px !important;
    padding-top: 1rem !important;
}

/* list view/ grid view */

#recentstudentListView,
#allstudentListView,
#recentstaffListView,
#allstaffListView {
    background-color: transparent !important;
}

.k-listview-bordered {
    border-width: 0px;
}

.k-listview-item {
    padding: 16px;
}

/* ====== FORM WIZARD STEPS  NEWS AND REMINDER===== */

#createNewsReminderModal {
    background: #f1f5f9;
}

#newsReminderAddForm {
    background: #fff;
}

.k-wizard {
    padding: 0px;
}

.k-wizard-horizontal .k-wizard-steps {
    margin-top: 0px;
    padding: 0px 25px;
}

.k-progressbar.k-progressbar-horizontal {
    display: none;
}

.k-stepper .k-step-list {
    border-bottom: 1px solid rgba(209, 213, 219, 1);
}

.k-step-list-horizontal .k-step-link {
    margin: initial;
    max-width: 100%;
    flex-direction: row;
}

.k-step-list-horizontal .k-step {
    padding: 15px 15px;
    position: relative;
}

.k-step-list-horizontal .k-step::after {
    content: "";
    width: 1px;
    height: 45px;
    background-color: #ccc;
    position: absolute;
    right: 46px;
    top: 0px;
    transform: rotate(-35deg);
}

.k-step-list-horizontal .k-step::before {
    content: "";
    width: 1px;
    height: 45px;
    background-color: #ccc;
    position: absolute;
    right: 46px;
    bottom: 0px;
    transform: rotate(35deg);
}

.k-step-list-horizontal .k-step:last-child:before,
.k-step-list-horizontal .k-step:last-child:after {
    display: none;
}

.k-stepper .k-step-indicator {
    width: 45px;
    height: 45px;
    margin-right: 15px;
}

.k-stepper .k-step-current .k-step-indicator {
    border-color: rgba(24, 144, 255, 1);
    color: rgba(24, 144, 255, 1);
    background-color: transparent;
    /* background-color: rgba(24, 144, 255, 1); */
}
.k-stepper .k-step-label,
.k-stepper .k-step-current .k-step-label,
.k-stepper .k-step-hover .k-step-label,
.k-stepper .k-step:hover .k-step-label {
    font-weight: 500;
}

.k-stepper .k-step-current .k-step-label {
    color: rgba(24, 144, 255, 1);
}

.k-stepper .k-step-done .k-step-indicator {
    border-color: transparent;
    color: #fff;
    background-color: rgba(24, 144, 255, 1);
}

.k-stepper .k-step-focus .k-step-indicator::after {
    display: none;
}

.k-stepper .k-step-focus .k-step-indicator::after,
.k-stepper .k-step-link:focus .k-step-indicator::after {
    display: none;
}

.k-stepper
    .k-step-done
    .k-step-indicator
    .k-stepper
    .k-step-current.k-step-hover
    .k-step-indicator,
.k-stepper .k-step-current:hover .k-step-indicator {
    background-color: transparent;
}

.k-stepper .k-step-done .k-step-link .k-step-text {
    color: #111827;
    font-weight: 500;
}

.k-stepper .k-step-done.k-step-hover .k-step-indicator,
.k-stepper .k-step-done:hover .k-step-indicator {
    background-color: rgba(24, 144, 255, 1);
}

#assign_access_to {
    gap: 0px;
    border: 1px solid #dfe3e9;
    padding: 0;
    display: inline-block;
    border-radius: 5px;
}

#assign_access_to .k-radio-item {
    margin: 0px;
    position: relative;
    padding: 0px;
}

#assign_access_to .k-radio-item .k-radio {
    width: 100px;
    border-radius: 0px;
    height: 35px;
    position: relative;
    border-radius: 0px !important;
    margin: 0px !important;
    border: 0px;
    border-right: 1px solid #dfe3e9 !important;
}

#assign_access_to .k-radio-item:first-child .k-radio {
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
}

#assign_access_to .k-radio-item:last-child .k-radio {
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
    border-right: 0px !important;
}

#assign_access_to .k-radio-item .k-radio-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#assign_access_to .k-radio-item .k-radio::before {
    display: none;
}

#assign_access_to .k-radio-item .k-radio[type="radio"]:checked + label {
    color: #fff !important;
}

#assign_access_to .k-radio-item .k-radio[type="radio"]:checked {
    background-color: rgba(24, 144, 255, 1) !important;
    background-image: none !important;
    color: #fff !important;
}

#assign_access_to .k-radio-item .k-radio[type="radio"]:checked:focus {
    outline: none !important;
}

#newsReminderAddForm .k-wizard-content {
    margin-bottom: 3.5rem;
}

#newsReminderAddForm .k-wizard-buttons {
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
    right: 0;
    padding: 15px 35px;
    background: rgba(249, 250, 251, 1);
}

#recurring_days .k-radio-item {
    margin: 0 !important;
    position: relative;
}

#recurring_days .k-radio {
    width: 35px;
    height: 35px;
    background: var(--color-gray-300);
    margin-right: 0px !important;
}

#recurring_days .k-radio-item .k-radio-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(107, 114, 128, 1);
    font-size: 12px;
}

#recurring_days .k-radio-item .k-radio[type="radio"]:checked {
    border-radius: 50% !important;
}

#recurring_days .k-radio-item .k-radio[type="radio"]:checked:focus {
    outline: none !important;
}

#recurring_days .k-radio-item .k-radio[type="radio"]:checked ~ label {
    color: white !important;
}

#recurring_days .k-radio-item .k-radio::before {
    display: none;
}

.k-switch.k-widget.k-switch-on .k-switch-container {
    background-color: #1890ff !important;
}

.k-switch.k-widget.k-switch-off .k-switch-container {
    background-color: rgba(209, 213, 219, 1) !important;
}

.k-switch.k-widget .k-switch-label-on,
.k-switch.k-widget .k-switch-label-off {
    display: none;
}

.k-switch-handle {
    background-color: #fff;
}

/* ======== COMMON CSS =====  */

.k-widget * {
    box-sizing: border-box !important;
}

.k-label.k-form-label {
    color: rgba(55, 65, 81, 1);
    font-weight: 500;
    font-size: 14px !important;
    margin-bottom: 10px;
}

.k-input[type="text"]:focus {
    border-color: #2ea2f8 !important;
    box-shadow: none;
    color: #354052 !important;
}

/* .k-form-field-wrap input[type="text"] {
    font-size: 16px !important;
    line-height: 24px !important;
    border-radius: 5px;
    box-shadow: none;
    font-weight: 400 !important;
    border-color: #dfe3e9 !important;
    color: #354052 !important;
    height: 41px !important;
    border-radius: 4px !important;
    background-color: #fff;
} */

.k-form-field-wrap input[type="text"]:focus {
    border-color: #2ea2f8 !important;
    box-shadow: none;
    color: #354052 !important;
}

.k-radio-item .k-radio[type="radio"]:checked {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    background-image: none !important;
    background-color: rgba(24, 144, 255, 1) !important;
}

.k-radio-item .k-radio[type="radio"]:checked:focus {
    border-color: transparent;
    background-color: currentColor;
}

.k-radio-item .k-radio {
    margin-right: 5px !important;
}

.k-radio-item .k-radio::before {
    width: 7px;
    height: 7px;
}

.k-radio.k-checked.k-state-focus,
.k-radio:checked:focus {
    box-shadow: none;
}

.k-input:focus-within {
    box-shadow: none;
}

.k-wizard .k-wizard-step.k-state-focused,
.k-wizard .k-wizard-step:focus {
    outline: none !important;
    /* outline-width: 1px;
    outline-style: dotted;
    outline-offset: -1px; */
}

/* ===== table NEWS AND REMINDER ==== */

/* ====== ACTION ICON ===== */

.icon-actions-set .cursor-pointer.k-icon {
    width: 30px;
    height: 30px;
    margin: 0 2px;
    border-radius: 50%;
    font-size: 14px;
    border: 1px solid #dfe2e5;
    color: #6f8199;
}

.icon-actions-set .cursor-pointer.k-icon:hover {
    color: #fff;
    background: #1890ff;
}

.k-notification {
    border-radius: 8px;
    padding: 0px 0px;
    border-width: 0px;
}
.k-state-active svg g {
    fill: #1890ff;
}

.sms-sidebar-menu svg.text-primary-blue-500 {
    stroke: #1890ff;
}
svg g {
    fill: #6f8199;
}
.k-list-scroller .k-state-selected {
    color: #fff;
}

#userList.k-grid .k-grid-header .k-header {
    background: #f3f4f6 !important;
    height: 30px;
}
.k-picker-wrap {
    border-radius: 0.5rem !important;
}

/* Tagify css */
.tagify__tag {
    border: 1px solid #ddd !important;
    border-radius: 30px !important;
}
.tagify__tag__removeBtn {
    font: 20px Arial !important;
    color: #9ca3af !important;
}
.tag-div {
    border-radius: 30px !important;
}
.tagify {
    --tag-bg: none;
    --tag-hover: #bae7ff !important;
    --tags-disabled-bg: #fff !important;
    --tags-border-color: #ddd !important;
    --tags-hover-border-color: #ddd !important;
    --tag-remove-btn-bg--hover: none !important;
    --tag-remove-btn-color: black;
    --tag-remove-bg: none !important;
}
.tagify {
    border: none !important;
}

#allStudentListView.k-listview,
#allstaffListView.k-listview .k-listview-content,
#recentstudentListView.k-listview .k-listview-content,
#recentstaffListView.k-listview .k-listview-content {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1rem;
}

#newsReminderAddForm-1 .k-form {
    width: 60%;
}

#newsReminderAddForm-1 .k-form .k-d-grid {
    column-gap: 1rem;
}
