<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head id="Head1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>Student ID Card</title>
        <style>
            @page {
                size: 3.375in 2.125in;
                margin: 0;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, Helvetica, sans-serif;
                font-size: 10px;
                width: 3.375in;
                height: 2.125in;
                background: {{ $settings['secondary_color'] ?? '#ffffff' }};
                position: relative;
                overflow: hidden;
            }
            
            .card-container {
                width: 100%;
                height: 100%;
                position: relative;
                border: 2px solid {{ $settings['primary_color'] ?? '#0161a3' }};
                box-sizing: border-box;
                @if(isset($settings['background_image']) && $settings['background_image'])
                background-image: url('{{ asset($settings['background_image']) }}');
                background-size: cover;
                background-position: center;
                @endif
            }
            
            .header {
                background: {{ $settings['primary_color'] ?? '#0161a3' }};
                color: white;
                padding: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                @if($settings['card_orientation'] === 'portrait')
                height: 40px;
                @else
                height: 25px;
                @endif
            }
            
            .content {
                padding: 10px;
                display: flex;
                @if($settings['card_orientation'] === 'portrait')
                flex-direction: column;
                height: calc(100% - 60px);
                @else
                flex-direction: row;
                height: calc(100% - 45px);
                @endif
            }
            
            .photo-section {
                @if($settings['card_orientation'] === 'portrait')
                text-align: center;
                margin-bottom: 10px;
                @else
                flex: 0 0 80px;
                margin-right: 10px;
                @endif
            }
            
            .photo {
                @if($settings['card_orientation'] === 'portrait')
                width: 80px;
                height: 80px;
                @else
                width: 70px;
                height: 70px;
                @endif
                border: 2px solid {{ $settings['primary_color'] ?? '#0161a3' }};
                border-radius: 4px;
                object-fit: cover;
                display: block;
                @if($settings['card_orientation'] === 'portrait')
                margin: 0 auto;
                @endif
            }
            
            .info-section {
                @if($settings['card_orientation'] === 'portrait')
                flex: 1;
                @else
                flex: 1;
                @endif
                font-size: 9px;
                line-height: 1.3;
            }
            
            .student-name {
                font-weight: bold;
                font-size: 11px;
                color: {{ $settings['primary_color'] ?? '#0161a3' }};
                margin-bottom: 5px;
                text-transform: uppercase;
            }
            
            .info-row {
                margin-bottom: 3px;
                display: flex;
                justify-content: space-between;
            }
            
            .label {
                font-weight: bold;
                color: #333;
                min-width: 60px;
            }
            
            .value {
                color: #666;
                text-align: right;
                flex: 1;
            }
            
            .logo {
                position: absolute;
                @if(($settings['logo_position'] ?? 'top-left') === 'top-left')
                top: 5px;
                left: 5px;
                @elseif($settings['logo_position'] === 'top-right')
                top: 5px;
                right: 5px;
                @elseif($settings['logo_position'] === 'top-center')
                top: 5px;
                left: 50%;
                transform: translateX(-50%);
                @elseif($settings['logo_position'] === 'bottom-left')
                bottom: 5px;
                left: 5px;
                @elseif($settings['logo_position'] === 'bottom-right')
                bottom: 5px;
                right: 5px;
                @endif
                width: 40px;
                height: auto;
                z-index: 10;
            }
            
            .footer {
                position: absolute;
                bottom: 5px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 8px;
                color: {{ $settings['primary_color'] ?? '#0161a3' }};
                text-align: center;
            }
            
            /* Default template specific styling */
            @if(($settings['card_template'] ?? 'default') === 'default')
            .default-branding {
                background: linear-gradient(135deg, {{ $settings['primary_color'] ?? '#0161a3' }} 0%, #004080 100%);
            }

            .default-accent {
                border-left: 3px solid #ff6b35;
                padding-left: 8px;
            }
            @endif
            
            /* Custom CSS */
            @if(isset($settings['custom_css']))
            {{ $settings['custom_css'] }}
            @endif
        </style>
    </head>
    <body>
        <div class="card-container">
            @if(isset($clg_logo) && $clg_logo)
            <img class="logo" src="{{ asset($clg_logo) }}" alt="College Logo" />
            @endif
            
            <div class="header {{ ($settings['card_template'] ?? 'default') === 'default' ? 'default-branding' : '' }}">
                {{ $arrStudentInfo->college_name ?? 'Student ID Card' }}
            </div>
            
            <div class="content">
                <div class="photo-section">
                    <img class="photo" src="{{ asset($profile_pic) }}" alt="Student Photo" />
                </div>
                
                <div class="info-section">
                    <div class="student-name {{ ($settings['card_template'] ?? 'default') === 'default' ? 'default-accent' : '' }}">
                        {{ trim(($arrStudentInfo->name_title ?? '') . ' ' . ($arrStudentInfo->first_name ?? '') . ' ' . ($arrStudentInfo->middel_name ?? '') . ' ' . ($arrStudentInfo->family_name ?? '')) }}
                    </div>
                    
                    @if(($settings['field_settings']['show_student_number'] ?? true) && !empty($arrStudentInfo->generated_stud_id))
                    <div class="info-row">
                        <span class="label">Student ID:</span>
                        <span class="value">{{ $arrStudentInfo->generated_stud_id }}</span>
                    </div>
                    @endif
                    
                    @if(($settings['field_settings']['show_dob'] ?? true) && !empty($arrStudentInfo->DOB))
                    <div class="info-row">
                        <span class="label">DOB:</span>
                        <span class="value">{{ date('d/m/Y', strtotime($arrStudentInfo->DOB)) }}</span>
                    </div>
                    @endif
                    
                    @if(($settings['field_settings']['show_course'] ?? true) && !empty($arrStudentInfo->course_name))
                    <div class="info-row">
                        <span class="label">Course:</span>
                        <span class="value">{{ $arrStudentInfo->course_name }}</span>
                    </div>
                    @endif
                    
                    @if(($settings['field_settings']['show_campus'] ?? true) && !empty($arrStudentInfo->campus_name))
                    <div class="info-row">
                        <span class="label">Campus:</span>
                        <span class="value">{{ $arrStudentInfo->campus_name }}</span>
                    </div>
                    @endif
                    
                    @if(($settings['field_settings']['show_issue_date'] ?? true))
                    <div class="info-row">
                        <span class="label">Issued:</span>
                        <span class="value">{{ date('d/m/Y') }}</span>
                    </div>
                    @endif
                    
                    @if(($settings['show_expiry_date'] ?? true) && ($settings['field_settings']['show_expiry_date'] ?? true))
                    <div class="info-row">
                        <span class="label">Expires:</span>
                        <span class="value">{{ date('d/m/Y', strtotime('+' . ($settings['expiry_years'] ?? 2) . ' years')) }}</span>
                    </div>
                    @endif
                </div>
            </div>
            
            @if(($settings['card_template'] ?? 'default') === 'default')
            <div class="footer">
                Student ID Card
            </div>
            @endif
        </div>
    </body>
</html>
