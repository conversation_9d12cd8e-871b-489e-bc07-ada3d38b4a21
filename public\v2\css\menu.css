* {
	box-sizing: border-box;
}
ul,
li {
	list-style: none;
}
a {
	text-decoration: none;
	color: black;
}

.k-widget * {
	/* box-sizing: border-box !important; */
}

/* modal radius */
/* .k-window{
    border-radius: 8px !important;
}
.k-window-titlebar{
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
} */

/* #searchtabstrip.k-tabstrip-top
	> .k-tabstrip-items-wrapper
	.k-item.k-state-active {
	border-bottom: 3px solid #1890ff !important;
} */

#searchtabstrip .k-tabstrip-items .k-link {
	padding: 16px 20px !important;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
div,
button,
ul,
li {
	padding: 0;
	margin: 0;
}
/* #searchTabStrip.k-tabstrip-top>.k-tabstrip-items-wrapper .k-item.k-state-active {
    border-bottom: 3px solid #1890FF !important;
} */

#searchTabStrip .k-tabstrip-items .k-link {
    padding: 16px 20px !important;
}

body,h1,h2,h3,h4,h5,h6,p,div,button,ul,li{
    padding: 0;
    margin: 0;
}
.sidebar{
    width: 250px;
    padding: 8px;
    -moz-transition: width 0.5s ease-out;
    -o-transition: width 0.5s ease-out;
    -webkit-transition: width 0.5s ease-out;
    transition: width 0.5s ease-out;
    display: inline-block;
    z-index: 100;
    /* background: #fff; */
    /* background: #1F2937; */
}

/* .main_wrapper{
    width: calc(100% - 250px);
    padding-left:15px;
    height: 100vh;
} */
/* header{
    width: 100%;
    box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
    background: #fff;
    border: 1px solid;
} */
/* .main-wrap{
    width: 100%;
    height: 100vh;
    border: 1px solid;
    box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
    height: calc(100vh - 74px);
} */
main {
	width: 100%;
}
.sidebar-wrapper .logo {
	width: 200px;
	height: 50px;
}
.sidebar-wrapper .logo img {
	width: 100%;
	object-fit: contain;
}
.sidebar-wrapper .btn {
	border: 1px solid;
	background: #0784ae;
	border: none;
	color: white;
}
.sidebar-wrapper .btn:hover {
	background: #9edff5;
}

.search-box {
	position: relative;
}
.search-box i {
	position: absolute;
	left: 6px;
	top: 8px;
}
.search-box input {
	padding-left: 25px;
}
.btn-slide .sidebar {
	/* transform: scaleX(calc(80/250)); */
	/* transition: transform 2s; */
	/* width: 80px; */
	/* transition: width 0.3s ease-out; */
}
.btn-slide .main_wrapper {
	/* width: calc(100% - 80px); */
	/* transition: width 0.5s; */
}
.btn-slide .logo-img,
.btn-slide .menuText,
.btn-slide .activeText {
	display: none;
}
.selector-item_radio {
	appearance: none;
	display: none;
}

/* .dark-mode .header,
.dark-mode .sidebar,
.dark-mode .main-wrap
{
    background-color: #283142;
} */
.dark-mode .nav-link,
.dark-mode .menuText,
.dark-mode .aside-menu i {
	/* color: white; */
}
.aside-menu {
	padding: 15px 0;
}
.dark-mode .root {
	background: #000;
}

#verticalMenu {
	opacity: 1;
}

/* ============================== */

.logo-wrapper {
	display: flex;
	align-items: center;
	width: 100%;
	justify-content: space-between;
	padding: 0 0.8rem;
	height: 40px;
	position: relative;
	transition: 0.5s;
	margin-top: 0.9rem;
	margin-bottom: 0.9rem;
}
.logo-wrapper img {
	width: 100%;
	max-width: 150px;
}
.btn-slide .logo-wrapper button {
	transition: 0.5s linear;
	transform: scaleX(1);
}

.k-menu-scroll-wrapper.vertical .menu-list {
	width: 230px !important;
	/* transition: 0.5s linear; */
	margin-top: 0px;
}

.aside_wrap .k-menu-scroll-wrapper {
	background-color: transparent !important;
	width: auto !important;
}

.menu-list li a {
	/* padding: 0px !important; */
	display: flex !important;
	justify-content: space-between !important;
}

.logo-wrapper a {
	margin-left: 0;
	width: 100% !important;
	transition: 0.5s;
}
.logo-wrapper a svg {
	width: 90% !important;
	transition: 0.5s;
}
.profileBox {
	/* width: auto; */
	transition: 0.5s;
	position: relative;
	/* z-index: 9999; */
	/* background-color: aqua; */
	/* left: 0; */
	/* bottom:0px; */
	border: 0;
	margin-top: auto;
	/* margin-bottom:5px; */
	width: 100%;
}
/* .aside-menu{
    padding:0px 11px !important;
    
}
.sidebar-menu > li{
    padding:0px 11px !important;
} */

.menu-list li {
	/* padding: 7px 11px !important; */
}
/* .menu-list li a {
    justify-content: center !important ;
} */

.menu-list.k-menu-vertical > .k-menu-item > .k-menu-link span.icon-wrapper {
	/* width: 18px;
   display: inline-block;
   margin-right: 13px; */
}
.menu-list span.icon-wrapper {
	/* width: 18px; */
	/* display: inline-block; */
	/* margin-right: 13px; */
}

.menu-list.k-menu-vertical > .k-menu-item > .k-menu-link span.icon-wrapper {
	/*color: #9CA3AF;*/
}

.menu-list li.k-menu-item {
	/* padding: 11px !important; */
}

.menu-list li.k-menu-item .menuText {
	/*color: #4B5563;*/
}

.search-bar-wrap p {
	margin-bottom: 0px !important;
	margin-left: 15px !important;
}

/* ===============  BUTTON SLIDE  ================= */
.btn-slide .logo-wrapper {
	justify-content: center;
}

.btn-slide .serachBox {
	/* margin-top: 35px;
	margin-bottom: 35px; */
}
.btn-slide .logo-wrapper a {
	display: none;
}
.btn-slide .logo-wrapper button {
	transform: scaleX(-1);
}
.btn-slide .k-menu-scroll-wrapper.vertical {
	width: 100% !important;
}

.btn-slide .k-menu-scroll-wrapper.vertical .menu-list {
	width: 100% !important;
}

.btn-slide .arrow-wrap {
	display: none;
}
.btn-slide .menu-list.k-menu-vertical > .k-menu-item > .k-menu-link {
	justify-content: center;
}

.btn-slide .menu-list li.k-menu-item {
	/* padding: 11px 0px !important; */
}

.btn-slide
	.menu-list.k-menu-vertical
	> .k-menu-item
	> .k-menu-link
	span.icon-wrapper {
	margin-right: 0px !important;
}

/* vrutang */
.k-menu-group,
.k-menu.k-context-menu {
	border-color: rgba(0, 0, 0, 0.08);
	color: #d1d5db;
	margin: 0px 20px !important;
	border-radius: 6px;
}

#verticalMenu .fa-angle-right {
	color: #d1d5db;
}

/* Tooltip Start*/

.tooltip2 .tooltiptext {
	visibility: hidden;
	display: block;
	width: auto;
	margin: -30px 64px;
	font-size: 14px !important;
	/* background-color: black; */
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding: 8px 10px;

	/* Position the tooltip */
	position: fixed;
	z-index: 1;
}

.btn-slide .tooltip2 .tooltiptext::after {
	content: " ";
	position: absolute;
	top: 50%;
	right: 100%; /* To the left of the tooltip */
	margin-top: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: transparent black transparent transparent;
}

.btn-slide .tooltip2:hover .tooltiptext {
	visibility: visible;
	float: none;
	border-width: 0;
}

/* scroll */

#verticalMenu {
	/* height: calc(100vh - 180px);
	-moz-transition: width 0.5s ease-out;
	-o-transition: width 0.5s ease-out;
	-webkit-transition: width 0.5s ease-out;
	transition: width 0.5s ease-out;
	width: auto !important; */
	/*max-height: 540px;*/
}
.display-none {
	display: none;
}

/* .li-tooltip{
   background-color: #111823 !important;
    cursor: default !important;
} */
/* .li-tooltip span {
    color: #fff !important;
    cursor: default !important;
}

.sidebar-menu > li > a{
    padding: 0rem !important;
    
} */

/* .k-menu .k-item>.k-link, .k-menu-scroll-wrapper .k-item>.k-link, .k-popups-wrapper .k-item>.k-link{
    line-height: 0px !important;
} */

/* .menuText , .activeText, .subMenuText{ 
    font-size: 14px !important;
}

.btn-slide .menu-list li a {
    justify-content: center !important ;
    align-items: center !important;
} 

.btn-slide .search-bar-wrap{
justify-content: center !important;
}

.treeview-menu {
    width: 270px;
} */

.profileMenuClass {
	position: absolute;
	bottom: 64px;
	/* margin-left: 242px; */
}

.profileMenuClass .iradio_flat-green {
	display: none;
}

#findStudents {
	padding-left: 45px;
}

/* redio button */
.sidebarTheme_radio {
	appearance: none;
	display: none;
}

.sidebarTheme_radio:checked + .sidebarTheme_label {
	border: 1px solid #9ca3af;
}

.iradio_flat-green.checked + .sidebarTheme_label {
	border: 1px solid #9ca3af;
}

/* theme color implementation */
.darktheme,
.darktheme .k-menu-group {
	background: #1f2937 !important;
}
.lighttheme,
.lighttheme .k-menu-group {
	background: #ffffff !important;
}

.darktheme .menuText,
.darktheme .subMenuText {
	color: #d1d5db;
}

.lighttheme .menuText,
.ighttheme .subMenuText {
	color: #374151;
}

.darktheme .profileBox {
	background-color: #374151;
}

.lighttheme .profileBox {
	background-color: #f3f4f6;
}

.darktheme .activeText,
.lighttheme .activeText {
	color: #ffffff;
}

/* .lighttheme .activeText {
    color: #D1D5DB;
} */

.darktheme .activeSubmenu,
.darktheme .k-menu-scroll-button {
	background-color: #374151 !important;
	cursor: pointer;
}

.lighttheme .active-li:hover,
.lighttheme .activeSubmenu,
.lighttheme .k-menu-scroll-button {
	background-color: #f3f4f6 !important;
	cursor: pointer;
}

.darktheme .li-tooltip,
.darktheme .tooltip2 .tooltiptext {
	background-color: #111823;
}
.darktheme .tooltip2 .tooltiptext::after {
	border-color: transparent black transparent transparent;
}

.lighttheme .li-tooltip,
.lighttheme .tooltip2 .tooltiptext {
	background-color: #5db1ff;
}
.lighttheme .tooltip2 .tooltiptext::after {
	border-color: transparent #5db1ff transparent transparent;
}

#verticalMenu .fa-angle-right {
	color: #d1d5db;
}

.darktheme .fa-angle-right,
.darktheme .k-i-more-horizontal,
.darktheme .k-i-arrow-60-up,
.darktheme .k-i-arrow-60-down {
	color: #d1d5db !important;
}

.lighttheme .fa-angle-right,
.lighttheme .k-i-more-horizontal,
.lighttheme .k-i-arrow-60-up,
.lighttheme .k-i-arrow-60-down {
	color: #4b5563 !important;
}

.darktheme .logo-wrapper a .lightSvg {
	display: none;
}

.lighttheme .logo-wrapper a .darkSvg {
	display: none;
}

/* .k-i-arrow-60-up , .k-i-arrow-60-down {
    color: #fff !important;
}
.k-menu-scroll-button {
    background-color: #374151 !important;
} */
/* li .newicon{
	fill: #9ca3af;
}
li.activeMainMenu  .newicon{
fill: white;
} */