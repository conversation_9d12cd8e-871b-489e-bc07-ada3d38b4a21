<ul>
    @foreach ($formCollection->groups() as $group => $items)
        @php
            $groupSummary = $formCollection->summary($group);
        @endphp
        <li class="mb-4 rounded-md bg-white px-5 py-4 shadow-md text-gray-900 text-base"
            x-data="{ expanded: {{ $loop->first ? 'true' : 'true' }} }">
            <div class="flex items-center">

                <span
                      class="{{ @config('onboarding.forms.' . $group . '.bg') }} mr-2 flex h-[40px] w-[40px] items-center justify-center rounded-full">
                    {!! @config('onboarding.forms.' . $group . '.icon') !!}
                </span>

                <span class="font-base ml-2">{{ @config('onboarding.forms.' . $group . '.label') }}</span>

                <span class="ml-auto rounded-full bg-gray-100 px-3 py-1 text-xs">
                    {{ $groupSummary->completed }} of {{ $groupSummary->total }}
                </span>
                <span class="ml-2 flex h-[20px] w-[20px] cursor-pointer items-center"
                      x-on:click="expanded=!expanded;">
                    <i class="fa"
                       x-bind:class="{ 'fa-angle-right': !expanded, 'fa-angle-down': expanded }"></i>
                </span>


            </div>
            <ul class="ml-14 text-sm text-gray-700"
                x-show="expanded"
                
                x-collapse>
                @foreach ($items as $item)
                    <li class="mt-3 flex items-center gap-2">

                        <span
                              class="{{ $item->isCompleted() ? 'bg-blue-500 border-blue-300' : 'border-gray-300' }} flex h-[20px] w-[20px] shrink-0 items-center justify-center rounded-full border">
                            <svg width="12"
                                 height="10"
                                 viewBox="0 0 12 10"
                                 fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.259 0.991227C11.4934 1.22564 11.625 1.54352 11.625 1.87498C11.625 2.20643 11.4934 2.52432 11.259 2.75873L5.00901 9.00873C4.7746 9.24307 4.45672 9.37471 4.12526 9.37471C3.79381 9.37471 3.47592 9.24307 3.24152 9.00873L0.741515 6.50873C0.513817 6.27297 0.387824 5.95722 0.390672 5.62948C0.39352 5.30173 0.524981 4.98821 0.756742 4.75645C0.988502 4.52469 1.30202 4.39323 1.62976 4.39038C1.95751 4.38754 2.27326 4.51353 2.50901 4.74123L4.12526 6.35748L9.49152 0.991227C9.72592 0.756889 10.0438 0.625244 10.3753 0.625244C10.7067 0.625244 11.0246 0.756889 11.259 0.991227Z"
                                      fill="white" />
                            </svg>
                        </span>
                        <a href="{{ route($item->route) }}"
                           class="{{ $item->isCompleted() ? 'line-through ' : 'hover:underline' }} hover:text-blue-500 ">{{ $item->label }}</a>


                    </li>
                @endforeach
            </ul>
        </li>
    @endforeach
</ul>
