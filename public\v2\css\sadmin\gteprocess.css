input:checked ~ .dot {
    transform: translateX(100%);
}
#requestDocumentModal,
#approveOfferLetterModal,
#rejectOfferLetterModal,
#approveGteDocModal,
#rejectGteDocModal,
#approveEnrollDocModal,
#approveVisaDocModal,
#rejectEnrollDocModal,
#rejectVisaDocModal,
#approvePaymentDocModal,
#rejectPaymentDocModal {
    padding: 0px;
}

/* //action */
.sortable.open .dropdown-action.dm-toggle {
    top: inherit !important;
    left: inherit !important;
    display: block;
}

.sortable.open .dropdown-menu.dm-toggle {
    top: inherit !important;
    left: inherit !important;
    display: block;
}

.dropdown-action {
    position: absolute;
    top: 100%;
    left: -85px;
    z-index: 1000;
    display: none;
    float: right;
    min-width: 125px;
    padding: 2px 0;
    margin: -10px -132px 0px;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
    box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
}

.dropdown-reset-action {
    position: absolute;
    top: 100%;
    left: -85px;
    z-index: 1000;
    display: none;
    min-width: 150px;
    font-size: 14px;
    text-align: left;
    border-radius: 4px;
}

.sortable.open .dropdown-reset-action.dm-toggle {
    top: 100%;
    left: 0;
    display: block;
}

.titlebar-gte-modal {
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
}
.k-window {
    border-radius: 8px !important;
}
.k-widget * {
    box-sizing: border-box !important;
}
#uploaddoc .k-upload {
    width: 100%;
}
#uploaddoc .k-dropzone {
    background-color: #f9fafb;
    height: 200px;
    cursor: pointer;
}
#uploaddoc .k-upload-button {
    position: absolute;
    top: 0;
    left: 0;
    color: transparent;
    border: 0;
    background: transparent;
    cursor: pointer;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 22;
    border: 2px dashed #eaeaea;
}
#uploaddoc .k-dropzone-hint,
#uploaddoc .k-upload-status-total {
    text-align: center;
    display: block;
    width: 100%;
}
input:checked + .slider {
    background-color: #2196f3;
}

textarea:focus,
[type="text"]:focus {
    border: 1px solid #1890ff;
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

.k-notification {
    border-radius: 8px !important;
    padding: 0px 0px !important;
    border-width: 0px !important;
}

/* wizard form */

.k-wizard .k-wizard-step {
    padding: 0px;
}

.k-wizard-horizontal .k-wizard-content,
.k-wizard-horizontal .k-wizard-steps {
    margin: 0px;
}

.k-wizard .k-wizard-step:focus {
    outline: none;
}

/* === MAIN INDICATORS START ==== */

#gteSteps ol li:nth-child(2),
#gteSteps ol li:nth-child(3),
#gteSteps ol li:nth-child(4),
#gteSteps ol li:nth-child(5),
#gteSteps ol li:nth-child(6) {
    height: auto;
    border-radius: 4px;
    padding: 4px 8px 4px 4px;
    min-height: auto;
}

#gteSteps ol li:nth-child(2).k-step-current.k-step-focus,
#gteSteps ol li:nth-child(3).k-step-current.k-step-focus,
#gteSteps ol li:nth-child(4).k-step-current.k-step-focus,
#gteSteps ol li:nth-child(5).k-step-current.k-step-focus,
#gteSteps ol li:nth-child(6).k-step-current.k-step-focus {
    background: #e6f7ff;
    border: 1px solid #1890ff;
    border-radius: 4px;
}

#gteSteps ol li:nth-child(2).k-step-current.k-step-focus .k-step-indicator,
#gteSteps ol li:nth-child(3).k-step-current.k-step-focus .k-step-indicator,
#gteSteps ol li:nth-child(4).k-step-current.k-step-focus .k-step-indicator,
#gteSteps ol li:nth-child(5).k-step-current.k-step-focus .k-step-indicator,
#gteSteps ol li:nth-child(6).k-step-current.k-step-focus .k-step-indicator {
    background-color: #1890ff !important;
    border: 2px solid #fff !important;
    outline: #1890ff solid 2px !important;
}

#gteSteps ol li:nth-child(2).k-step-success.k-step-done .k-step-indicator,
#gteSteps ol li:nth-child(3).k-step-success.k-step-done .k-step-indicator,
#gteSteps ol li:nth-child(4).k-step-success.k-step-done .k-step-indicator,
#gteSteps ol li:nth-child(5).k-step-success.k-step-done .k-step-indicator,
#gteSteps ol li:nth-child(6).k-step-success.k-step-done .k-step-indicator {
    background-color: #1890ff !important;
    border: 2px solid transparent !important;
    outline: #1890ff solid transparent !important;
}

#gteSteps
    ol
    li:nth-child(2).k-step-current.k-step-focus
    .k-step-indicator::after,
#gteSteps
    ol
    li:nth-child(3).k-step-current.k-step-focus
    .k-step-indicator::after,
#gteSteps
    ol
    li:nth-child(4).k-step-current.k-step-focus
    .k-step-indicator::after,
#gteSteps
    ol
    li:nth-child(5).k-step-current.k-step-focus
    .k-step-indicator::after,
#gteSteps
    ol
    li:nth-child(6).k-step-current.k-step-focus
    .k-step-indicator::after,
#gteSteps ol li:nth-child(1) .k-step-indicator,
#gteSteps ol li:nth-child(1).k-step-current .k-step-label .k-step-text,
#gteSteps ol li:nth-child(1).k-step-done .k-step-label .k-step-text {
    display: none;
}

#gteSteps ol li:nth-child(2) .k-step-indicator,
#gteSteps ol li:nth-child(3) .k-step-indicator,
#gteSteps ol li:nth-child(4) .k-step-indicator,
#gteSteps ol li:nth-child(5) .k-step-indicator,
#gteSteps ol li:nth-child(6) .k-step-indicator {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
}

#gteSteps ol li:nth-child(2) .k-step-indicator .k-step-indicator-icon,
#gteSteps ol li:nth-child(3) .k-step-indicator .k-step-indicator-icon,
#gteSteps ol li:nth-child(4) .k-step-indicator .k-step-indicator-icon,
#gteSteps ol li:nth-child(5) .k-step-indicator .k-step-indicator-icon,
#gteSteps ol li:nth-child(6) .k-step-indicator .k-step-indicator-icon {
    color: #9ca3af;
}

#gteSteps
    ol
    li:nth-child(2).k-step-current.k-step-focus
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(3).k-step-current.k-step-focus
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(4).k-step-current.k-step-focus
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(5).k-step-current.k-step-focus
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(6).k-step-current.k-step-focus
    .k-step-indicator
    .k-step-indicator-icon {
    color: #fff;
}

#gteSteps
    ol
    li:nth-child(2).k-step-success.k-step-done
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(3).k-step-success.k-step-done
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(4).k-step-success.k-step-done
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(5).k-step-success.k-step-done
    .k-step-indicator
    .k-step-indicator-icon,
#gteSteps
    ol
    li:nth-child(6).k-step-success.k-step-done
    .k-step-indicator
    .k-step-indicator-icon {
    color: #fff;
}

#gteSteps
    ol
    li:nth-child(2).k-step-current.k-step-focus
    .k-step-label
    .k-step-text,
#gteSteps
    ol
    li:nth-child(3).k-step-current.k-step-focus
    .k-step-label
    .k-step-text,
#gteSteps
    ol
    li:nth-child(4).k-step-current.k-step-focus
    .k-step-label
    .k-step-text,
#gteSteps
    ol
    li:nth-child(5).k-step-current.k-step-focus
    .k-step-label
    .k-step-text,
#gteSteps
    ol
    li:nth-child(6).k-step-current.k-step-focus
    .k-step-label
    .k-step-text {
    color: #1890ff;
}

#gteSteps .k-step-list-vertical ~ .k-progressbar {
    left: 21px;
    margin-top: -215px !important;
    height: 200px !important;
}

#gteSteps .k-progressbar .k-state-selected {
    background-color: #1890ff;
    border-color: #1890ff;
}

.k-stepper.gteSteps .k-step-current .k-step-indicator {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
    color: #fff !important;
}

.k-stepper.gteSteps .k-step-current .k-step-indicator:hover {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
    color: #fff;
}

.k-step-list-vertical .k-step-indicator + .k-step-label {
    display: inline-block;
}

.k-stepper.gteSteps .k-step-current:hover .k-step-indicator {
    background-color: #1890ff !important;
    color: #fff !important;
}

/* ==== STEPPER CSS ==== */

/* .sidebar-menu li.active > .treeview-menu {
        display: block;
    } */

#getDocumentStepper ol li .k-step-label {
    display: none;
}

#getDocumentStepper ol li .k-step-indicator {
    width: 100% !important;
    height: auto;
    margin: 0px !important;
    display: flex !important;
    justify-content: flex-start !important;
    padding: 10px !important;
    border-right: 0px !important;
    border-left: 0px !important;
    border-top: 0px !important;
    border-color: #e5e7eb !important;
}
/* #getDocumentStepper ol li .k-step-indicator span {
    width: 100%  !important;
} */
#getDocumentStepper.k-stepper .k-step-indicator {
    border-radius: 0%;
}
#getDocumentStepper.k-stepper .k-step-indicator::after {
    border-radius: 0%;
}
#getDocumentStepper.k-stepper .k-step-done .k-step-indicator {
    background-color: #fff;
}

#getDocumentStepper .k-step-list-vertical .k-step {
    min-height: auto !important;
}
#getDocumentStepper .k-widget.k-progressbar.k-progressbar-vertical {
    display: none !important;
}
/* #getDocumentStepper ol li {
    border: 1px solid darkgray;
    margin: 0px -17px;
    padding: 15px 40px 15px 10px;
    #E6F7FF
} */

.k-stepper.getDocumentStepper .k-step-indicator {
    color: #1890ff !important;
}
.k-stepper.getDocumentStepper .k-step-indicator span strong {
    margin-bottom: 5px !important;
    display: inline-block;
}

.k-stepper.getDocumentStepper .k-step-current .k-step-indicator {
    border-color: #e5e7eb;
    color: #1890ff;
    background-color: #e6f7ff;
}
.k-stepper.getDocumentStepper .k-step-done .k-step-indicator {
    color: #1890ff !important;
}

#uploaddoc .k-upload {
    border: 0px;
    border-radius: 0.5rem;
}
#uploaddoc .k-upload-button {
    border-radius: 0.5rem !important;
}

.k-widget .k-invalid-msg {
    display: none;
}
/* DropDown  */

#visaStatusForm .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 5px 0px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#visaStatusForm .k-list-optionlabel.k-state-selected,
.k-list-optionlabel {
    display: none;
}

.k-popup .k-list .k-state-hover {
    background-color: #5ca5ff !important;
    cursor: pointer !important;
    color: white !important;
}

#visaStatusForm .k-dropdown-wrap .k-select {
    line-height: 35px !important;
    width: 35px !important;
}

#visaStatusForm .k-widget .k-invalid-msg {
    display: none;
}

/* DatePicker */

#visaStatusForm .k-form-field-wrap .k-datepicker {
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#visaStatusForm .k-picker-wrap .k-input[type="text"] {
    border-radius: 0.5rem;
    height: 36px !important;
}

#visaStatusForm .k-picker-wrap .k-input[type="text"]:focus {
    border: none !important;
    box-shadow: none !important;
}

#visaStatusForm .k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

.k-label.k-form-label {
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 5px;
}
/* //stepper */
.currentStatusStepper {
    width: 100%;
}

#visaStatusForm .k-form-buttons {
    margin-top: -63px !important;
}

#stepperID .k-progressbar .k-state-selected,
#stepperID .k-stepper .k-step-done .k-step-indicator,
#stepperID .k-stepper .k-step-current .k-step-indicator {
    border-color: #1890ff !important;
    color: #fff;
    background-color: #1890ff !important;
}

#stepperID .k-step-current:hover .k-step-indicator {
    background-color: #1890ff !important;
    background: #1890ff !important;
}

#stepperID .k-stepper .k-step-done.k-step-hover .k-step-indicator,
.k-stepper .k-step-done:hover .k-step-indicator {
    background-color: #1890ff;
}

#visaDateStepper .k-step-indicator,
#visaDateStepper .k-progressbar {
    display: none !important;
}

/* ===== COMMENT AREA ===== */

.comment-text:focus {
    outline: none;
    box-shadow: none;
    border: none;
}

.k-dialog .k-dialog-titlebar {
    background: #1890ff !important;
}

.action-div {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

[type="checkbox"]:checked:hover,
[type="checkbox"]:checked:focus,
[type="radio"]:checked:hover,
[type="radio"]:checked:focus {
    background-color: #1890ff !important;
    box-shadow: none;
    outline: 2px solid #1890ff;
    outline-offset: 2px;
}

#globalSearchText[type="text"]:focus,
[type="password"]:focus {
    border-color: #ffffff !important;
    border-color: #ffffff;
}
#globalSearchText[type="text"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
    /* outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty);
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #ffff;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
    border-color: #ffff; */
}

.titlebar-gte-modal .k-window-action .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}
.k-invalid {
    border: 1px solid red !important;
}
.k-list-scroller {
    max-height: 100% !important;
}
