let myEditor;
$(document).ready(function () {
    var gridID = '#studentPlacementList';
    var selectedStudent = [];
    var filterLoad = true;
    $(document).find('html').addClass('overflow-hidden');

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    $.ajaxSetup({
        headers: {
            Authorization: api_token,
        },
    });

    var filterData = new kendo.data.HierarchicalDataSource({
        transport: {
            read: {
                url: site_url + 'api/student-placement-filter-html',
                dataType: 'json',
                type: 'POST',
            },
            parameterMap: function (data, operation) {
                if (operation == 'read') {
                    return data;
                }
            },
        },
        schema: {
            data: 'data',
            model: {
                id: 'id',
                hasChildren: 'hasChild',
                expand: 'expanded',
            },
        },
    });

    $('#panelBar').kendoPanelBar({
        template: kendo.template($('#filterPanelBarTemplate').html()),
        dataSource: filterData,
        dataBound: function (e) {
            if (filterLoad) {
                filterLoad = false;
                $('#panelBar').data('kendoPanelBar').expand($('#panelBar > .k-item:first'));
            }
        },
        //expandMode: "single"
    });

    $('body').on('click change', '.external-filter', function () {
        updateFilterCount();
    });

    $('body').on('click', '#applyFilter', function () {
        let appliedFilterArr = [];
        let extFilterArr = { course: [], provider: [] };
        $('.external-filter:checked').each(function () {
            let field = $(this).attr('data-category');
            if (typeof field == 'undefined') {
                return true;
            }
            extFilterArr[field].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).attr('data-val'),
            });
        });
        $(document).find('#appliedFilterList').html(setAppliedFilterData(appliedFilterArr));
        manageFilterOnGrid(gridID, 'extra', extFilterArr);
        manageFilterBaseTitle();
    });

    $('body').on('change', '.external-filter', function () {
        let appliedFilterArr = [];
        let extFilterArr = { course: [], provider: [] };

        // Collect data from all checked checkboxes
        $('.external-filter:checked').each(function () {
            let field = $(this).attr('data-category');
            if (typeof field === 'undefined') {
                return true; // Skip if no category
            }

            extFilterArr[field].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).attr('data-val'),
            });
        });

        // Update the applied filters list
        $(document).find('#appliedFilterList').html(setAppliedFilterData(appliedFilterArr));

        getQueryParams(extFilterArr);

        // Apply filters to the grid
        manageFilterOnGrid(gridID, 'extra', extFilterArr);

        // Update the filter base title
        manageFilterBaseTitle();
    });

    $('body').on('click', '#clearFilter', function (e) {
        e.preventDefault();
        $(gridID).data('kendoGrid').dataSource.filter([]);
        $(document).find('.external-filter').prop('checked', false);
        $(document).find('#applyFilter span').text('');
        $(document).find('.filterFooterBox').hide();
        let extFilterArr = { course: [], provider: [] };
        getQueryParams(extFilterArr);
    });

    $('body').on('click', '.clear_applied_filter', function () {
        let filterId = $(this).attr('data-filter-id');
        if (filterId == 'all') {
            console.log('Here');
            $(document).find('#appliedFilterList').html('');
            $(document).find('#clearFilter').trigger('click');
            $(document).find('.external-filter').prop('checked', false);
            let extFilterArr = { course: [], provider: [] };
            getQueryParams(extFilterArr);
        } else {
            $(document)
                .find('input[value="' + filterId + '"]')
                .prop('checked', false)
                .trigger('change');
            $(document).find('#applyFilter').trigger('click');
        }
    });

    $('body').on('keyup', '.sidebarTopSearch', function (e) {
        let searchText = $(this).val();
        let fieldId = $(this).attr('data-field');
        let action = $('#' + fieldId).find('li');
        action.each(function () {
            if (searchText.length > 0 && $(this).find('label')[0]) {
                if (
                    $(this)
                        .find('label')[0]
                        .innerText.toUpperCase()
                        .includes(searchText.toUpperCase())
                )
                    $(this).fadeIn();
                else $(this).fadeOut();
            } else {
                $(this).fadeIn();
            }
        });
    });

    function updateFilterCount() {
        let filterCount = $(document).find('.external-filter:checked').length;
        let buttonText = '';
        if (filterCount > 0) {
            buttonText = '(' + filterCount + ')';
            $(document).find('.filterFooterBox').show();
        } else {
            $(document).find('.filterFooterBox').hide();
        }
        $(document).find('#applyFilter span').text(buttonText);
    }

    function setAppliedFilterData(appliedFilterArr) {
        let filterHtml = '';
        if (appliedFilterArr.length > 0) {
            appliedFilterArr.filter(function (arr) {
                filterHtml +=
                    '<div class="inline-flex items-center justify-center space-x-2 px-2 py-1 bg-gray-100 rounded-full mr-2 mt-2"><span class="text-sm leading-5 text-center text-gray-800">' +
                    arr['value'] +
                    '</span><span class="cursor-pointer k-icon k-i-close clear_applied_filter text-blue-500" data-filter-id="' +
                    arr['id'] +
                    '" data-key= "' +
                    arr['key'] +
                    '"></span></div>';
            });
            filterHtml +=
                '<div class="inline-flex items-center justify-center space-x-2 mt-2"><button class="text-sm leading-5 font-medium text-primary-blue-500 clear_applied_filter" data-filter-id="all">Clear Filters</button></div>';
        }
        return filterHtml;
    }

    $(gridID).kendoGrid({
        dataSource: customDataSource(
            'api/students-placement-data',
            {
                profile_picture: { type: 'string' },
                student_name: { type: 'string' },
                course_list: { type: 'string' },
                student_id: { type: 'string' },
                campus: { type: 'string' },
                start_date: { type: 'date' },
                finish_date: { type: 'date' },
                vpms_start_date: { type: 'date' },
                vpms_finish_date: { type: 'date' },
            },
            [],
            ['vpms_start_date', 'vpms_finish_date', 'start_date', 'finish_date'],
            25
        ),
        height: getGridTableHeight(gridID, 0),
        pageable: customPageableArr(),
        dataBound: function (e) {
            defaultHideShowColumn();
            initializeTooltips();
            setTimeout(function () {
                setFilterIcon(gridID);
                manageFilterBaseTitle();
            }, 100);
        },
        persistSelection: true,
        change: onChange,
        filterable: true,
        sortable: true,
        resizable: true,
        //navigatable: true,
        columns: [
            {
                selectable: true,
                width: '50px',

                headerAttributes: {
                    class: 'header-checkbox-custom',
                },
            },
            {
                template:
                    "<div class='course-name text-sm leading-5 text-gray-600 action-div glob-tooltip' title='#: student_id #'>#: student_id #</div>",
                field: 'student_id',
                title: 'Student ID',
                minResizableWidth: 80,
                width: '115px',
                filterable: false,
            },
            {
                template: function (dataItem) {
                    return manageProfilePic(
                        dataItem.id,
                        dataItem.secure_id,
                        dataItem.profile_pic,
                        dataItem.student_name,
                        dataItem.contact
                    );
                },
                field: 'student_name',
                title: 'Full Name',
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Full Name</a>",
                filterable: false,
                minResizableWidth: 260,
                width: $.cookie('studentlist-student_name')
                    ? $.cookie('studentlist-student_name') + 'px'
                    : 180,
            },

            {
                template:
                    "<div class='course-name text-sm leading-5 text-gray-600 action-div glob-tooltip' title='#: application_id #'> #: application_id # </div>",
                field: 'application_id',
                title: 'Application ID',
                width: '120px',
                filterable: false,
            },
            {
                template:
                    "<div class='course-name text-sm leading-5 text-gray-600 action-div glob-tooltip' title='#: campus #'>#: campus #</div>",
                field: 'campus',
                title: 'Campus',
                filterable: false,
                width: '120px',
            },
            {
                template:
                    "<div class='course-name text-sm leading-5 text-gray-600 action-div'>#: ((placement_officer_name !=null) ? placement_officer_name : 'N/A') #</div>",
                field: 'placement_officer_name',
                title: 'Officer Name',
                width: '120px',
                filterable: false,
            },
            {
                template: function (dataItem) {
                    return manageStatusWithCss(dataItem.status);
                },
                field: 'status',
                title: 'Status',
                width: '120px',
                filterable: false,
            },
            {
                template:
                    "<div class='course-name flex text-sm leading-5 text-gray-600 action-div'> #: ((vpms_start_date) ? (kendo.toString(vpms_start_date, 'dd MMM yyyy')) : 'N/A') #</div>",
                field: 'vpms_start_date',
                title: 'Placement Start-Date',
                width: '160px',
                filterable: false,
            },
            {
                template:
                    "<div class='course-name flex text-sm leading-5 text-gray-600 action-div'> #: ((vpms_finish_date) ? (kendo.toString(vpms_finish_date, 'dd MMM yyyy')) : 'N/A') #</div>",
                field: 'vpms_finish_date',
                title: 'Placement End-Date',
                width: '160px',
                filterable: false,
            },
            {
                template:
                    "<div class='course-name glob-tooltip text-xs bg-gray-100 py-0.5 px-2 leading-5 text-gray-600 truncate' title='#: course_list #'>#: course_list #</div>",
                field: 'course_list',
                title: 'Current Course',
                width: '150px',
                filterable: false,
                // filterable: {
                //     multi: true,
                //     search: true,
                //     dataSource: {
                //         transport: {
                //             read: {
                //                 url: site_url + "api/student-course-list",
                //                 dataType: "json",
                //                 type: "POST",
                //                 data: {
                //                     field: "course_list",
                //                 },
                //             },
                //         },
                //         schema: {
                //             data: "data",
                //         },
                //     },
                // },
                // width: ($.cookie("studentlist-course_list"))?$.cookie("studentlist-course_list")+'px': ''
            },
            {
                template:
                    '<div class=\'course-name flex text-sm leading-5 text-gray-600 action-div\'> #: kendo.toString(start_date, "dd MMM yyyy") # </div>',
                field: 'start_date',
                title: 'Course Start-Date',
                width: '150px',
                filterable: false,
            },
            {
                template:
                    '<div class=\'course-name flex text-sm leading-5 text-gray-600 action-div\'> #: kendo.toString(finish_date, "dd MMM yyyy") # </div>',
                field: 'finish_date',
                title: 'Course Finish-Date',
                width: '150px',
                filterable: false,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'> #: ((work_placement_hour !=null) ? work_placement_hour : '--') # hr </div>",
                field: 'work_placement_hour',
                title: 'Work Placement Hours',
                width: '150px',
                filterable: false,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'> #: ((provider_name !=null) ? provider_name : 'N/A') #  </div>",
                field: 'provider_name',
                title: 'Provider Name',
                width: '150px',
                filterable: false,
            },
        ],
        noRecords: noRecordTemplate(),
        excel: {
            fileName: 'Student.xlsx',
            filterable: true,
            //allPages:true
        },
        dataBound: function (e) {
            $('.totalStudentCount').show();
        },
    });
    customGridHtml(gridID);

    function showExistingFilter(filters) {
        let params = new URLSearchParams();
        for (const key in filters) {
            if (filters[key].length) {
                // Join the array into a comma-separated string
                params.set(key, filters[key].join(','));
            }
        }

        // const filterCookieUrl = getQueryParams();
    }

    showExistingFilter();

    function getQueryParams(filters) {
        let params = new URLSearchParams();

        // Update or set the query params
        for (const key in filters) {
            if (filters[key].length) {
                // Join the array into a comma-separated string
                params.set(key, filters[key].join(','));
            }
        }

        // Construct the query string without URL-encoding
        let queryString = '';
        for (let [key, value] of params.entries()) {
            queryString += `${key}=${value}&`;
        }

        // Remove the trailing '&' if it exists
        queryString = queryString.slice(0, -1);

        // Determine the new URL
        const newUrl = queryString
            ? `${window.location.pathname}?${queryString}`
            : window.location.pathname;

        // Update the browser URL without reloading the page
        history.replaceState(null, '', newUrl);
    }

    function manageStatusWithCss(status) {
        let statusTitle = '';
        let statusCode = '';

        if (status == 'notstarted' || status == null) {
            statusCode = 'gray';
            statusTitle = 'Pre-Placement';
        } else if (status == 'started') {
            statusCode = 'green';
            statusTitle = 'In-Placement';
        } else if (status == 'complete') {
            statusCode = 'primary-blue';
            statusTitle = 'Post-Placement';
        }
        let statusHtml =
            "<div class='inline-flex items-center justify-center px-2.5 py-0.5 bg-" +
            statusCode +
            "-100  rounded-full'><span class='text-xs leading-5 text-center text-" +
            statusCode +
            "-700'>" +
            statusTitle +
            '</span></div>';

        return statusHtml;
    }

    function manageProfilePic(normalId, id, profile_pic, nameStr, contact) {
        // Manage user name with profile picture or default 2 characters
        let html = '';
        if (profile_pic == '') {
            let displayName = 'NA';
            if (typeof nameStr !== undefined && nameStr != null) {
                let name = nameStr.toUpperCase().split(/\s+/);
                displayName =
                    name.length >= 2
                        ? name[0].charAt(0) + name[1].charAt(0)
                        : name[0].substring(0, 2);
            } else {
                nameStr = 'N/A';
            }
            html =
                "<div class='flex items-center scout_" +
                normalId +
                ' stud_' +
                normalId +
                " space-x-2 studentNameDiv'><div class='user-profile-pic h-7 w-7 max-w-7 flex items-center justify-center flex-shrink-0 rounded-full bg-blue-500'><span class='text-xs leading-none font-medium'>" +
                displayName +
                "</span></div>&nbsp;<button type='button' data-name='" +
                nameStr +
                "'  data-student-id='" +
                id +
                "' data-profile='" +
                profile_pic +
                "' data-contact='" +
                contact +
                "' class='view_profile student-first-name text-sm leading-5 text-gray-700 action-div hover:text-primary-blue-500  hover:underline font-normal'>" +
                nameStr +
                '</button></div>';
        } else {
            html =
                "<div class='flex items-center scout_" +
                normalId +
                '  stud_' +
                normalId +
                " space-x-2 studentNameDiv'><img class='h-7 w-7 object-cover rounded-full flex object-top' src='" +
                profile_pic +
                "' alt=''>&nbsp;<button type='button' data-name='" +
                nameStr +
                "' data-student-id='" +
                id +
                "' data-profile='" +
                profile_pic +
                "' data-contact='" +
                contact +
                "' class='view_profile student-first-name text-sm leading-5 text-gray-700 action-div hover:text-primary-blue-500 student-name-hover font-medium'>" +
                nameStr +
                '</button></div>';
        }
        return html;
    }

    function defaultHideShowColumn() {
        $(document)
            .find('.manageColumnBox .fc-checkbox')
            .each(function () {
                if ($(this).is(':checked')) $(gridID).data('kendoGrid').showColumn($(this).val());
                else $(gridID).data('kendoGrid').hideColumn($(this).val());
            });
    }

    function onChange(e) {
        let studentIds = [];
        let studentCourseIds = [];
        $('#mailToUser').html('');
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $(gridID).data('kendoGrid');
            var dataItem = grid.dataItem(this);
            studentIds.push(dataItem.id);
            studentCourseIds.push(dataItem.student_course_id);
        });
        $(document).find('.studentIds').val(studentIds);
        $(document).find('.studentCourseIds').val(studentCourseIds);
        let selectedCount = this.selectedKeyNames().length;
        let selectedTitle = 'No student selected';
        if (selectedCount > 0) {
            selectedTitle =
                selectedCount == 1 ? '1 student selected' : selectedCount + ' students selected';
            $('#action').addClass('bottomaction').removeClass('heightzero');
            $('#selectedStudents').prop('checked', true);
        } else {
            $('#action').removeClass('bottomaction').addClass('heightzero');
        }
        $(document).find('#selected_title').text(selectedTitle);
    }

    function manageFilterBaseTitle() {
        $(document)
            .find('.filter_title')
            .html('')
            .html($(gridID).find('span.k-pager-info').text().replace('results', 'students'));
        searchloading();
    }

    function refreshGridData() {
        $(gridID).data('kendoGrid').refresh();
        $(gridID).data('kendoGrid').dataSource.read();
        $('.k-i-close').trigger('click');
        $('.closeAction').trigger('click');
    }

    $('body').on('click', '#exportData', function (e) {
        $(gridID).data('kendoGrid').saveAsExcel();
        //$(gridID).getKendoGrid().saveAsExcel();
    });

    $('body').on('click', '#manageColumns', function (e) {
        //e.preventDefault();
        let checkHtml = $(document).find('.manageColumnBox');
        if (checkHtml.parent().hasClass('active')) {
            checkHtml.removeClass('active');
            checkHtml.parent().removeClass('active');
        } else {
            checkHtml.addClass('active');
            checkHtml.parent().addClass('active');
        }
    });

    $('body').on('click', '.column_filter', function (e) {
        e.preventDefault();
        let columnHtml = $(document).find('.manageColumnBox .fc-checkbox');
        if ($(this).hasClass('reset')) columnHtml.prop('checked', true);

        defaultHideShowColumn();
        if ($(this).hasClass('clear') || $(this).hasClass('save')) {
            $(document).find('#manageColumns').trigger('click');
        }
    });

    $('body').on('change', '#selectedStudents', function () {
        $('.k-checkbox').each(function () {
            if ($(this).closest('tr').is('.k-state-selected')) {
                $(this).click();
            }
        });
    });

    $('body').on('click', '.closeAction', function () {
        $('#action').removeClass('bottomaction').addClass('heightzero');
        $('#selectedStudents').trigger('click');
        $(document).find('#moreActionModal').hide();
    });

    $('body').on('click', '#moreAction', function () {
        $(document).find('#moreActionModal').toggle();
    });

    $(document).on('click', '.copy_data', function () {
        $(document).find('.copy_data').removeClass('active');
        $(this).addClass('active');
        var textVal = $(this).attr('data-text');
        var $temp = $("<input name='copy'>");
        $('body').append($temp);
        $temp.val(textVal).select();
        document.execCommand('copy');
        $temp.remove();
    });

    $(document).on('mousedown', 'td', function (event) {
        switch (event.which) {
            case 1:
                if ($(this).find('div').hasClass('action-only')) {
                    $(this)
                        .closest('td')
                        .removeClass('action-assign')
                        .addClass('action-assign')
                        .trigger('click');
                } else if ($(this).find('input').hasClass('k-checkbox')) {
                    return false;
                } else if (!$(this).find('a').hasClass('expand-row')) {
                    $(this).closest('tr').find('.k-checkbox').click();
                }
                break;
            case 2:
                break;
            case 3:
                if (!$(this).find('div').hasClass('action-only')) {
                    $(this)
                        .closest('td')
                        .removeClass('action-assign')
                        .addClass('action-assign')
                        .trigger('click');
                }
                break;
            default:
            //alert('You have a strange Mouse!');
        }
    });

    // On escape press remove modal with selected data
    document.onkeydown = function (evt) {
        evt = evt || window.event;
        var isEscape = false;
        if ('key' in evt) {
            isEscape = evt.key === 'Escape' || evt.key === 'Esc';
        } else {
            isEscape = evt.keyCode === 27;
        }
        if (isEscape) {
            if ($(document).find('#action').hasClass('bottomaction')) {
                $('#selectedStudents').trigger('click');
                $(document).find('#moreActionModal').hide();
            }
        }
    };

    //vpms

    function kendowindowOpen(windowID) {
        let kendoWindow = $(document).find(windowID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent('div')
            .addClass('!rounded-md')
            .find('.k-window-titlebar')
            .addClass(
                'titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500 !rounded-t-md'
            )
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    }

    function openWindow(title) {
        return {
            title: title,
            width: '40%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: '10%',
                left: '30%',
            },
            // animation: {
            //     close: {
            //         effects: "fade:out",
            //     },
            // },
            animation: false,
        };
    }

    $('body').on('click', '.assignVPMS', function () {
        let primaryID = $('#studentCourseIds').val();
        $.ajax({
            type: 'POST',
            url: site_url + 'api/dynamic-vpms-dates',
            dataType: 'json',
            data: { student_course_id: primaryID },
            success: function (response) {
                kendowindowOpen('#assignVpmsProviderModal');
                let editNewsReminder = $(document).find('#assignVpmsProviderModal');
                editNewsReminder.find('#start_date').val(response.vpms_start_date);
                editNewsReminder.find('#end_date').val(response.vpms_end_date);
            },
        });
    });

    $('body').on('click', '.unAssignVPMS', function () {
        let primaryID = $('#studentCourseIds').val();
        $('#confirmUnAssignVpmsModal').data('kendoDialog').open();
        $('#confirmUnAssignVpmsModal').find('#deleteAgentDocId').val(primaryID);
    });

    $('#confirmUnAssignVpmsModal').kendoDialog({
        width: '400px',
        title: 'Confirm Unassign VPMS',
        content:
            "Are you sure you want to un-assign selected records? <input type='hidden' name='id' id='deleteAgentDocId' />",
        actions: [
            { text: 'Close', action: onCancel },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    unAssignVpms($('#confirmUnAssignVpmsModal').find('#deleteAgentDocId').val());
                },
            },
        ],
        animation: {
            open: {
                effects: 'fade:in',
            },
        },
        open: onOpenConfirmCourseStarted,
        visible: false,
    });

    function onOpenConfirmCourseStarted(e) {
        $('#confirmUnAssignVpmsModal')
            .parent()
            .find('.k-dialog-titlebar')
            .addClass('bg-gradient-to-l from-green-400 to-blue-500');
        $('#confirmUnAssignVpmsModal')
            .parent()
            .find('button:first')
            .addClass(
                'bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
            );
        $('#confirmUnAssignVpmsModal')
            .parent()
            .find('.k-primary')
            .addClass(
                'text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
            );
    }

    function onCancel() {
        //alert('confirm cancel');
    }

    function unAssignVpms(PrimaryId) {
        kendo.ui.progress($(document.body), true);
        let dataArr = {
            action: 'removeStudentsVpms',
            data: { student_course_ids: PrimaryId },
        };
        $.ajax({
            type: 'POST',
            url: site_url + 'api/placement-provider/ajaxAction',
            dataType: 'json',
            data: dataArr,
            success: function (response) {
                kendo.ui.progress($(document.body), false);
                refreshGridData();
                notificationDisplay(response.message, '', response.status);
            },
        });
    }

    $('#assignVpmsProviderModal').kendoWindow(openWindow('Assign Placement Provider'));

    $('.dateField').kendoDatePicker();

    $('#assignVpmsProviderForm').kendoForm({
        orientation: 'vertical',
        formData: {
            start_date: new Date(),
            end_date: new Date(),
        },
        items: [
            {
                field: 'provider_id',
                editor: 'DropDownList',
                label: 'Provider Name',
                editorOptions: {
                    optionLabel: 'Select Provider Name',
                    dataSource: {
                        schema: {
                            data: 'data',
                        },
                        transport: {
                            read: {
                                url: site_url + 'api/get-vpms-provider-name',
                                dataType: 'json',
                                type: 'POST',
                            },
                        },
                    },
                    dataTextField: 'text',
                    dataValueField: 'value',
                },
                validation: { required: true },
                attributes: {
                    class: '!rounded-md custom-input-border',
                },
            },
            {
                field: 'start_date',
                editor: 'DatePicker',
                label: 'Start Date',
                validation: { required: true },
                attributes: {
                    class: '!border-0',
                },
            },
            {
                field: 'end_date',
                editor: 'DatePicker',
                label: 'End Date',
                validation: { required: true },
                attributes: {
                    class: '!border-0',
                },
            },
        ],
        buttonsTemplate:
            '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end px-6 py-2">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button class="flex justify-center w-24 h-8 px-3 py-2 bg-white shadow border hover:shadow-lg shadow rounded-lg border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500 cancel_btn" data-type-id="assignVpmsProviderModal" type="button">\n' +
            '<p class="text-sm font-medium leading-none text-gray-700">Cancel</p>\n' +
            '</button>\n' +
            '<button class="flex justify-center h-8 px-3 py-2 bg-primary-blue-500 hover:shadow-lg shadow rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500" id="" type="submit">\n' +
            '<p class="text-sm font-medium leading-none text-white">Assign</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
        submit: function (ev) {
            kendo.ui.progress($(document.body), true);
            let dataArr = {};
            let serializeArr = $('#assignVpmsProviderModal')
                .find('input[name], select[name], textarea[name]')
                .serializeArray();
            $(serializeArr).each(function (i, field) {
                dataArr[field.name] = field.value;
            });
            $.ajax({
                type: 'POST',
                url: site_url + 'api/placement-provider/ajaxAction',
                dataType: 'json',
                data: { action: 'saveStudentsVpms', data: dataArr },
                success: function (response) {
                    kendo.ui.progress($(document.body), false);
                    window.parent.$('#assignVpmsProviderModal').data('kendoWindow').close();
                    refreshGridData();
                    notificationDisplay(response.message, '', response.status);
                },
            });
            ev.preventDefault();
            return false;
        },
    });

    $(document).on('click', '.cancel_btn', function () {
        let modalID = $(this).attr('data-type-id');
        window.parent
            .$('#' + modalID)
            .data('kendoWindow')
            .close();
    });

    function createMultiSelectStudents(element) {
        element.removeAttr('data-bind');
        element.kendoMultiSelect({
            dataSource: {
                transport: {
                    read: {
                        url: site_url + 'api/student-full-name',
                        dataType: 'json',
                        type: 'POST',
                        data: {
                            field: 'student_name',
                        },
                    },
                },
                schema: {
                    data: 'data',
                },
            },
            change: function (e) {
                var filter = { logic: 'or', filters: [] };
                var values = this.value();
                $.each(values, function (i, v) {
                    filter.filters.push({
                        field: 'student_name',
                        operator: 'eq',
                        value: v,
                    });
                });
                $(gridID).data('kendoGrid').dataSource.filter(filter);
                searchloading();
            },
        });
    }
    function searchloading() {
        kendo.ui.progress.messages = {
            loading:
                '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
        };
        // kendo.ui.progress($(document.body), true);
        kendo.ui.progress($('#maincontainer'), true);
        setTimeout(function () {
            kendo.ui.progress($('#maincontainer'), false);
        }, 500);
    }

    initializeSplitter('#studentPlacementSplitter');
});
