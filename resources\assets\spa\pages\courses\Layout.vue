<script setup>
import { Link } from '@inertiajs/vue3'

import { onMounted } from 'vue'
onMounted(() => {
  console.log("layout loaded");
})
</script>

<template>
  <div class="flex px-6 p-4 justify-between gradientbackground">
    <div class="flex space-x-2 items-center justify-start">
      <a href="#" class="text-xl leading-6 text-white cursor-pointer hover:underline">Courses</a>
    </div>
    <div class="flex space-x-2 items-center justify-end">

    </div>

    <header>
      <Link class="text-white p-4 font-bold" :href="appUrl('spa/courses')">Courses</Link>
      <Link class="text-white p-4 font-bold" :href="appUrl('spa/course-types')">Course Types</Link>
      <Link class="text-white p-4 font-bold" :href="appUrl('spa/course-templates')">Course Template</Link>
      <a class="text-white p-4 font-bold" :href="route('user-onboard')">Laravel Route Example</a>
    </header>
  </div>
  <main class="px-6 w-full overflow-y-auto h-full relative">
    
    <article class="mt-4">
      <slot />
    </article>
  </main>
  
</template>
<style>
.gradientbackground {
  background: linear-gradient(270deg, #06B6D4 20.35%, #1E93FF 75.64%);
  /* margin-bottom: -3px; */
}
</style>