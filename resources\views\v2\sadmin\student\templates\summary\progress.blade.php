<script id="studSummaryTemplate" type="text/html">
    <div class="px-6 pb-4 md:px-8 md:pb-6 bg-gray-100 space-y-5">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-5">
            <div class="space-y-5 col-span-6 3xl:col-span-7">
                <x-v2.animation.slide
                    class="flex flex-col space-y-5 py-4 px-6 bg-white shadow-sm rounded-md w-full summaryLoader">
                    <div class="inline-flex space-x-4 items-center justify-between w-full">
                        <div class="flex space-x-2 items-center justify-start">
                            <p class="text-lg leading-7 font-medium text-gray-900">Progress</p>
                            <div class="flex items-center justify-center px-2.5 py-1 bg-green-100 rounded-xl">
                                <p class="text-xs leading-4 text-center text-green-800">Normal</p>
                            </div>
                        </div>
                        <div class="h-full">
                            <button type="button" class="goToCourseTab btn-tertiary h-[1.875rem]">
                                <p class="text-xs font-normal leading-none text-primary-blue-500">View All</p>
                                <img src="{{ asset('v2/img/profile-rightside-arrow.svg') }}" class=" " alt=" ">
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 justify-start w-full mt-5">
                        <div class="flex space-x-2 items-center justify-start">
                            <div class="w-14 h-10 row-span-2">
                                <div class="w-14 h-10 m-1 bg-white rounded-full">
                                    <svg viewBox="0 0 36 36" class="circular-chart blue">
                                        <path class="circle-bg"
                                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                        </path>
                                        <path class="circle bg-blue-500"
                                            stroke-dasharray="#= Math.round(arr['days'] * 100 / arr['diff_days']) #, 100"
                                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                        </path>
                                        <text x="18" y="20.35" class="percentage">#= Math.round(arr["days"] * 100 /
                                            arr["diff_days"]) #%</text>
                                    </svg>
                                </div>
                            </div>
                            <div class="col-span-2">
                                <p
                                    class="font-medium inset-x-0 leading-5 mx-auto reletive text-gray-900 text-sm top-0 mt-1">
                                    #= arr["days"] # of #= arr["diff_days"] # days</p>
                                <p class="leading-4 right-0 text-gray-500 text-xs mt-1">#= arr["start_date"] # To #=
                                    arr["finish_date"] #</p>
                            </div>
                        </div>
                        # let unitPer = ((arr2["use_unit"] > 0) ? (arr2["use_unit"] * 100 / arr2["total_unit"]) : 0); #
                        <div class="flex space-x-2 items-center justify-start">
                            <div class="w-14 h-10 row-span-2">
                                <div class="w-14 h-10 m-1 bg-white rounded-full">
                                    <svg viewBox="0 0 36 36" class="circular-chart green">
                                        <path class="circle-bg"
                                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                        </path>
                                        <path class="circle bg-blue-500"
                                            stroke-dasharray="#= (Number.isInteger(unitPer)) ? unitPer : unitPer.toFixed(1) #, 100"
                                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                        </path>
                                        <text x="18" y="20.35" class="percentage">#= (Number.isInteger(unitPer)) ?
                                            unitPer : unitPer.toFixed(1) #%</text>
                                    </svg>
                                </div>
                            </div>
                            <div class="col-span-2">
                                <p
                                    class="font-medium inset-x-0 leading-5 mx-auto reletive text-gray-900 text-sm top-0 mt-1">
                                    #= arr2["use_unit"] # of #= arr2["total_unit"] # units</p>
                                <p class="leading-4 right-0 text-gray-500 text-xs mt-1">#= arr2["title"] #</p>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-11 items-center justify-start w-full">
                        {{-- <div id="summaryTimeline"></div> --}}
                        <div class="tw-h-timeline">
                            <x-v2.skeleton.timeline style="display: none;" />
                            <div class="tw-timeline-wrapper" style="display: none;">
                                <div class="horizontal-timeline" id="summaryTimeline">
                                </div>
                            </div>
                        </div>
                    </div>
                </x-v2.animation.slide>
                <x-v2.animation.slide
                    class="inline-flex flex-col space-y-6 justify-start px-4 pt-4 pb-6 bg-white shadow-sm rounded-md w-full">
                    <div class="inline-flex items-center space-x-4 justify-between">
                        <div class="flex space-x-2  justify-between">
                            <p class="text-lg leading-7 font-medium text-gray-900">Payments</p>
                            <div class="flex items-center justify-center px-2.5 py-1 bg-red-100 rounded-xl">
                                <p class="text-xs leading-4 text-center text-red-800">Risk</p>
                            </div>
                        </div>
                        <div class="h-full">
                            <button type="button" class="goToPaymentHistory btn-tertiary h-[1.875rem]">
                                <p class="text-xs font-normal leading-none text-primary-blue-500">More Details</p>
                                <img src="{{ asset('v2/img/profile-rightside-arrow.svg') }}" class="w-3 h-3" alt=" ">
                            </button>
                        </div>
                    </div>
                    <div class="inline-flex items-start justify-start flex-wrap w-full">
                        <div class="inline-flex flex-col items-start justify-start w-1/3 gap-0.5">
                            <p class="text-sm font-medium leading-4 text-gray-500">Total Fee</p>
                            <p class="text-sm leading-5 text-blue-500">#: kendo.toString(arr3["total"], 'c') #</p>
                        </div>
                        <div class="inline-flex flex-col items-start justify-start w-1/3 gap-0.5">
                            <p class="text-sm font-medium leading-4 text-gray-500">Balance Due</p>
                            <p class="text-sm leading-5 text-blue-500">#: kendo.toString(arr3["unpaid"], 'c') #</p>
                        </div>

                        <div class="inline-flex flex-col items-start justify-start w-1/3 gap-0.5">
                            <p class="text-sm font-medium leading-4 text-gray-500">Fee Paid</p>
                            <p class="text-sm leading-5 text-green-500">#: kendo.toString(arr3["paid"], 'c') #</p>
                        </div>
                        <div class="inline-flex flex-col items-start justify-start w-1/3 gap-0.5 mt-5">
                            <p class="text-sm font-medium leading-4 text-gray-500">Invoiced Due</p>
                            <p class="text-sm leading-5 text-yellow-600">#: kendo.toString(arr3["due"], 'c') #</p>
                        </div>
                        <div class="inline-flex flex-col items-start justify-start w-1/3 gap-0.5 mt-5">
                            <p class="text-sm font-medium leading-4 text-gray-500">Overdue</p>
                            <div class="inline-flex space-x-1 items-center justify-start">
                                <p class="text-sm leading-5 text-red-500">#: kendo.toString(arr3["overDue"], 'c') #</p>
                                <span class="k-icon k-i-warning k-color-warning"></span>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex space-x-2 items-start justify-start w-full">
                        <button type="button" class="showPaymentSchedule btn-secondary w-1/2">
                            <img src="{{ asset('v2/img/payment-invoice.svg') }}" class="" alt="" />
                            <p class="text-xs font-normal leading-4 text-gray-700 truncate">View Payment Installments
                            </p>
                        </button>
                    </div>
                </x-v2.animation.slide>
                <x-v2.animation.slide
                    class="inline-flex flex-col space-y-6 justify-start px-4 pt-4 pb-6 bg-white shadow-sm rounded-md w-full">
                    <div class="inline-flex items-center  space-x-4 justify-between">
                        <div class="flex space-x-2  justify-between">
                            <p class="text-lg leading-7 font-medium text-gray-900">Attendance</p>
                            <div class="flex items-center justify-center px-2.5 py-1 bg-green-100 rounded-xl">
                                <p class="text-xs leading-4 text-center text-green-800">Normal</p>
                            </div>
                        </div>
                        <div class="h-full">
                            <button type="button" class="goToAttendanceTab btn-tertiary h-[1.875rem]">
                                <p class="text-xs font-normal leading-none text-primary-blue-500">More Details</p>
                                <img src="{{ asset('v2/img/profile-rightside-arrow.svg') }}" class="w-3 h-3" alt=" ">
                            </button>
                        </div>
                    </div>
                    <div class="flex space-x-2 items-start justify-start w-full">
                        <div class="flex w-1/2">
                            # let overallAttd = ((arr4[0]["overall_attd"]) ? ((arr4[0]["overall_attd"] == 100) ?
                            arr4[0]["overall_attd"]
                            : parseFloat(arr4[0]["overall_attd"]).toFixed(1)) : 0); #
                            <div class="m-1 bg-white rounded-full w-20 h-20 flex-shrink-0">
                                <svg viewBox="0 0 36 36" class="circular-chart green">
                                    <path class="circle-bg"
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                    </path>
                                    <path class="circle bg-green-500"
                                        stroke-dasharray="#= Math.round(overallAttd) #, 100"
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                    </path>
                                    <text x="18" y="20.35" class="percentage">#= overallAttd #%</text>
                                </svg>
                            </div>
                            <div class="flex space-x-2 items-center justify-start w-full">
                                <div class="inline-flex flex-col items-start justify-center h-full">
                                    <p class="w-full text-xs leading-4 text-gray-500">Overall Attendance</p>
                                    <p class="text-sm font-medium text-gray-900">#= overallAttd #%</p>
                                </div>
                            </div>
                        </div>
                        <div class="flex w-1/2">
                            # let projectedAttd = ((arr4[0]["projected_attd"]) ? ((arr4[0]["projected_attd"] == 100) ?
                            arr4[0]["projected_attd"] : parseFloat(arr4[0]["projected_attd"]).toFixed(1)) : 0); #
                            <div class="m-1 bg-white rounded-full w-20 h-20 flex-shrink-0">
                                <svg viewBox="0 0 36 36" class="circular-chart orange">
                                    <path class="circle-bg"
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                    </path>
                                    <path class="circle bg-orange-500"
                                        stroke-dasharray="#= Math.round(projectedAttd) #, 100"
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
                                    </path>
                                    <text x="18" y="20.35" class="percentage">#= projectedAttd #%</text>
                                </svg>
                            </div>
                            <div class="flex space-x-2 items-center justify-start w-full">
                                <div class="inline-flex flex-col items-start justify-center h-full">
                                    <p class="w-full text-xs leading-4 text-gray-500">Projected Attendance</p>
                                    <p class="text-sm font-medium text-gray-900">#= projectedAttd #%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="demo-section wide">
                        <div id="attendance-chart"></div>
                    </div>
                </x-v2.animation.slide>
            </div>
            <div class="space-y-5 col-span-6 3xl:col-span-5">
                <x-v2.animation.slide class="flex flex-col p-4 bg-white shadow-sm rounded-md space-y-4">
                    <div class="space-x-2">
                        <p class="text-lg leading-7 font-medium text-gray-900">Tasks</p>
                    </div>
                    # if(arr5.length == 0 && !arr5.tasks){ #

                    <div class="w-full px-4 py-12 flex-col justify-center items-center gap-2 inline-flex h-full">
                        <img src={{ asset('v2/img/icon-tick.svg')}} class="w-8 h-8 relative" alt="icon-tick">
                        <div class="text-gray-500 text-sm font-normal leading-tight tracking-tight">No Tasks Yet</div>
                        <div class="self-stretch h-5 flex-col justify-center items-center gap-1 flex">
                            <a class="text-primary-blue-400 hover:text-primary-blue-500 text-sm font-normal leading-tight tracking-tight"
                                href="{{ route('task-list', array('id' =>$studentId)) }}">
                                <div class=" justify-start items-center inline-flex">
                                    <img src="{{ asset('v2/img/add-icon.svg') }}" class="w-3 h-3" alt="Add Icon">
                                    <div
                                        class="text-primary-blue-400 hover:text-primary-blue-500 text-sm font-normal leading-tight tracking-tight">
                                        Add Task</div>
                                </div>
                            </a>
                        </div>
                    </div>
                     # }else if(arr5.tasks){ #
                            <div class="w-full flex flex-col justify-between rounded-md h-full">
                                    <div class="space-y-2">
                                       <a class="text-primary-blue-400 hover:text-primary-blue-500 text-sm font-normal leading-tight tracking-tight addTaskBeta cursor-pointer"
                                            data-studentId="$studentId">
                                            <div class=" justify-start items-center inline-flex">
                                                <img src="{{ asset('v2/img/add-icon.svg') }}" class="w-3 h-3" alt="Add Icon">
                                                <div
                                                    class="text-primary-blue-400 hover:text-primary-blue-500 text-sm font-normal leading-tight tracking-tight">
                                                    Add Task Beta</div>
                                            </div>
                                        </a>
                                        <div class="w-full h-full min-h-48 max-h-64 overflow-scroll">

                                            # if(arr5.tasks.length > 0){ #

                                            # for(let i=0; i < arr5.tasks.length; i++) { # # let task_url=site_url + 'task-detail/' +
                                                arr5.tasks[i].taskid #
                                                <div class="w-full flex flex-col">
                                                <div class="self-stretch justify-between items-center inline-flex">
                                                    <div class="pr-6 py-2 justify-start items-center gap-2 flex">
                                                        <a href="#: task_url #"
                                                            class="text-gray-700 hover:text-gray-900 text-sm font-normal leading-tight tracking-tight max-w-[16rem] truncate"
                                                            title="#: arr5.tasks[i].title #">#: arr5.tasks[i].title #</a>
                                                        <div class="justify-start items-center flex">
                                                            <img src="{{ asset('v2/img/icon-branch.svg') }}" class="w-4 h-4"
                                                                alt="Icon Branch" title="#: arr5.tasks[i].subtask # Subtasks">
                                                            <div class="text-gray-500 text-xs font-normal leading-none">#:
                                                                arr5.tasks[i].subtask_count #</div>
                                                        </div>
                                                    </div>
                                                    <div class="h-5 justify-end items-center gap-1 flex flex-1">
                                                        <img src="{{ asset('v2/img/icon-clock.svg') }}" class="w-4 h-4"
                                                            alt="Icon Clock">
                                                        <div class="text-gray-400 text-xs font-normal leading-tight tracking-wide"
                                                            title="#: arr5.tasks[i].task_lastupdate #">
                                                            #: arr5.tasks[i].task_lastupdate #
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="w-full h-px bg-gray-200"></div>
                                        </div>
                                        # } #
                                        # } #
                                    </div>
                                </div>
                                 <a class="text-primary-blue-400 hover:text-primary-blue-500 text-sm font-normal leading-tight tracking-tight"
                                        href="#: arr5.qTaskUrl #">View All Tasks Beta →</a>
                        </div>
                    # }else{ #
                    <div class="w-full flex flex-col justify-between rounded-md h-full">
                        <div class="space-y-2">
                            <a class="btn-ghost justify-start"
                                href="{{ route('task-list', array('id' =>$studentId)) }}">
                                <img src="{{ asset('v2/img/add-icon.svg') }}" class="w-3 h-3" alt="Add Icon">
                                <div class="text-sm font-normal leading-tight tracking-tight">Add Task</div>
                            </a>
                            <div class="w-full h-full min-h-48 max-h-64 overflow-scroll">

                                # if(arr5.length > 0){ #

                                # for(let i=0; i < arr5.length; i++) { # # let task_url=site_url + 'task-detail/' +
                                    arr5[i].taskid #
                                    <div class="w-full flex flex-col">
                                    <div class="self-stretch justify-between items-center inline-flex">
                                        <div class="pr-6 py-2 justify-start items-center gap-2 flex">
                                            <a href="#: task_url #"
                                                class="text-gray-700 hover:text-gray-900 text-sm font-normal leading-tight tracking-tight max-w-[16rem] truncate"
                                                title="#: arr5[i].title #">#: arr5[i].title #</a>
                                            <div class="justify-start items-center flex">
                                                <img src="{{ asset('v2/img/icon-branch.svg') }}" class="w-4 h-4"
                                                    alt="Icon Branch" title="#: arr5[i].subtask # Subtasks">
                                                <div class="text-gray-500 text-xs font-normal leading-none">#:
                                                    arr5[i].subtask #</div>
                                            </div>
                                        </div>
                                        <div class="h-5 justify-end items-center gap-1 flex flex-1">
                                            <img src="{{ asset('v2/img/icon-clock.svg') }}" class="w-4 h-4"
                                                alt="Icon Clock">
                                            <div class="text-gray-400 text-xs font-normal leading-tight tracking-wide"
                                                title="#: arr5[i].task_lastupdate #">
                                                #: arr5[i].task_lastupdate #
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full h-px bg-gray-200"></div>
                            </div>
                            # } #
                            # } #
                        </div>
                    </div>
                    <div class="justify-start items-center inline-flex mt-auto">
                        <a class="text-primary-blue-400 hover:text-primary-blue-500 text-sm font-normal leading-tight tracking-tight"
                            href="{{ route('task-list', array('id' =>$studentId)) }}">View All Taskss →</a>
                    </div>
            </div>
            # } #
            </x-v2.animation.slide>
            <x-v2.animation.slide
                class="flex flex-col items-start px-4 pt-4 pb-6 bg-white shadow-sm rounded-md overflow-y-auto space-y-4 col-span-6 3xl:col-span-5 relative">
                <div class="inline-flex items-center space-x-4 justify-between w-full">
                    <div class="flex space-x-2 justify-between">
                        <p class="text-lg leading-7 font-medium text-gray-900">Activity Log</p>
                    </div>
                    <div class="h-full">
                        {{-- <button type="button" class="quickAddNoteButton btn-tertiary h-[1.875rem]">
                            <p class="text-xs font-normal leading-none text-primary-blue-500">Add Note</p>
                            <img src="{{ asset('v2/img/add-icon.svg') }}" class="w-3 h-3" alt="Add Icon">
                        </button> --}}
                        <button type="button" class="goToActivityTab btn-tertiary h-[1.875rem]">
                            <p class="text-xs font-normal leading-none text-primary-blue-500">View All</p>
                            <img src="{{ asset('v2/img/profile-rightside-arrow.svg') }}" class=" " alt=" ">
                        </button>
                    </div>
                </div>
                <div class="flex flex-col w-full h-full">
                    <form id="addNoteDefaultForm" method="POST" accept-charset="UTF-8" enctype="multipart/form-data">
                        <!-- <textarea id="addNoteDefault" rows="1" class="block p-2.5 w-full text-sm text-gray-700 rounded-lg border border-gray-300 cusInput" placeholder="Add Note"></textarea> -->
                        <div class="flex flex-col w-full border rounded-md customAddNote hidden">
                            <textarea class="inline-flex overflow-y-auto h-40 w-full" id="addNewNote" name="addNewNote"
                                placeholder="Enter message here"></textarea>
                            <div
                                class="inline-flex items-center justify-start px-4 py-2 bg-gray-50 w-full border-b border-t">
                                <input class="" id="note_attachment" name="note_attachment" type="file" multiple
                                    style="display: none;">
                                <label for="note_attachment"
                                    class="flex space-x-2 items-center justify-center py-2 px-3 bg-white border rounded-lg border-gray-300 cursor-pointer">
                                    <img src="{{ asset('v2/img/activity-attechment.svg') }}" class=""
                                        alt="searchIcon" />
                                    <span class='block text-xs font-medium leading-4 text-gray-700'>Attach Files</span>
                                </label>
                                <span class="ml-2 selected_file"></span>
                            </div>
                            <div
                                class="inline-flex space-x-4 items-center justify-between px-2 py-2 bg-gray-50 w-full rounded-md">
                                <div></div>
                                <div class="flex space-x-2 items-center justify-end">
                                    <button type="button"
                                        class="backToDefaultNote flex justify-center px-6 py-2 bg-white shadow-sm border hover:shadow-md rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">
                                        <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
                                    </button>
                                    <button type="button"
                                        class="saveActivityNote flex justify-center h-full px-6 py-2 bg-primary-blue-500 hover:shadow-md rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                                        <p class="text-sm font-medium leading-4 text-white ">ADD NOTE</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div id="allActivityLog" class="tw-scroll-shadow"
                        style="overflow: auto; min-height: 400px; max-height: 720px;"></div>
                </div>
                <div class="w-full" id="activityTabStrip" hidden>
                    <ul class="flex flex-wrap k-tabstrip-items k-reset" data-tabs-toggle="" role="none">
                        <!-- <li class="k-state-active k-tabstrip-item k-item k-tab-on-top k-state-default k-first" role="tab" aria-selected="true" aria-controls="activityTabStrip-1" id="activityTabStrip-tab-1"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">All</span></span></li> -->
                        <!-- <li class="k-tabstrip-item k-item k-state-default" role="tab" aria-controls="activityTabStrip-2" id="activityTabStrip-tab-2"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">Notes</span></span></li>
                                        <li class="k-tabstrip-item k-item k-state-default" role="tab" aria-controls="activityTabStrip-3" id="activityTabStrip-tab-3"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">Email</span></span></li>
                                        <li class="k-tabstrip-item k-item k-state-default" role="tab" aria-controls="activityTabStrip-4" id="activityTabStrip-tab-4"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">SMS</span></span></li>
                                        <li class="k-tabstrip-item k-item k-state-default" role="tab" aria-controls="activityTabStrip-5" id="activityTabStrip-tab-5"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">Document</span></span></li>
                                        <li class="k-tabstrip-item k-item k-state-default" role="tab" aria-controls="activityTabStrip-6" id="activityTabStrip-tab-6"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">Payment</span></span></li>
                                        <li class="k-tabstrip-item k-item k-state-default" role="tab" aria-controls="activityTabStrip-7" id="activityTabStrip-tab-7"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">Task</span></span></li>
                                        <li class="k-tabstrip-item k-item k-state-default k-last" role="tab" aria-controls="activityTabStrip-8" id="activityTabStrip-tab-8"><span class="k-loading k-complete"></span><span unselectable="on" class="k-link"><span class="inline-block text-sm">Status</span></span></li>  -->
                    </ul>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div class="px-1" style="display: none;">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <!-- <div id="allActivityLog" style="overflow: auto; max-height: 600px;"></div> -->
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="notesActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="emailActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="smsActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="documentActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="paymentActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="taskActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>
                    <div class="searchdata" style="">
                        <div class="space-y-4 mt-4">
                            <div hidden class="activityTimeLineSearchInput px-1">
                                <x-v2.search-input placeholder="Search activity" class="cusInput" wrapperClass="w-full">
                                    <x-v2.icons name="icon-search" width="16" height="16" class="text-gray-400" />
                                </x-v2.search-input>
                            </div>
                            <div id="statusActivityLog" style="overflow: auto; max-height: 600px;"></div>
                        </div>
                    </div>

                </div>
            </x-v2.animation.slide>
        </div>
    </div>
    </div>
    <x-v2.skeleton.templates.summary-part data-id="studSummaryTab" style="display:none;" class="tw-skeleton" />
</script>