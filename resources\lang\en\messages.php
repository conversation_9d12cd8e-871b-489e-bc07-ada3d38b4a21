<?php

return [
    'moodle' => [
        'not_connect' => 'Moodle not connected',
        'syncing_to' => 'Course Syncing to <PERSON><PERSON><PERSON>',
        'syncing_from' => 'All Courses are Syncing from <PERSON><PERSON><PERSON>',
        'select_any_course' => 'Please select at least one course to sync.',
    ],
    'courses' => [
        'updated' => 'Course information has been successfully updated.',
        'subjectupdated' => 'Course subject has been successfully updated.',
        'subjectupdatedpratially' => 'Course subject infor has been successfully updated but units remain the same as this subject has the enrollments already.',
        'unitupdated' => 'Course unit has been successfully updated.',
        'units_sorted' => 'Units have been rearranged successfully',
        'unitadded' => 'Units have been added to the course successfully.',
        'deleted' => 'Courses have been deleted successfully.',
        'deletenotfound' => 'No course found for deletion.',
        'deletenotallowed' => 'Courses assigned to students or mapped to subjects cannot be deleted.',
        'stop_deactivate' => 'Deactivation is not possible for this course as it currently has enrolled students.',
        'stop_activate' => 'Activation cannot be processed for this course. Please complete all missing information before activating.',
        'status_not_changed' => 'Could not change the active status of this course.',
        'updated_stop_activate' => 'Course information has been successfully updated but could not activate this course as it is incomplete.',
        'updated_stop_deactivate' => 'Course information has been successfully updated but could not deactivate this course as it it currently has enrolled students.',
        'updated_status_not_changed' => 'Course information has been successfully updated but could not change the active status.',
        'updated_activated' => 'Course information has been successfully updated and activated.',
        'updated_deactivated' => 'Course information has been successfully updated and deactivated.',
        'nocourse' => 'No course found.',
        'subjectnotfound' => 'Subject does not exist.',
        'subjectnotsaved' => 'Unable to save subject information.',
        'subjectnotmapped' => 'Unable to map subject to the course.',
        'nounitsadded' => 'No units added to this subject. Subjects must contain at least one unit.',
        'unitnotfound' => 'Unit not found for editing.',
        'unitnotsynced' => 'Unit details not found in training.gov.au.',
        'course_type_invalid' => 'Course type is required and must be an integer value.',
        'superseded_date_invalid' => 'A valid superseded date is required for the superseded VET course.',
        'superseded_coursecode_invalid' => 'Provide the course code that supersedes this course.',
        'national_code_invalid' => 'A valid national code is required for the VET course.',
        'course_code_invalid' => 'Course code is required and must be a valid code (Max 150 Characters).',
        'cricos_code_invalid' => 'A valid CRICOS code is required (Max 150 Characters) for courses that accept international students.',
        'course_name_invalid' => 'Course name is required (Max 500 Characters).',
        'delivery_target_invalid' => 'Delivery target is required.',
        'course_duration_invalid' => 'Course duration is not provided.',
        'course_duration_type_invalid' => 'Specify the type of course duration.',
        'online_hours_invalid' => 'Only positive numeric values are accepted for online hours.',
        'face_to_face_hours_invalid' => 'Only positive numeric values are accepted for face-to-face hours.',
        'tuition_fee_invalid' => 'Provide the tuition fees.',
        'domestic_fee_invalid' => 'Provide the tuition fees for domestic students.',
        'maximum_weekly_study_invalid' => 'Provide the maximum weekly study hours.',
        'effective_start_invalid' => 'Higher education courses must have a course start date.',
        'effective_end_invalid' => 'Course end date must be greater than the start date.',
        'course_completion_type_invalid' => 'Select the course completion type.',
        'course_delivery_type_invalid' => 'Select the course delivery type.',
        'results_calculation_methods_invalid' => 'Select one course result calculation method.',
        'placement_officer_id_invalid' => 'Select the work placement officer.',
        'work_placement_hour_invalid' => 'Provide the work placement hours.',
        'vocational_duration_invalid' => 'Provide the work placement duration.',
        'vocational_startweek_invalid' => 'Provide the work placement start week.',
        'free_help_study_load_invalid' => 'Provide the fee help study load.',
        'free_help_study_type_invalid' => 'Select the type of fee help study.',
        'course_credit_point_invalid' => 'Provide the course credit point value.',
        'campus_list_invalid' => 'Select at least one campus to apply this course to.',
        'course_recognition_id_invalid' => 'Select the course recognition.',
        'level_of_education_id_invalid' => 'Select the level of education.',
        'field_of_education_id_invalid' => 'Select the field of education.',
        'ANZSCO_code_invalid' => 'Select the ANZSCO code.',
        'total_nominal_hours_invalid' => 'Provide the total nominal hours.',
        'avetmiss_notsaved' => 'Error! Unable to save AVETMISS information.',
        'packaging_notsaved' => 'Error! Unable to save packaging rules.',
        'higheredinfo_notsaved' => 'Error! Unable to save Higher Education information.',
        'general_detail_fail' => 'Error! Could not save course general details!',
        'hourfees_detail_fail' => 'Error! Could not save hour and fees details!',
        'faculty_detail_fail' => 'Error! Could not save faculty details!',
        'subject_deleted' => 'Subject has been successfully deleted.',
        'unit_deleted' => 'Unit has been successfully deleted.',
        'unit_deleted_fromtemplate' => 'Unit has been successfully deleted from the selected major.',
        'already_active' => 'This course is already in active state.',
        'already_inactive' => 'This course is already deactivated.',
        'activated' => 'The course has been successfully activated. It is now open for student enrollment.',
        'deactivated' => 'The course has been deactivated. It is currently closed for student enrollment.',
        'course_not_in_tga' => 'The course is not listed on training.gov.au. Verify the national code for accuracy.',
        'cannotupdateunit' => 'Unit is not allowed to edit. Some batches might be using this unit.',
        'cannotupdatesubjectunit' => 'No units can be altered from the subject that is already assigned to one or more batches.',
        'hasbatches' => 'This subject is already assigned to one or more batches. So, no changes can be made.',
        'hasenrollments' => 'This subject is already assigned to one or more students. So, no changes can be made.',
        'unittypequotafull' => 'No more units of selected unit type can be added to this course.',
        'notalladded' => '{{count}} units were not added because the course packaging rule does not allow any more of the selected unit type.',
        'updatefailed' => '{{count}} units were not updated as they were assigned with one or more batches.',
        'allunitscount' => 'Course has {{totalcount}} units in total now.',
        'unitaddedremoved' => '{{addedcount}} units added and {{removedcount}} units removed successfully.',
        'hascoreunitsadded' => '{{corecount}} core units are already added to this course.',
        'haselectiveunitsadded' => '{{electivecount}} elective units are already added to this course.',
        'nomoreunits' => 'This course/major can not have more :type units.',
        'unit_not_deleted' => 'Error!!! could not delete this unit! Possibly this unit is assigned to batches.',
        'unit_not_unlinked' => 'Error!!! could not remove this unit from this major!',
        'templatenotfound' => 'Course major not found.',
        'templateadded' => 'Course major added successfully.',
        'templatesaved' => 'Course major saved successfully.',
        'templateupdated' => 'Course major has been updated successfully.',
        'templatedeleted' => 'Course major deleted successfully.',
        'templatereservedname' => '":name" is a reserved major name. Use a different name.',
        'packageexceeded' => 'Major already has :core core units and :elective elective units added.',
        'coreexceeded' => 'Major already has :count core units added.',
        'electiveexceeded' => 'Major already has :count elective units added.',
        'templatecopied' => 'Course Major duplicated successfully.',
        'savedcelement' => 'Element of Unit of Competency saved successfully.',
        'cnotsaved' => 'Error occurred while saving Element of Unit of Competency.',
        'savedshortcourseintake' => 'Short course intake saved successfully.',
        'shortcourseintakeupdated' => 'Short course intake updated successfully.',
    ],
    'intakes' => [
        'saved' => 'Intake(s) have been saved successfully.',
        'deleted' => 'Intake(s) have been deleted successfully.',
        'campusdeleted' => 'Intakes for {{campus}} campuses have been deleted.',
        'notdeleted' => 'No intakes were deleted.',
    ],
    'attendance' => [
        'notimetableid' => 'The provided timetable ID is invalid.',
        'timetable' => 'The specified batch of students is not valid.',
        'student_id' => 'An issue occurred while processing student information.',
        'subject_id' => 'The batch subject is not correctly configured.',
        'batch' => 'The provided batch code could not be found.',
        'timetable_detail_id' => 'The date of the marked attendance does not exist or a future date was marked.',
        'attendance_id' => 'The provided attendance ID does not exist.',
        'attendance_date' => 'Invalid attendance date provided.',
        'noattendancemarked' => 'None of the attendance was marked. Please try again.',
        'partialsaved' => 'Out of :total students, attenndance of :saved student has been updated.',
        'allsaved' => 'Attenendance of :marked students has been updated successfully.',
        'publicholiday' => 'You are trying to mark attendance on a day marked as a public holiday.',
        'letter_generated' => 'Letter was successfully generated for :count :label',
        'letter_not_generated' => 'Letter could not be generated for :count :label',
    ],
    'assessment' => [
        'notfound' => 'Assessment requested is not available.',
        'duplicatetask' => 'Task with the same name is already there for the selected subject.',
        'notsaved' => 'Assessment could not be saved.',
        'saved' => 'Assessment saved successfully.',
        'assigned' => 'Assessment assigned successfully.',
        'marked' => 'Assessment marked successfully.',
        'markedbulk' => 'Assessments marked successfully for selected students.',
        'notmarked' => 'There was an error while marking the student assessment.',
        'resulttrasnferred' => 'Result transferred successfully',
    ],
    'shortcourse' => [
        'email_not_verified_only' => 'Email not verified',
        'email_not_verified' => 'Email not verified. Check your email and verify your email to continue further.',
        'registered_email_not_verified' => 'Student registered successfully. Now you need to verify your email to continue further. Please check your email for verification link.',
        'email_verified' => 'Email verified successfully.',
        'email_verified_only' => 'Email verified',
        'invalid_credentials' => 'Invalid credentials',
        'unable_to_generate_token' => 'Unable to generate token',
        'user_authenticated_successfully' => 'User authenticated successfully',
        'process_stuck' => 'Process stuck. Contact support.',
        'email_verified_redirect' => 'Email verified successfully. Redirecting to the application...',
        'email_not_verified_redirect' => 'Email not verified. Check your email and verify your email to continue further. Redirecting to the application...',
        'invalid_credentials_redirect' => 'Invalid credentials. Redirecting to the application...',
        'unable_to_generate_token_redirect' => 'Unable to generate token. Redirecting to the application...',
        'process_stuck_redirect' => 'Process stuck. Contact support. Redirecting to the application...',
        'user_not_found' => 'User not found.',
        'user_not_found_redirect' => 'User not found. Redirecting to the application...',
        'could_not_register_user' => 'Could not register user.',
        'user_registered_successfully' => 'User registered successfully.',
        'invalid_redirect_uri' => 'Invalid redirect uri',
        'invalid_redirect_uri_redirect' => 'Invalid redirect uri. Redirecting to the application...',
        'particular_student_is_not_available_for_you' => 'Particular student is not available for you.',
        'student_application_doesnot_exist' => 'Student application doesnot exist.',
        'student_found' => 'Student found',
        'student_application_initiated' => 'Student application initiated',
        'usi_details_saved' => 'USI details saved',
        'continued_without_usi' => 'Continued without USI',
        'student_not_found' => 'Student Not Found',
        'could_not_update_student_language_details' => "Could not update student's language details",
        'could_not_update_student_address_details' => "Could not update student's address details",
        'could_not_update_student_contact_details' => "Could not update student's contact details",
        'could_not_update_student_emergency_contact_details' => "Could not update student's emergency contact details",
        'could_not_update_student_previous_education_details' => "Could not update student's previous education details",
        'could_not_update_student_disability_details' => "Could not update student's disability details",
        'could_not_update_student_schooling_details' => "Could not update student's schooling details",
        'could_not_update_student_qualification_details' => "Could not update student's qualification details",
        'could_not_save_student_personal_details' => "Could not save student's personal details",
        'language_details_saved' => 'Language details saved',
        'language_deleted' => 'Language deleted',
        'disability_details_saved' => 'Disability details saved',
        'qualification_details_saved' => 'Qualification details saved',
        'qualification_deleted' => 'Qualification deleted',
        'qualification_not_found' => 'Qualification not found',
        'qualification_not_saved' => 'Qualification not saved',
        'qualification_not_deleted' => 'Qualification not deleted',
        'qualification_not_updated' => 'Qualification not updated',
        'schooling_details_saved' => 'Schooling details saved',
        'qualification_details_deleted' => 'Qualification details deleted',
        'profile_updated' => 'Profile updated',
        'could_not_update_student_employment' => "Could not update student's employment",
        'employment_details_saved' => 'Employment details saved',
        'could_not_delete_student_employment' => "Could not delete student's employment",
        'employment_deleted' => 'Employment details deleted',
        'could_not_save_student_oshc' => "Could not save student's oshc",
        'oshc_details_saved' => 'OSHC details saved',
        'could_not_save_student_emergency_contact' => "Could not save student's emergency contact",
        'emergency_contact_details_deleted' => 'Emergency contact details deleted',
        'emergency_contact_details_updated' => 'Emergency contact details updated',
        'could_not_save_student_document' => "Could not save student's document",
        'document_uploaded' => 'Document uploaded',
        'could_not_delete_student_document' => "Could not delete student's document",
        'document_deleted' => 'Document deleted',
        'student_already_enrolled_to_course' => 'Student is already enrolled to this course for the selected intake and campus.',
        'could_not_process_checkout' => 'Could not process checkout. Try again later.',
        'proceed_to_payment' => 'Proceed to payment',
        'successfully_enrolled_to_the_course' => 'Successfully enrolled to the course.',
        'booking_received_issue_with_application' => 'Booking received but could not generate checkout url. There is an issue with student application.',
        'could_not_enroll' => 'Could not enroll student to the course.',
        'could_not_save_student_previous_education' => "Could not save student's previous education",
        'previous_education_details_saved' => 'Previous education details saved',
        'previous_education_details_deleted' => 'Previous education details deleted',
        'total_courses_found' => ':total courses found',
        'enrollment_detail_found' => 'Enrollment detail found',
        'enrollment_not_found' => 'Enrollment not found.',
        'course_not_found' => 'Course not found.',
        'successfully_saved' => 'Successfully saved',
        'can_not_submit_application' => 'Can not submit application. Student Application is not complete.',
        'application_completed' => 'Application completed',
        'email_already_verified' => 'Email already verified.',
        'email_verification_link_sent' => 'Email verification link sent',
        'verification_link_expired' => 'Verification link expired.',
        'password_reset_link_sent' => 'Password reset link sent',
        'password_reset_request_expired' => 'This request is expired or was revoked or was already used.',
    ],
];
