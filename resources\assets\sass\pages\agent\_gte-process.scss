.tw-gte-steps {
    ol.k-step-list {
        li.k-step {
            &:first-child {
                display: none;
            }
            &:hover {
                .k-step-indicator {
                    .tw-stepper-icon {
                        color: white;
                    }
                }
            }
            &.k-step-current.k-focus {
                .k-step-link {
                    background: #e6f7ff;
                    border: 1px solid #1890ff;
                    border-radius: 0.25rem;
                    padding: 0.125rem;
                }

                .k-step-indicator {
                    background-color: #1890ff !important;
                    border: 2px solid #fff !important;
                    outline: #1890ff solid 2px !important;
                    .tw-stepper-icon {
                        color: white;
                    }

                    &::after {
                        display: none;
                    }
                }

                .k-step-label .k-step-text {
                    color: #1890ff;
                }
            }

            &.k-step-success.k-step-done .k-step-indicator {
                background-color: #1890ff !important;
                border: 2px solid transparent !important;
                outline: transparent solid 2px !important;

                .k-step-indicator-icon {
                    color: #fff;
                }
            }

            .k-step-indicator {
                width: 32px;
                height: 32px;
                border: 1px solid #d1d5db;

                .k-step-indicator-icon {
                    color: #9ca3af;
                }

                &.k-step-current.k-step-focus .k-step-indicator-icon {
                    color: #fff;
                }
            }
        }
    }
}
