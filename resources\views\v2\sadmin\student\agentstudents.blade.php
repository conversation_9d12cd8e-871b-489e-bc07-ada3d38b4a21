<x-v2.layouts.default>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/student.css') }}">
    </x-slot>

    {{-- Filter panelbar template --}}
    <script id="filterPanelbarTemplate" type="text/kendo-ui-template">

        <div class="flex flex-col items-start justify-start w-full #: item.value #">

            # if (item.id != 0 && typeof item.text != 'undefined') { #
            <div class="inline-flex space-x-4 items-center justify-between w-full filter-panel-title-#: item.id # ">
                <span class="text-sm font-medium leading-tight text-gray-700 cursor-pointer">#: item.text #</span>
            </div>
           
            # } #
               
            # if (item.type == 'switch') { #
            <div class="flex items-center justify-center">
                <label for="switch_#: item.value.toLowerCase() #_course" class="flex items-center cursor-pointer">
                    <div class="relative">
                        <input type="checkbox" class="sr-only external-filter" role="switch" id="switch_#: item.value.toLowerCase() #_course" data-category="#: item.field #" value="#: item.value #" data-val="#: item.original #" checked/>
                        <div class="w-10 h-5 bg-gray-200 rounded-full shadow-inner outer-dot"></div>
                        <div class="dot absolute w-5 h-5 bg-white rounded-full shadow left-0 top-0 transition"></div>
                    </div>
                    <div class="ml-3 text-gray-700 font-medium">
                        #: item.value #
                    </div>
                </label>
            </div>
            # } #

            # if (item.type == 'input') { #
            <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
                <img src="{{ asset('v2/img/search.png') }}" class="h-4 w-4" alt="searchIcon" />
                # if(item.field == 'course'){ #
                    <input type="text" data-value="#: item.value #" class="sidebarSearchForType h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } else { #
                    <input type="text" data-value="#: item.value #" class="sidebarSearch h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400 w-full" placeholder="Search #: item.subtext #">
                # } #

            </div>
            # } #

            # if (item.type == 'checkbox') { #
            <div class="inline-flex space-x-2 items-center justify-start">
                <div class="form-check flex items-center">
                    <input class="form-check-input external-filter mt-0 f-checkbox appearance-none h-4 w-4 border border-gray-300 rounded-sm bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200  align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer" type="checkbox" value="#: item.value #" data-category="#: item.field #" data-val="#: item.original #" id="#: item.category_id #_checkbox_#: item.id #">
                    <label class="text-sm leading-5 text-gray-700 h-full" for="#: item.category_id #_checkbox_#: item.id #" data-val="#: item.original #" >
                        #: item.subtext #
                    </label>
                </div>
            </div>
            # } #

            # if (item.type == 'dropdown') { #
            <select class="external-filter inline-flex space-x-2 items-center w-full justify-start inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 text-gray-500 custom-option leading-6 text-sm #: item.field #" id="#: item.value #">
                # if(item.field == 'Course') { #
                    <option value="">Select Course</option>
                # } #

                # for(var i=0; i < item.arr.length; i++){ #
                    <option value="#: item.arr[i].id #" title="#: item.arr[i].text #">#: ((item.arr[i].text.length > 30) ? (item.arr[i].text.substring(0,30) + '...') : item.arr[i].text) #</option>
                # } #
            </select>
            # } #

            # if (item.type == 'button') { #
            <button class="inline-flex space-x-2 items-center justify-center w-full h-10 py-1.5 bg-white border rounded-lg border-gray-300 hover:bg-blue-500 hover:text-white" id="#: item.value #">
                <span class="text-sm font-medium leading-none text-blue-500 hover:text-white">
                    <span class="k-icon k-i-plus text-blue-500 hover:text-white"></span>
                    Add Course
                </span>
            </button>
            # } #

        </div>

    </script>

    {{-- Student details when expand row --}}
    <script id="rowDetailTemplate" type="text/html">
        <div class='student-details'>
            <div class="grid grid-cols-2 gap-4 bg-gray-100 w-full overflow-auto">
                <div class="inline-flex flex-col space-x-6 m-4">
                    <div class="flex space-x-4 m-5">
                        # if (profile_pic == '') { let name = student_name.toUpperCase().split(/\s+/); let shortName = name[0].charAt(0) + name[1].charAt(0); #
                            <div class="rounded-full">
                                <div class='flex user-profile-pic w-12 h-12 rounded-full bg-blue-500 items-center'><span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName #</span></div>
                            </div>
                        # } else { #
                            <div class="w-12 h-12 rounded-full">
                                <img class="w-full flex-1 rounded-full" src="#= profile_pic #"/>
                            </div>
                        # } #
                        <div class="inline-flex flex-col space-y-2 items-start justify-start">
                            <p class="text-base font-medium leading-6 text-gray-900">#: student_name #</p>
                            <div class="inline-flex space-x-4 items-start justify-start">
                                {{-- <div class="flex items-center justify-center px-2.5 py-1 bg-gray-300 rounded-full">
                                    <p class="text-xs leading-none text-center text-gray-800">CURRENT XXXX</p>
                                </div> --}}
                                <div class="flex space-x-1 items-center justify-start">
                                    <p class="text-sm leading-5 text-gray-700">#: student_id #</p>
                                    <a href="javascript:void(0);" class="copy_data" title="Copy" data-text="#: student_id #"><span class="k-icon k-i-copy copy-icon hover:text-blue-500"></span></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <div class="inline-flex flex-col space-y-3 items-start justify-start">
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/overseas.png') }}" class="h-4 w-4" alt="overseasIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: stud_type_name #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/bday.png') }}" class="h-4 w-4" alt="bdayIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: convertJsDateFormat(DOB) #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/email.png') }}" class="h-4 w-4" alt="emailIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: email #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/phone.png') }}" class="h-4 w-4" alt="phoneIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: contact #</p>
                            </div>
                            <div class="inline-flex space-x-5 justify-start w-full">
                                <img src="{{ asset('v2/img/location.png') }}" class="h-4 w-4" alt="locationIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: address #</p>
                            </div>
                            <div class="inline-flex space-x-5 items-center justify-start w-full">
                                <img src="{{ asset('v2/img/dairy.png') }}" class="h-4 w-4" alt="dairyIcon" />
                                <p class="w-52 text-sm font-normal leading-5 text-gray-700">#: ((emergency == null) ? 'N/A': emergency) #</p>
                            </div>
                        </div>
                        <div class="inline-flex flex-col space-y-3 items-start justify-start">
                            <div class="inline-flex items-center justify-start">
                                <p class="w-24 text-sm leading-5 font-medium text-gray-900">Emergency</p>
                                <div class="inline-flex flex-col items-start justify-start w-56">
                                    <p class="text-sm font-normal leading-5 text-gray-700">#: ((emergency_phone == null) ? 'N/A': emergency_phone) #</p>
                                </div>
                            </div>
                            <div class="inline-flex items-center justify-start w-full">
                                <p class="w-24 text-sm leading-5 font-medium text-gray-900">USI</p>
                                <div class="flex-1">
                                    <p class="w-full text-sm font-normal leading-5 text-gray-700">#: ((USI == null) ? 'N/A': USI) #</p>
                                </div>
                            </div>
                            <div class="inline-flex  justify-start w-full">
                                <p class="w-24 text-sm leading-5 font-medium text-gray-900">Agent</p>
                                <div class="flex-1">
                                    <p class="w-full text-sm font-normal leading-5 text-gray-700">#: agency_name #</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="inline-flex flex-col justify-center space-y-2 m-8">
                    #for(var i=0; i< course_detail.length; i++){#
                    <div class="bg-white p-4 relative rounded-lg items-center justify-start shadow w-full">
                        # let color = (course_detail[i]["status"] == 'Current Student') ? 'green' : ((course_detail[i]["status"] == 'Cancelled') ? 'red' : 'blue'); #
                        <div class="w-1 h-full absolute left-0 top-0 rounded-l-lg bg-#= color #-500"></div>
                        {{--                        <img class="w-1 h-20 absolute left-0 top-0 rounded-lg" src="https://via.placeholder.com/4x101"/>--}}
                        <div class="flex-col inline-flex space-y-4 w-full">
                            <div class="inline-flex items-center justify-between space-x-2">
                                <div class="flex items-center font-normal px-2.5 py-0.5 bg-gray-200 rounded">
                                    <p class="text-xs leading-4 font-normal text-gray-500">#= course_detail[i]["course_code"] #</p>
                                </div>
                                <p class="text-sm font-medium leading-5 text-gray-700 truncate w-auto">#= course_detail[i]["course_name"] #</p>
                                <div class="flex items-center justify-end px-2.5 py-0.5 bg-gray-200 rounded">
                                    <p class="text-xs leading-4 font-normal text-gray-700 truncate">#= course_detail[i]["status"].toUpperCase() #</p>
                                </div>
                            </div>
                            <div class="inline-flex items-center justify-start space-x-4">
                                <div class="flex space-x-4 items-start justify-start">
                                    <div class="flex space-x-2 items-center justify-start">
                                        <p class="text-xs font-medium leading-4 text-gray-700">Start date</p>
                                        <p class="text-xs leading-4 text-gray-700">#= course_detail[i]["start_date"] #</p>
                                    </div>
                                    <div class="flex space-x-2 items-center justify-start">
                                        <p class="text-xs font-medium leading-4 text-gray-700">End date</p>
                                        <p class="text-xs leading-4 text-gray-700">#= course_detail[i]["finish_date"] #</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2 items-center justify-start">
                                    <div class="inline-flex space-x-0.5 items-start justify-center">
                                        # for(let j=0; j < course_detail[i]['course_progress'].length; j++){ #
                                        <div class="flex items-center justify-center w-1.5 h-6 bg-#= course_detail[i]['course_progress'][j]['color'] # rounded">
                                            # if(j==0){ #
                                            <div class="arrow-down"></div>
                                            # } #
                                        </div>
                                        # } #
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    #}#
                </div>
            </div>
        </div>
    </script>

    {{-- Tooltip with student details when hover on full name column --}}
    <script id="tooltipTemplateForFullName" type="text/html">
        <div class="inline-flex flex-col items-center justify-center bg-white shadow-xl rounded-md border-gray-100 w-full h-full">
            <div class="inline-flex space-x-3 items-start justify-start py-4 pl-4 bg-gray-50 rounded-tl-md rounded-tr-md w-full">
                <div class="w-12 h-full rounded-full">
                    # if (arr.profile_pic == '') { let name = arr.student_name.toUpperCase().split(/\s+/); let shortName = name[0].charAt(0) + name[1].charAt(0); #
                        <div class='flex user-profile-pic w-12 h-12 rounded-full bg-blue-500 items-center'><span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName #</span></div>
                    # } else { #
                        <img class="w-full flex-1 rounded-full" src="#= arr.profile_pic #"/>
                    # } #
                </div>

                <div class="inline-flex flex-col space-y-2 items-start justify-start">
                    <p class="text-base font-medium leading-normal text-gray-900">#: arr.student_name #</p>
                    <div class="inline-flex space-x-4 items-start justify-start">
<!--                        <div class="flex items-center justify-center px-2.5 py-1 bg-gray-300 rounded-full">
                            <p class="text-xs leading-none text-center text-gray-800">CURRENT XXXX </p>
                        </div>-->
                        <div class="flex space-x-1 items-center justify-start">
                            <p class="text-xs leading-tight text-gray-700">#: arr.student_id #</p>
                            <a href="javascript:void(0);" class="copy_data" title="Copy" data-text="#: arr.student_id #"><span class="k-icon k-i-copy copy-icon hover:text-blue-500"></span></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-start mt-4">
                <div class="inline-flex space-x-4 items-start justify-start">
                    <div class="inline-flex flex-col space-y-3 items-start justify-start w-56">
                        <div class="inline-flex space-x-2 items-center justify-start w-full">
                            <img src="{{ asset('v2/img/overseas.png') }}" class="h-4 w-4" alt="overseasIcon" />
                            <p class="w-52 text-xs leading-none text-gray-700">Overseas Student</p>
                        </div>
                        <div class="inline-flex space-x-2 items-center justify-start w-full">
                            <img src="{{ asset('v2/img/bday.png') }}" class="h-4 w-4" alt="bdayIcon" />
                            <p class="w-52 text-xs leading-tight text-gray-700">#: convertJsDateFormat(arr.DOB) #</p>
                        </div>
                        <div class="inline-flex space-x-2 items-center justify-start w-full">
                            <img src="{{ asset('v2/img/email.png') }}" class="h-4 w-4" alt="emailIcon" />
                            <p class="w-52 text-xs leading-tight text-gray-700">#: arr.email #</p>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-3 items-start justify-start w-56">
                        <div class="inline-flex space-x-2 items-center justify-start w-full">
                            <img src="{{ asset('v2/img/phone.png') }}" class="h-4 w-4" alt="phoneIcon" />
                            <p class="w-52 text-xs leading-tight text-gray-700">#: arr.contact #</p>
                        </div>
                        <div class="inline-flex space-x-2 items-start justify-start w-full">
                            <img src="{{ asset('v2/img/location.png') }}" class="h-4 w-4" alt="locationIcon" />
                            <p class="w-52 text-xs leading-tight text-gray-700">#: arr.address #</p>
                        </div>
                        <div class="inline-flex space-x-2 items-center justify-start w-full">
                            <img src="{{ asset('v2/img/dairy.png') }}" class="h-4 w-4" alt="dairyIcon" />
                            <p class="w-52 text-xs leading-tight text-gray-700">#: ((arr.emergency == null) ? 'N/A': arr.emergency) #</p>
                        </div>
                    </div>
                </div>
            </div>

            {{-- arr.course_detail.length--}}
            #for(var i=0; i<1; i++){#
            # let color2 = (arr.course_detail[i]["status"] == 'Current Student') ? 'green' : ((arr.course_detail[i]["status"] == 'Cancelled') ? 'red' : 'blue'); #
            <div class="bg-white m-4 relative rounded-lg items-center justify-start shadow">
                <div class="w-1 h-full absolute left-0 top-50 rounded-l-lg bg-#= color2 #-500"></div>
                <!-- <img class="w-1 h-full rounded-lg" src="https://via.placeholder.com/4x101"/> -->

                <div class="inline-flex flex-col p-4 space-y-4 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-between space-x-2">
                        <div class="flex items-center justify-center px-2.5 py-1 bg-gray-200 rounded">
                            <p class="text-xs leading-4 font-normal text-gray-500">#= arr.course_detail[i]["course_code"] #</p>
                        </div>
                        <p class="text-sm font-medium leading-5 text-gray-700 truncate w-64">#= arr.course_detail[i]["course_name"] #</p>
                        <div class="flex items-center justify-center px-2.5 py-1 bg-gray-100 rounded-full">
                            <p class="text-xs leading-4 font-normal text-gray-700 truncate">#= arr.course_detail[i]["status"].toUpperCase() # </p>
                        </div>
                    </div>
                    <div class="inline-flex items-center justify-start space-x-4">
                        <div class="flex space-x-4 items-start justify-start">
                            <div class="flex space-x-2 items-center justify-start">
                                <p class="text-xs font-medium leading-4 text-gray-700">Start date</p>
                                <p class="text-xs leading-4 text-gray-700">#= arr.course_detail[i]["start_date"] #</p>
                            </div>
                            <div class="flex space-x-2 items-center justify-start">
                                <p class="text-xs font-medium leading-4 text-gray-700">End date</p>
                                <p class="text-xs leading-4 text-gray-700">#= arr.course_detail[i]["finish_date"] #</p>
                            </div>
                        </div>
                        <div class="flex space-x-2 items-center justify-start">
                            <div class="inline-flex space-x-0.5 items-start justify-center">

                                # for(let j=0; j < arr.course_detail[i]['course_progress'].length; j++){ #
                                <div class="flex items-center justify-center w-1.5 h-6 bg-#= arr.course_detail[i]['course_progress'][j]['color'] # rounded">
                                    # if(j==0){ #
                                        <div class="arrow-down"></div>
                                    # } #
                                </div>
                                # } #
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            #}#

        </div>
    </script>

    {{-- Tooltip for current course column --}}
    <script id="tooltipTemplateForCurrentCourse" type="text/html">
        <div class="inline-flex flex-col items-start justify-start p-4 bg-white shadow-xl border rounded-md border-gray-100">
            <div class="flex flex-col space-y-2 items-start justify-start">
                <p class="w-full text-sm font-medium leading-tight text-gray-900">#= arr["course_code"] # - #= arr["course_name"] #</p>
                <div class="inline-flex items-center justify-center px-2.5 py-0.5 bg-green-100 rounded-full">
                    <p class="text-xs leading-none text-center text-green-800">#= arr["status"] #</p>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start">
                    <div class="grid grid-flow-col gap-2">
                        <div class="w-10 h-10 bg-blue-500 rounded-full row-span-2">
                            <div class="w-8 h-8 m-1 bg-white rounded-full">
                                <p class="text-xs pt-2.5 leading-3 text-center text-gray-400">#= Math.round(arr["days"] * 100 / arr["diff_days"]) #%</p>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs font-medium leading-none text-gray-900">#= arr["days"] # of #= arr["diff_days"] # days</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs leading-none text-gray-500">Ending #= arr["finish_date"] #</p>
                        </div>
                    </div>
                    <div class="grid grid-flow-col gap-2">
                        <div class="w-10 h-10 bg-green-500 rounded-full row-span-2">
                            <div class="w-8 h-8 m-1 bg-white rounded-full">
                                # let unitPer = ((arr2["use_unit"] > 0) ? (arr2["use_unit"] * 100 / arr2["total_unit"]) : 0); #
                                <p class="text-xs pt-2.5 leading-3 text-center text-gray-400">#= (Number.isInteger(unitPer)) ? unitPer : unitPer.toFixed(1) #%</p>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs font-medium leading-none text-gray-900">#= arr2["use_unit"] # of #= arr2["total_unit"] # units</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs leading-none text-gray-500">#= arr2["title"] #</p>
                        </div>
                    </div>
<!--                    <div class="grid grid-flow-col gap-2 content-center">
                        <div class="w-10 h-10 bg-yellow-500 rounded-full row-span-2">
                            <div class="w-8 h-8 m-1 bg-white rounded-full">
                                <p class="text-xs pt-2.5 leading-3 text-center text-gray-400">100%</p>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs font-medium leading-none text-gray-900">Total 120 days</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-xs leading-none text-gray-500">28 of 42 days Present</p>
                        </div>
                    </div>-->
                </div>
            </div>
        </div>
    </script>

    {{-- Tooltip for course progress column --}}
    <script id="tooltipTemplateForCourseProgress" type="text/html">
        <div class="inline-flex flex-col items-start justify-start p-4 bg-white shadow-xl border rounded-md border-gray-100">
            <p class="text-xs leading-none text-gray-700">#= unit_name #</p>
        </div>
    </script>

    <div class="flex flex-row w-full">
        <div style="transition:width 0.2s;" class="relative toggelfilter transition ease-in-out filter-left-sidebar inline-flex flex-col  space-y-4 items-start justify-start pt-4 bg-gray-50 shadow-inner widthzero">

            <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                <div class="inline-flex space-x-2 items-center px-2 py-1 justify-start bg-white border rounded-lg border-gray-300 w-full">
                    <img src="{{ asset('v2/img/search.png') }}" class="h-4 w-4" alt="searchIcon" />
                    <input type="text" class="sidebarTopSearch w-full h-6 px-2 pr-3 border-transparent focus:border-transparent text-sm leading-5 font-normal text-gray-400" placeholder="Search keywords">
                </div>
            </div>

            <ul id="panelbar" class="w-64 flex items-start justify-start">

            </ul>
            <div class="filterFooterBox flex flex-col items-center justify-start w-full py-3.5 bg-white border absolute inset-x-0 bottom-0" style="display: none;">
                <div class="flex space-x-4 items-center justify-end">
                    <div class="flex items-center justify-center w-20 h-full px-3 py-2 bg-white shadow border rounded-lg border-gray-300">
                        <button type="button" id="clearFilter" class="text-xs font-medium leading-none text-gray-700">Clear</button>
                    </div>
                    <div class="flex items-center justify-center w-24 h-full px-3 py-2 bg-primary-blue-500 shadow rounded-lg">
                        <button id="applyFilter" class="text-xs font-medium leading-tight text-white">Apply <span></span></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="transition ease-in-out filter-result flex flex-col z-10 w-full bg-white" id="toggleFilterDiv">
            <div class="flex space-x-2 items-center justify-between p-4 flex bg-gray-50 border shadow-inner">
                <p class="text-lg font-medium leading-tight text-gray-700">{{ $agentName['agency_name'] }}'s students</p>
                <a href="{{ route('view-agent-list') }}" data-toggle="tooltip" class="flex" data-original-title="Back to Agents">
                    <div class="btn-add"><i class="fa fa-reply"></i></div>
                </a>
            </div>
            <div class="flex flex-col space-y-2 items-start justify-center py-4 pl-8 pr-6  w-full">
                <div class="searchdata flex space-x-2 items-center justify-between w-full">
                    <div class="flex space-x-2 items-center justify-start h-9">
                        <button type="button" id="filterBtn" class="active flex space-x-2 items-center justify-center w-9 h-full px-2.5 py-2 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                            <svg width="14" height="14" viewBox="0 0 14 14" class="w-3 h-3" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 2C1 1.44772 1.44772 1 2 1H12C12.5523 1 13 1.44772 13 2V3.25245C13 3.51767 12.8946 3.77202 12.7071 3.95956L8.62623 8.04044C8.43869 8.22798 8.33333 8.48233 8.33333 8.74755V10.3333L5.66667 13V8.74755C5.66667 8.48233 5.56131 8.22798 5.37377 8.04044L1.29289 3.95956C1.10536 3.77202 1 3.51767 1 3.25245V2Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <p class="text-base font-medium leading-tight text-gray-700 filter_title"></p>
                    </div>
                    <div class="flex space-x-2 items-center justify-end h-9">
                        <div class="flex space-x-2 items-center justify-start w-auto h-full px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow hover:border-gray-400">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 19L13 13M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <input type="text" id="" data-grid-id="agentStudentList" class="searchInputField text-sm leading-5 font-normal text-gray-400" placeholder="Search">
                        </div>
                        <button type="button" id="exportDataList" data-agent="{{ $agentId }}" class="btn-secondary h-full glob-tooltip" title="Export">
                            <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z" fill="#9CA3AF" />
                            </svg>
                            <span class="text-sm font-normal leading-tight text-gray-500 k-grid-excel">Export</span>
                        </button>
                        <div>
                            <button type="button" id="manageColumns" class="btn-secondary w-9 h-full px-2.5 py-2.5 glob-tooltip" title="Columns">
                                <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.00033 9.3335V2.66683M7.00033 9.3335C7.00033 10.0699 6.20439 10.6668 5.22255 10.6668H3.44477C2.46293 10.6668 1.66699 10.0699 1.66699 9.3335V2.66683C1.66699 1.93045 2.46293 1.3335 3.44477 1.3335H5.22255M7.00033 9.3335C7.00033 10.0699 7.79626 10.6668 8.7781 10.6668H10.5559C11.5377 10.6668 12.3337 10.0699 12.3337 9.3335V2.66683C12.3337 1.93045 11.5377 1.3335 10.5559 1.3335H8.7781M5.22255 1.3335C6.20439 1.3335 7.00033 1.93045 7.00033 2.66683M5.22255 1.3335H8.7781M7.00033 2.66683C7.00033 1.93045 7.79626 1.3335 8.7781 1.3335" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <div class="manageColumnBox">
                                <div class="relative w-full">
                                    <div class="manage-column-box__dropdown absolute top-2 right-0 w-auto pt-4 bg-white shadow border rounded-lg border-gray-200 space-y-4">
                                        <div class="flex flex-col space-y-2 items-start justify-start w-full px-4">
                                            <div class="inline-flex space-x-4 items-center justify-between w-full">
                                                <p class="text-sm font-medium leading-tight text-gray-700 pb-2">Columns</p>
                                            </div>
                                            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                                                @foreach($column_arr as $column)
                                                    <div class="inline-flex items-center justify-start w-full pr-2 rounded">
                                                        <div class="flex space-x-2 items-center justify-start">
                                                            <input type="checkbox" class="fc-checkbox cursor-pointer rounded" id="fc_{{ $column['id'] }}" value="{{ $column['id'] }}" {{ $column['default'] }}/>
                                                            <label for="fc_{{ $column['id'] }}" class="text-sm leading-none text-gray-700">{{ $column['title'] }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                            <button class="text-sm font-medium leading-tight pb-2 pt-1 text-blue-500 reset column_filter">Reset Columns</button>
                                        </div>
                                        <div class="inline-flex space-x-4 items-center justify-end w-full py-4 pl-2 pr-2 border border-gray-200">
                                            <div class="flex space-x-4 items-center justify-end">
                                                <button type="button" class="btn-secondary w-24 h-full px-3 py-2 clear column_filter uppercase text-xs">Cancel</button>
                                                <button type="button" class="btn-primary w-28 h-full px-3 py-2 save column_filter uppercase text-xs">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="appliedFilterList"></div>
            </div>
            <input type="hidden" name="agent_id" id="agent_id" value={{ $agentId }}>
            <div class="h-full bg-gray-100">
                <div id="progressbar"></div>
                <div id="agentStudentList"></div>
            </div>
        </div>
    </div>

    {{-- Notification Templates --}}
    @include('v2.sadmin.notification')

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/jszip.min.js') }}"></script>
        <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script>
        <script src="{{ asset('v2/js/sadmin/agentstudents.js') }}"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ (isset($api_token) ? "Bearer $api_token" : "") }}";
        var SMSApiKey = "{{ Config::get('constants.SMSApiKey') }}";
        var sendSmsUrl = "{{ Config::get('constants.sendSmsUrl') }}";
    </x-slot>

</x-v2.layouts.default>