<script id="headerForFeeScheduleDiv" type="text/html">
    <div class="flex flex-col space-y-4 items-center justify-start bg-white w-full">
        <div class="inline-flex space-x-1 items-center justify-between w-full">
            <div class="flex space-x-2 items-center justify-start">
                # if (profile_pic == '') {
                let name = student_name.toUpperCase().split(/\s+/);
                let shortName = name[0].charAt(0) + name[1].charAt(0); #
                <div class="rounded-full">
                    <div class='flex user-profile-pic w-12 h-12 rounded-full bg-blue-500 items-center'>
                        <span class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                            #</span>
                    </div>
                </div>
                # } else { #
                <div class="w-12 h-12 rounded-full">
                    <img class="w-12 h-12 flex-1 rounded-full" src="#= profile_pic #" />
                </div>
                # } #
                <div class="inline-flex flex-col items-start justify-end">
                    <p class="text-sm font-bold leading-5 text-gray-900">#: student_name #</p>
                    <p class="text-xs leading-5 text-gray-400">#: generated_stud_id #</p>
                </div>
            </div>
            <div class="flex space-x-2 items-center justify-start">
                <p class="text-xs leading-5 text-gray-700">#:course_name #</p>
                <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-100 rounded">
                    <p class="text-xs leading-5 text-center text-primary-blue-800">#: status #</p>
                </div>
            </div>
        </div>
    </div>

    <div
        class="w-full p-4 bg-gray-50 rounded-lg border border-gray-200 flex-col justify-start items-start gap-4 inline-flex mt-4">
        <div class="self-stretch justify-start items-start gap-6 inline-flex">
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Offer ID</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#: offer_id #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Start Date</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#:
                    convertJsDateFormat(start_date) #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Finish Date</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#:
                    convertJsDateFormat(finish_date) #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Course Duration
                </p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#: total_weeks # Weeks</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Course Fee</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#: kendo.toString(course_fee,
                    "c") #</p>
            </div>
        </div>
        <div class="self-stretch justify-start items-start gap-6 inline-flex">
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Upfront Fee</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#:
                    kendo.toString(course_upfront_fee, "c") #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Total Fee Paid</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#: kendo.toString(paid_amount,
                    "c") #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Remaining Fee To
                    Schedule</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#:
                    kendo.toString(remaining_amount, "c") #</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Agent Commission
                </p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#: (agent_commission != null)
                    ? agent_commission : 0 #%</p>
            </div>
            <div class="grow shrink basis-0 flex-col justify-center items-start gap-0.5 inline-flex">
                <p class="self-stretch text-gray-500 text-sm font-medium leading-snug tracking-tight">Is GST ?</p>
                <p class="text-gray-700 text-sm font-normal leading-tight tracking-tight">#: (agent_gst == 'GST') ?
                    'Yes' : 'No' #</p>
            </div>
        </div>
    </div>
</script>