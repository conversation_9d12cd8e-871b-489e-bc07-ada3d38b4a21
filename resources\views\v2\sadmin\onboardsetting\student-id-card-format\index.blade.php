<x-v2.layouts.onboard>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/onboard.css') }}">
    </x-slot>

    <style>
        input[type="file" i] {
            appearance: none;
            background-color: initial;
            cursor: default;
            align-items: baseline;
            color: inherit;
            text-overflow: ellipsis;
            white-space: pre;
            text-align: start !important;
            padding: initial;
            border: initial;
            overflow: hidden !important;
        }

        #studentIdCardFormatForm .k-form-field {
            margin-bottom: 20px;
        }

        .color-preview {
            width: 30px;
            height: 30px;
            border: 1px solid #ccc;
            border-radius: 4px;
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }

        /* Color field styling */
        #primary_color, #secondary_color {
            width: 90% !important;
            height: 40px !important;
            border: 1px solid #ccc !important;
            border-radius: 4px !important;
            padding: 2px !important;
        }

        /* Form layout for color fields */
        .k-form .k-form-field[data-field="primary_color"],
        .k-form .k-form-field[data-field="secondary_color"] {
            width: 48% !important;
            display: inline-block !important;
            margin-right: 2% !important;
        }
    </style>

    <div class="flex h-full flex-row p-6">
        <div class="w-full bg-white rounded-lg p-6 shadow">
            <input id="college_id" type="hidden" name="college_id" value="{{ isset($college_id) ? $college_id : '' }}" />
            <livewire:onboarding.progress form="templates_letters.student_id_card_format" />
            <div class="flex space-x-6">
            <div class="h-full w-1/2">
                <p class="block text-lg font-medium text-gray-700 text-center mb-4">Student Card Setup</p>
                <div class="w-full rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <form id="studentIdCardFormatForm">
                        <input type="hidden" name="tracking_form" value="templates_letters.student_id_card_format" />
                    </form>
                </div>
            </div>
            <div class="h-full w-1/2">
                <p class="block text-lg font-medium text-gray-700 text-center mb-4">Student Card Preview</p>
                <div class="w-full">
                    <div id="previewContainer" class="border border-gray-300 rounded-lg p-4 min-h-96 bg-gray-50 flex items-center justify-center">
                        <div class="text-gray-500 text-center">
                            <p class="mb-2">Click "Preview Card" to see your card design</p>
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V3a1 1 0 011 1v8.586l-2-2V6a1 1 0 00-1-1H8a1 1 0 00-1 1v4.586l-2 2V4a1 1 0 011-1z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/sadmin/student-id-card-format.js') }}"></script>
        <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ isset($api_token) ? "Bearer  $api_token" : '' }}"
    </x-slot>
</x-v2.layouts.onboard>
