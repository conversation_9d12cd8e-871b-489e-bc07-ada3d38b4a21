/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './resources/**/*.blade.php',
        './resources/**/*.js',
        './resources/**/*.vue',
        './public/**/*.js',
        './src/**/*.blade.php',
    ],
    safelist: [
        'grid',
        'grid-cols-4',
        'gap-4',
        {
            pattern:
                /(bg|text|border)-(orange|indigo|pink|cyan|purple|red|gray|yellow|green|blue|light-blue|primary-blue)-(50|100|200|300|400|500|600|700|800|900)/,
            variants: ['lg', 'hover', 'focus', 'lg:hover'],
        },
    ],
    theme: {
        extend: {
            borderColor: {
                default: '#D1D5DB',
            },
            fontSize: {
                xxs: ['10px', '16px'],
                13: ['0.8125rem', '1'],
            },
            colors: {
                transparent: 'transparent',
                current: 'currentColor',
                'primary-blue': {
                    50: '#e6f7ff',
                    100: '#bae7ff',
                    200: '#91d5ff',
                    300: '#69c0ff',
                    400: '#40a9ff',
                    500: '#1890ff',
                    600: '#096dd9',
                    700: '#0050b3',
                    800: '#003a8c',
                    900: '#002766',
                },
                bluegray: {
                    50: '#f8fafc',
                    100: '#f1f5f9',
                    200: '#e2e8f0',
                    300: '#cbd5e1',
                    400: '#94a3b8',
                    500: '#64748b',
                    600: '#475569',
                    700: '#334155',
                    800: '#1e293b',
                    900: '#0f172a',
                },
                'green-teal': {
                    500: '#10B981',
                },
            },
            boxShadow: {
                'inner-line': '0px -1px 0px 0px #E5E7EB inset',
                'inner-top': '0px 1px 0px 0px #E5E7EB inset',
                popup: '0px 4px 6px -2px rgba(0, 0, 0, 0.05),0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.05)',
                top: '0 -4px 6px -1px rgba(0, 0, 0, 0.1)',
            },
            screens: {
                '3xl': '1600px',
            },
            flex: {
                10: '1 0 auto',
            },
            animation: {
                'spin-fast': 'spin 0.7s linear infinite',
                'spin-faster': 'spin 0.5s linear infinite',
            },
        },
    },
    plugins: [require('tailwindcss-animate')],
};
