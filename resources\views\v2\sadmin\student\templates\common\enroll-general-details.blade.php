<script id="generalDetailsTemplate" type="text/html">
    <div id="1" class="w-full holder">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">General Details</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
            </div>
            <div class="flex space-x-4 my-5 items-center">
                <div class="flex w-16 h-16 display_profile_pic">
                    # if (arr.profile_pic == '') { let name = arr.full_name.toUpperCase().split(/\s+/); let shortName =
                    name[0].charAt(0) + name[1].charAt(0); #
                    <div class="rounded-md">
                        <div class='flex user-profile-pic w-16 h-16 !rounded-full bg-blue-500 items-center'><span
                                class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                                #</span></div>
                    </div>
                    # } else { #
                    <div class="w-16 h-16 rounded-md object-cover object-top">
                        <img class="w-16 h-16 rounded-md object-cover object-top" src="#= arr.profile_pic #" />
                    </div>
                    # } #
                </div>
                <div class="flex flex-col items-start space-y-1 justify-center">
                    <p class="text-sm font-bold leading-5 text-gray-900">#: arr.full_name #</p>
                    <p class="text-xs leading-4 text-gray-400">#: arr.generated_stud_id #</p>
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">AVETMISS Claim (Funded)</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="is_claim" value="0" id="is_claim" />
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Is Full qualification</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">Yes</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="" value="1" id="is_qualification" disabled />
                    <input type="hidden" name="is_qualification" id="is_qualification_hidden" />
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Module of Delivery</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="mode_of_delivery" value="0" id="mode_of_delivery" />
                </div>
            </div>
            <div class="inline-flex space-x-4 items-start justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                    <p class="text-sm font-medium leading-5 text-gray-700">Offer Id</p>
                    <div class="w-full">
                        <input name="offer_id" id="offer_id_add" required data-message="Select Offer Id" />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                    <p class="text-sm font-medium leading-5 text-gray-700">Which Campus?</p>
                    <div class="w-full">
                        <input id="campus_id_add" name="campus_id" required data-message="Select Campus" />
                    </div>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-start justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700">Course</p>
                    <div class="w-full">
                        <input id="course_id_add" name="course_id" required data-message="Select Course" />
                    </div>
                </div>

            </div>
            <div class="inline-flex space-x-4 items-start justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                    <p class="text-sm font-medium leading-5 text-gray-700">Result Calculation Method</p>
                    <div class="w-full">
                        <input id="res_cal_method" name="res_cal_method" required
                            data-message="Select Result Calculation Method" />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                    <p class="text-sm font-medium leading-5 text-gray-700">Agent</p>
                    <div class="w-full">
                        <input id="agent_id" name="agent_id" required data-message="Select Agent" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>