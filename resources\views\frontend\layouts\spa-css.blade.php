<meta charset="UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

<link rel="shortcut icon" type="image/png" href="{{ asset('dist/img/galaxy360-favicon.png') }}" />

{{--
<link rel="stylesheet" href="https://unpkg.com/@progress/kendo-font-icons/dist/index.css" /> --}}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@progress/kendo-font-icons@4.0.0/dist/index.min.css" />
<link rel="preconnect" href="https://fonts.gstatic.com" />
<link
    href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet" />

@if(config('features.tailwind'))
{{--
<link rel="stylesheet" href="{{ asset(mix('build/css/tailwind.css')) }}" /> --}}
@vite(['resources/assets/sass/tailwind.css'])
@else
<link href="{{ asset('newdev/css/tailwind.css') }}" rel="stylesheet" />
@endif

@vite(['resources/assets/sass/kendo-theme.scss'])

@if (config('features.sidebar'))
{{--
<link rel="stylesheet" href="{{ asset(mix('build/css/main.css')) }}" /> --}}
@vite(['resources/assets/sass/main.scss'])
@endif

<script>
    var site_url = "{{ url('/') }}/";
    var SMSApiKey = "{{ Config::get('constants.SMSApiKey') }}";
</script>

@livewireStyles