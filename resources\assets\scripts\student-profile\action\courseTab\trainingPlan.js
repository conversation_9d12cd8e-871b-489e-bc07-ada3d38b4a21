var trainingPlanFlag = true;
var trainingPlanGridId = "#trainingPlanList";
var tpAddFormId = "#trainingPlanAddForm";
var tpEditFormId = "#editTrainingPlanForm";
var tpAddModelId = "#trainingPlanAddModel";
var tpEditModelId = "#editTrainingPlanModel";
var tpViewModelId = "#trainingPlanViewModel";
var tpDeleteModalId = "#deleteTrainingPlanModal";

$(tpAddModelId).kendoWindow(defaultWindowSlideFormat('Generate Training Plan', 65));
$(tpViewModelId).kendoWindow(defaultWindowSlideFormat('View Training Plan', 45));
$(tpEditModelId).kendoWindow(defaultWindowSlideFormat('Edit Training Plan', 65));

toggleFormDisableAttr(tpAddFormId, ".training-plan-submit");

function addTrainingPlanForm(responseData, tempselectedStudCourseID){
    $(tpAddFormId).html('').off("submit").kendoForm({
        validatable: defaultErrorTemplate(),
        orientation: "vertical",
        layout: "grid",
        type: "group",
        grid: { cols: 6, gutter: 24},
        items: [
            {
                field: "employer", editor: "DropDownList", label: "Employer", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Employer", responseData.employerData)
            },
            {
                field: "contract_date", editor: "DatePicker", editorOptions: {placeholder: "Select Date" }, label: "Contract Date", colSpan: 3, validation: { required: true },
            },
            {
                field: "contract_id", editor: "DropDownList", label: "Contract Id", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Contract", responseData.contractCodeData)
            },
            {
                field: "contract_schedule_id", label: "Purchasing Contract Schedule ID", editor: customNumberInput, colSpan: 3, validation: { required: true },
            },
            {
                field: "apprenticeship_name", label: "Apprenticeship Name", colSpan: 3, validation: { required: true },
            },
            {
                field: "status", editor: "DropDownList", label: "Status", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Status", responseData.trainingStatusData)
            },
            {
                field: "booking_id", label: "Booking ID", colSpan: 3, validation: { required: true },
            },
            {
                field: "funding_source", editor: "DropDownList", label: "Funding Source", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Funding Source", responseData.fundingSourceData)
            },
            {
                field: "venue_code", editor: "DropDownList", label: "Venue Code", colSpan: 3,
                editorOptions: setDropdownEditor("Select Venue Code", responseData.venueData)
            },
            {
                field: "aac", label: "Australian Apprenticeship Centre (AAC)", colSpan: 3, validation: { required: true },
            },
            {
                field: "note", colSpan: 6, editor: "TextArea", label: "Notes",
                editorOptions: {placeholder: "Write Notes", rows: 5}
            },
            {
                field: "training_contract_id", label: "Training Contract ID", colSpan: 3, validation: { required: true },
            },
            {
                field: "contract_type", editor: "DropDownList", label: "Contract Type", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Contract Type", responseData.contractTypeData)
            },
            {
                field: "apprenticeship_id", label: "Apprenticeship ID", colSpan: 3, validation: { required: true },
            },
            {
                field: "supervisor", label: "Supervisor", colSpan: 3, validation: { required: true },
            },
            {
                field: "state", editor: "DropDownList", label: "State", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select state", responseData.stateData)
            },
            {
                field: "course_site_id", editor: "DropDownList", label: "Course Site ID", colSpan: 3,
                editorOptions: setDropdownEditor("Select Course Site", responseData.courseSiteData)
            }
        ],
        buttonsTemplate: setWindowFooterTemplate("Add Training Plan", "submit", "training-plan-submit", true),
        submit: function (ev) {
            ev.preventDefault();
            saveTrainingPlanData(tpAddFormId, tpAddModelId, tempselectedStudCourseID);
        }
    });
}

function editTrainingPlanForm(responseData){
    $(tpEditFormId).html('').kendoForm({
        validatable: defaultErrorTemplate(),
        orientation: "vertical",
        layout: "grid",
        type: "group",
        grid: { cols: 6, gutter: 16},
        items: [
            {
                field: "employer", editor: "DropDownList", label: "Employer", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Employer", responseData.employerData)
            },
            {
                field: "contract_date", editor: "DatePicker", label: "Contract Date", colSpan: 3, validation: { required: true },
            },
            {
                field: "contract_id", editor: "DropDownList", label: "Contract Id", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Contract", responseData.contractCodeData)
            },
            {
                field: "contract_schedule_id", label: "Purchasing Contract Schedule ID", editor: customNumberInput, colSpan: 3
            },
            {
                field: "apprenticeship_name", label: "Apprenticeship Name", colSpan: 3, validation: { required: true },
            },
            {
                field: "status", editor: "DropDownList", label: "Status", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Status", responseData.trainingStatusData)
            },
            {
                field: "booking_id", label: "Booking ID", colSpan: 3, validation: { required: true },
            },
            {
                field: "funding_source", editor: "DropDownList", label: "Funding Source", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Funding Source", responseData.fundingSourceData),
            },
            {
                field: "venue_code", editor: "DropDownList", label: "Venue Code", colSpan: 3,
                editorOptions: setDropdownEditor("Select Venue Code", responseData.venueData)
            },
            {
                field: "aac", label: "Australian Apprenticeship Centre (AAC)", colSpan: 3, validation: { required: true },
            },
            {
                field: "note", colSpan: 6, editor: "TextArea", label: "Notes", colSpan: 3, validation: { required: true },
                editorOptions: { placeholder: "Write Notes", rows: 5 }
            },
            {
                field: "training_contract_id", label: "Training Contract ID", colSpan: 3, validation: { required: true },
            },
            {
                field: "contract_type", editor: "DropDownList", label: "Contract Type", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select Contract Type", responseData.contractTypeData)
            },
            {
                field: "apprenticeship_id", label: "Apprenticeship ID", colSpan: 3, validation: { required: true },
            },
            {
                field: "supervisor", label: "Supervisor", colSpan: 3, validation: { required: true },
            },
            {
                field: "state", editor: "DropDownList", label: "State", colSpan: 3, validation: { required: true },
                editorOptions: setDropdownEditor("Select State", responseData.stateData)
            },
            {
                field: "course_site_id", editor: "DropDownList", label: "Course Site ID", colSpan: 3,
                editorOptions: setDropdownEditor("Select Course Site", responseData.courseSiteData)
            }
        ],
        buttonsTemplate:
            '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-2 py-4 bottom-0 right-0 fixed border-t bg-white px-6">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn" type="button">\n' +
            '<p type="button" class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>\n' +
            '</button>\n' +
            '<button type="submit" class="flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm font-medium leading-4 text-white">EDIT TRAINING PLAN</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
        submit: function (ev) {
            updateTrainingPlanData(tpEditFormId, tpEditModelId);
            ev.preventDefault();
        },
    });
}

$(tpDeleteModalId).kendoDialog({
    width: "400px",
    title: "Delete",
    content: "Are you sure you want to delete this Training Plan? <input type='hidden' name='id' id='deleteTrainingPlanId' />",
    actions: [
        {text: 'Close'},
        {
            text: 'Yes',
            primary: true,
            action: function () {
                deleteTrainingPlanFunction($(tpDeleteModalId).find("#deleteTrainingPlanId").val());
            }
        }
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog(tpDeleteModalId),
    visible: false
});

function manageStudentTrainingPlaning(postData){
    $('#studentTrainingPlanModal').find('.addTrainingPlanBtn').attr('data-sc-id',postData.student_course_id);
    if(trainingPlanFlag){
        trainingPlanFlag = false;
        $(trainingPlanGridId).kendoGrid({
            dataSource: customDataSource(
                "api/get-student-training-plan-list",
                {
                    employer_name: {type: "string"},
                    training_contract_id: {type: "string"},
                    booking_id: {type: "string"},
                    contract_date: {type: "date"},
                    status: {type: "string"},
                    contract_type: {type: "string"}
                },
                postData
            ),
            // pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: employer_name #</div>",
                    field: "employer_name",
                    title: "Employer",
                    height: 150,
                },
                {
                    template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: training_contract_id #</div>",
                    field: "training_contract_id",
                    title: "Contract ID",
                },
                {
                    template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: booking_id #</div>",
                    field: "booking_id",
                    title: "Booking ID",
                },
                {
                    template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: convertJsDateFormat(contract_date) #</div>",
                    field: "contract_date",
                    title: "Contract Date",
                },
                {
                    template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: status #</div>",
                    field: "status",
                    title: "Status",
                },
                {
                    template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: contract_type #</div>",
                    field: "contract_type",
                    title: "Venue",
                },
                // {
                //     template: function(dataItem) {
                //         return manageTrainingPlanAction(dataItem.id,dataItem.student_course_id);
                //     },
                //     field: "action",
                //     title: "ACTION",
                //     headerTemplate:"<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                //     filterable: false,
                // },
                {
                    width: 50,
                    height: 150,
                    // headerTemplate:"<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                    field: "",
                    title: "",
                    filterable: false,
                    sortable: false,
                    template: function(dataItem) {
                        return manageTrainingPlanAction(dataItem.id, dataItem.student_course_id);
                    }
                }
            ],
            noRecords: noRecordTemplate(),
            dataBound: onBoundStudentTrainingPlan
        });
        customGridHtml(trainingPlanGridId);
        $(trainingPlanGridId + ' tr td:nth-child(1)').hide();
    }else{
        refreshGrid(trainingPlanGridId, postData);
    }
}
$(trainingPlanGridId).kendoTooltip({
    filter: "td .action-only",
    position: "bottom-left",
    // showOn: "click",
    content: function(e){
        let dataItem = $(trainingPlanGridId).data("kendoGrid").dataItem(e.target.closest('tr'));
        return kendo.template($("#traningPlanActionTemplate").html())({ 'id': dataItem.id, 'student_course_id': dataItem.student_course_id});
    }
});

function onBoundStudentTrainingPlan(e){
    togglePagination(trainingPlanGridId);
    if($(trainingPlanGridId).data('kendoGrid').dataSource.total() > 0){
        $(document).find('.noDataAndAddTrainingPlan').hide();
        $(document).find('.trainingPlanListDiv').show();
    }else{
        $(document).find('.noDataAndAddTrainingPlan').show();
        $(document).find('.trainingPlanListDiv').hide();
    }
}

function manageTrainingPlanAction(id, studCourseId){
    return `<div class="action-div action-only h-4 flex justify-start items-center space-x-1">
                <div class="action-div inline-flex flex-col items-center justify-center" data-id = "${id}" data-student_course_id = "${studCourseId}">
                    <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
                </div>
            </div>`;
}

function checkFormValidOrNot(formId){
    if(formId.length > 0) {
        let validator = defaultFormValidator(formId);
        if (!validator.validate()) {
            notificationDisplay('Please fill in all the required fields.', '', 'error');
            return false;
        }
        return true;
    }
    return false;
}

function saveTrainingPlanData(formId='', modalId='', tempselectedStudCourseID){
    if(checkFormValidOrNot(formId)){

        let defaultDataArr = { 'college_id': collegeId, 'student_id': studentId, 'student_course_id': tempselectedStudCourseID };
        let dataArr = getSerializeFormArray(formId, defaultDataArr);

        ajaxActionV2("api/save-training-plan-data", "POST", dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if(response.status == 'success'){
                closeKendoWindow(modalId);
                reloadGrid(trainingPlanGridId);
                $(formId)[0].reset();
            }
        });
    }
}

function updateTrainingPlanData(formId='', modalId=''){
    if(checkFormValidOrNot(formId)) {
        let defaultDataArr = { 'course_training_plan_id': $('#course_training_plan_id').val() };
        let dataArr = getSerializeFormArray(formId, defaultDataArr);
        console.log(dataArr);
        ajaxActionV2("api/update-training-plan-data", "POST", dataArr, function(response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                closeKendoWindow(modalId);
                closeKendoWindow(tpViewModelId);
                reloadGrid(trainingPlanGridId);
                $(formId)[0].reset();
            }
        });
    }
}

function setTrainingPlanHeaderData(sDetail){
    let studProfilePictureHtml = '';
    if (sDetail.profile_picture == '') {
        let name = sDetail.student_name.toUpperCase().split(/\s+/);
        let shortName = (name.length >= 2) ? name[0].charAt(0) + name[1].charAt(0) : name[0].substring(0, 2);
        studProfilePictureHtml = '<div class="w-16 h-full rounded-full rounded-full"><div class="flex user-profile-pic flex-1 w-16 h-16 rounded-full bg-blue-500 items-center"><span class="text-2xl flex justify-center items-center leading-6 px-1 w-full">'+shortName+'</span></div></div>';
    } else {
        studProfilePictureHtml = '<div class="w-16 h-16 rounded-full"><img class="w-full flex-1 w-16 h-16 rounded-full" src="'+ sDetail.profile_picture +'"/></div>';
    }
    let courseStatusHtml = `<div class="flex items-center justify-center px-3 py-0.5 bg-${sDetail.status_color}-100 rounded-full"><p class="text-xs leading-5 text-center text-${sDetail.status_color}-900 truncate">${sDetail.status}</p></div>`;

    $('.studProfilePictureHtml').html(studProfilePictureHtml);
    $('.studentCourseStatus').html(courseStatusHtml);
    $('.studentName').text(sDetail.student_name);
    $('.studentGeneratedId').text(sDetail.generated_stud_id);
    $('.studentCourseName').text(sDetail.course_name);
    $('.studentCourseDuration').text(sDetail.duration);
    $('.studentCourseCampusName').text(sDetail.campus_name);
}

function setEditTrainingPlanFormData(pDetail){
    $('#editTrainingPlanForm').data("kendoForm").setOptions({
        formData : {
            contract_date:pDetail.contract_date,
            training_contract_id:pDetail.training_contract_id,
            contract_schedule_id:pDetail.contract_schedule_id,
            apprenticeship_name:pDetail.apprenticeship_name,
            apprenticeship_id:pDetail.apprenticeship_id,
            booking_id:pDetail.booking_id,
            supervisor:pDetail.supervisor,
            aac:pDetail.aac,
            note:pDetail.note,
            state:'0'+pDetail.state,
            status:pDetail.status,
            employer:pDetail.employer,
            venue_code:pDetail.venue_code,
            contract_id:pDetail.contract_id,
            course_site_id:pDetail.course_site_id,
            funding_source:pDetail.funding_source,
            contract_type:pDetail.contract_type
        }
    });
}

function deleteTrainingPlanFunction(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2("api/delete-training-plan-data", "POST", {'id': primaryID}, function (response) {
            reloadGrid(trainingPlanGridId);
            notificationDisplay(response.message, '', response.status);
        });
    }
}

function manageEditorOptions(routeName, dataArr, dataVal='Id', dataText='Name'){
    return {
        dataSource: getDropdownDataSource(routeName, dataArr),
        dataValueField: dataVal,
        dataTextField: dataText,
    }
}

function manageEditorOptionsWithDefaultOption(routeName, dataArr, optionLabel, dataVal='Id', dataText='Name'){
    return {
        optionLabel: optionLabel,
        dataSource: getDropdownDataSource(routeName, dataArr),
        dataValueField: dataVal,
        dataTextField: dataText,
    }
}

$('body').on('click', '.addTrainingPlanBtn', function(e){
    e.preventDefault();
    let tempselectedStudCourseID = selectedStudCourseID;
    let tempSelectedDataArr = selectedDataArr;
    if ($(this).hasAttr("data-sc-id")) {
        tempSelectedDataArr = { 'college_id': collegeId, 'student_id': studentId, 'student_course_id': $(this).attr('data-sc-id') };
        tempselectedStudCourseID = $(this).attr('data-sc-id');
    }
    kendoWindowOpen(tpAddModelId);
    ajaxActionV2("api/get-training-plan-data", "POST", tempSelectedDataArr, function (response) {
        setTrainingPlanHeaderData(response.data.student_detail);
        addTrainingPlanForm(response.data, tempselectedStudCourseID);
    });
});

$('body').on('click', '.viewTrainingPlanBtn', function(e){
    e.preventDefault();
    kendoWindowOpen(tpViewModelId);
    console.log($(this).attr('data-student_course_id'));
    let defaultDataArr = { 'id': $(this).attr('data-id'), 'college_id': collegeId, 'student_id': studentId, 'student_course_id':  $(this).attr('data-student_course_id') };
    ajaxActionV2("api/get-training-plan-data", "POST", defaultDataArr, function (response) {
        let trainingPlanView = kendo.template($("#trainingPlanViewTemplate").html())({ 'data' :response.data});
        $(document).find('#trainingPlanView').html(trainingPlanView);
    });
});

$('body').on('click', '.openEditTrainingPlanBtn', function(e){
    e.preventDefault();
    kendoWindowOpen(tpEditModelId);
    let tempSelectedDataArr = selectedDataArr;
    if ($(this).hasAttr("data-sc-id")) {
        tempSelectedDataArr = { 'college_id': collegeId, 'student_id': studentId, 'student_course_id': $(this).attr('data-sc-id') };
    }
    tempSelectedDataArr['id'] = $(this).attr('data-id');
    ajaxActionV2("api/get-training-plan-data", "POST", tempSelectedDataArr, function (response) {
        editTrainingPlanForm(response.data);
        setTrainingPlanHeaderData(response.data.student_detail);
        setEditTrainingPlanFormData(response.data.plan_detail);
        $('#course_training_plan_id').val(response.data.plan_detail.id);
    });
});

$('body').on('click', '.deleteTrainingPlanBtn', function(e){
    e.preventDefault();
    var primaryID= $(this).attr('data-id');
    $(tpDeleteModalId).data("kendoDialog").open();
    $(tpDeleteModalId).find("#deleteTrainingPlanId").val(primaryID);
});

$('body').on("click", ".trainingPlanBtn", function() {
    let tempSelectedDataArr = selectedDataArr;
    if ($(this).hasAttr("data-sc-id")) {
        tempSelectedDataArr = { 'college_id': collegeId, 'student_id': studentId, 'student_course_id': $(this).attr('data-sc-id') };
    }
    manageStudentTrainingPlaning(tempSelectedDataArr);
    kendoWindowOpen("#studentTrainingPlanModal");
});