<?php

namespace App\Http\Controllers\v2\api;

use App;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\StudentProfile\EditStudentSanctionRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveCourseVariantRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveExitInterviewRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentCourseInformationRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentOsHelpRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentSaHelpRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentSanctionRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentTcsiDetailsRequest;
use App\Http\Requests\FormValidation\StudentProfile\TcsiStudentCreditOfferRequest;
use App\Model\v2\Student;
use App\Model\v2\StudentIdCardFormat;
use App\Process\StudentProfile\SaveCourseVariantProcess;
use App\Services\StudentProfileMoreActionServicess;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use App\Users;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Password;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;
use Support\Services\UploadService;

class StudentProfileMoreActionApiController extends Controller implements UpdatesUserProfileInformation
{
    use CommonTrait;
    use MustVerifyEmail;
    use ResponseTrait;

    private $studentProfileMoreActionServicess;

    public function __construct(
        StudentProfileMoreActionServicess $studentProfileMoreActionServicess
    ) {
        $this->studentProfileMoreActionServicess = $studentProfileMoreActionServicess;
        ini_set('memory_limit', '-1');
    }

    public function saveStudentSanction(SaveStudentSanctionRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentSanctionData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentSanctionList(Request $request)
    {
        $result = $this->studentProfileMoreActionServicess->getStudentSanctionListData($request);
        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentSanctionDetail(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getStudentSanctionDetailData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateStudentSanction(EditStudentSanctionRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->editStudentSanctionData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteStudentSanction(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required',
        ]);
        $data = $this->studentProfileMoreActionServicess->deleteStudentSanctionData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentExitInterviewList(Request $request)
    {
        $result = $this->studentProfileMoreActionServicess->getStudentExitInterviewListData($request);

        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveExitInterview(SaveExitInterviewRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveExitInterviewData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function updateExitInterview(SaveExitInterviewRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->updateExitInterviewData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getExitInterview(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getExitInterviewData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function deleteStudentExitInterview(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required',
        ]);
        $data = $this->studentProfileMoreActionServicess->deleteStudentExitInterviewData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentCard($studentId)
    {
        if (($studentId > 0)) {
            $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentDetail($studentId);
            if (isset($arrStudentInfo)) {
                $profilePicPath = Config::get('constants.displayProfilePicture');
                $filePath = Config::get('constants.uploadFilePath.StudentPics');
                $profilePicDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                $profilePicPath = $profilePicDestinationPath['view'];
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $logoPath = $destinationPath['view'];

                $data['clg_logo'] = $logoPath.$arrStudentInfo[0]->college_logo;
                $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $profilePicFullPath = public_path($profilePicPath.$arrStudentInfo[0]->profile_picture);
                if (File::exists($profilePicFullPath)) {
                    $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                } else {
                    $data['profile_pic'] = 'dist/img/avatar6.png';
                }
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.student-card-pdf', $data);

                return $pdf->download('student-card.pdf');
            }
        }
    }

    public function getStudentCardNew($studentId)
    {
        if (($studentId > 0)) {
            $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentDetail($studentId);
            if (isset($arrStudentInfo)) {
                $collegeId = Auth::user()->college_id;

                // Get student card settings
                $settings = StudentIdCardFormat::getForCollege($collegeId);
                if (! $settings) {
                    $settings = StudentIdCardFormat::getDefaults();
                } else {
                    $settings = $settings->toArray();
                }

                $profilePicPath = Config::get('constants.displayProfilePicture');
                $filePath = Config::get('constants.uploadFilePath.StudentPics');
                $profilePicDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                $profilePicPath = $profilePicDestinationPath['view'];
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $logoPath = $destinationPath['view'];

                $data['clg_logo'] = $logoPath.$arrStudentInfo[0]->college_logo;
                $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                $data['arrStudentInfo'] = $arrStudentInfo[0];
                $data['settings'] = $settings;

                $profilePicFullPath = public_path($profilePicPath.$arrStudentInfo[0]->profile_picture);
                if (File::exists($profilePicFullPath)) {
                    $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                } else {
                    $data['profile_pic'] = 'dist/img/avatar6.png';
                }

                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.student-id-card-pdf', $data);
                $pdf->setPaper([0, 0, 243, 153], 'landscape'); // 3.375" x 2.125" in points

                return $pdf->download('student-id-card.pdf');
            }
        }
    }

    public function getStudentCardSettings(Request $request)
    {
        $collegeId = $request->input('college_id');

        if (! $collegeId) {
            return $this->errorResponse('College ID is required.', 'data', [], 400);
        }

        $settings = StudentIdCardFormat::getForCollege($collegeId);

        if (! $settings) {
            $settings = StudentIdCardFormat::getDefaults();

            return $this->successResponse('Default settings loaded.', 'data', $settings);
        }

        $settingsArray = $settings->toArray();

        // Add background image URL if exists
        if ($settingsArray['background_image']) {
            $filePath = Config::get('constants.uploadFilePath.StudentCardBackGround');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
            $backgroundImagePath = $destinationPath['view'].$settingsArray['background_image'];
            $settingsArray['background_image_url'] = UploadService::exists($backgroundImagePath) ? UploadService::url($backgroundImagePath) : null;
        } else {
            $settingsArray['background_image_url'] = null;
        }

        return $this->successResponse('Settings loaded successfully.', 'data', $settingsArray);
    }

    public function previewStudentCard(Request $request)
    {
        $collegeId = $request->input('college_id');
        $settings = $request->input('settings', []);

        if (! $collegeId) {
            return $this->errorResponse('College ID is required.', 'data', [], 400);
        }

        // Create a sample student data for preview
        $sampleStudent = (object) [
            'name_title' => 'Mr.',
            'first_name' => 'John',
            'middel_name' => '',
            'family_name' => 'Doe',
            'generated_stud_id' => 'STU001234',
            'DOB' => '1995-05-15',
            'course_name' => 'Certificate IV in Business',
            'campus_name' => 'Main Campus',
            'college_name' => 'Churchill Institute',
        ];

        // Merge with default settings
        $defaultSettings = StudentIdCardFormat::getDefaults();
        $settings = array_merge($defaultSettings, $settings);

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $logoPath = $destinationPath['view'];

        $data = [
            'clg_logo' => $logoPath.'default-logo.png', // Use default logo for preview
            'profile_pic' => 'dist/img/avatar6.png', // Use default avatar for preview
            'arrStudentInfo' => $sampleStudent,
            'settings' => $settings,
        ];

        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView('v2.sadmin.student.pages.student-id-card-pdf', $data);
        $pdf->setPaper([0, 0, 243, 153], 'landscape'); // 3.375" x 2.125" in points

        // Generate a temporary file for preview
        $fileName = 'preview_student_card_'.time().'.pdf';
        $tempPath = storage_path('app/temp/'.$fileName);

        // Ensure temp directory exists
        if (! File::exists(storage_path('app/temp'))) {
            File::makeDirectory(storage_path('app/temp'), 0755, true);
        }

        $pdf->save($tempPath);

        $previewUrl = url('storage/temp/'.$fileName);

        return $this->successResponse('Preview generated successfully.', 'data', [
            'preview_url' => $previewUrl,
            'file_path' => $tempPath,
        ]);
    }

    public function uploadStudentCardBackGround(Request $request)
    {
        $collegeId = auth()->user()->college_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $backgroundImage = $request->file();
        if (isset($backgroundImage['background_image'])) {
            $file = $backgroundImage['background_image'];
            $filePath = Config::get('constants.uploadFilePath.StudentCardBackGround');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $college_name = str_replace(' ', '_', 'student_card_bg');
            $bg_name = str_replace(' ', '_', hashFileName($originalFileName));
            $filename = 'background'.'-'.$college_name.'-'.$bg_name;
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form Student Card Background', [$res]);
            if ($res && ! empty($collegeId)) {
                // Update the student card format record with the background image filename
                StudentIdCardFormat::updateForCollege($collegeId, ['background_image' => $filename]);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Background image uploaded successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something went wrong. Please try again.']);
        exit;
    }

    public function removeStudentCardBackGround(Request $request)
    {
        $collegeId = $request->post('college_id');
        if (! empty($collegeId)) {
            $filePath = Config::get('constants.uploadFilePath.StudentCardBackGround');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $settings = StudentIdCardFormat::getForCollege($collegeId);
            if ($settings && $settings->background_image) {
                $fileName = $settings->background_image;
                UploadService::delete($destinationPath['view'].$fileName);

                // Update the student card format record to remove background image
                StudentIdCardFormat::updateForCollege($collegeId, ['background_image' => null]);
            }
        }

        $result = ['status' => 'success', 'message' => 'Background image removed successfully.'];
        echo json_encode($result);
        exit;
    }

    public function bulkGenerateStudentCards(Request $request)
    {
        try {
            $studentIds = $request->input('student_ids', []);
            $collegeId = Auth::user()->college_id;

            if (empty($studentIds)) {
                return $this->errorResponse('No students selected for ID card generation.', 'data', [], 400);
            }

            // Get student card settings
            $settings = StudentIdCardFormat::getForCollege($collegeId);
            if (! $settings) {
                $settings = StudentIdCardFormat::getDefaults();
            } else {
                $settings = $settings->toArray();
            }

            $generatedCards = [];
            $failedCards = [];

            // Create temporary directory for bulk generation
            $tempDir = storage_path('app/temp/bulk_student_cards_'.time());
            if (! File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            foreach ($studentIds as $studentId) {
                try {
                    $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentDetail($studentId);

                    if (! isset($arrStudentInfo) || empty($arrStudentInfo)) {
                        $failedCards[] = [
                            'student_id' => $studentId,
                            'reason' => 'Student information not found',
                        ];

                        continue;
                    }

                    $profilePicPath = Config::get('constants.displayProfilePicture');
                    $filePath = Config::get('constants.uploadFilePath.StudentPics');
                    $profilePicDestinationPath = Helpers::changeRootPath($filePath, $studentId);
                    $profilePicPath = $profilePicDestinationPath['view'];
                    $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                    $destinationPath = Helpers::changeRootPath($filePath);
                    $logoPath = $destinationPath['view'];

                    $data['clg_logo'] = $logoPath.$arrStudentInfo[0]->college_logo;
                    $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                    $data['arrStudentInfo'] = $arrStudentInfo[0];
                    $data['settings'] = $settings;

                    $profilePicFullPath = public_path($profilePicPath.$arrStudentInfo[0]->profile_picture);
                    if (File::exists($profilePicFullPath)) {
                        $data['profile_pic'] = $profilePicPath.$arrStudentInfo[0]->profile_picture;
                    } else {
                        $data['profile_pic'] = 'dist/img/avatar6.png';
                    }

                    $pdf = App::make('dompdf.wrapper');
                    $pdf->loadView('v2.sadmin.student.pages.student-id-card-pdf', $data);
                    $pdf->setPaper([0, 0, 243, 153], 'landscape'); // 3.375" x 2.125" in points

                    // Generate filename
                    $studentName = trim(($arrStudentInfo[0]->first_name ?? '').'_'.($arrStudentInfo[0]->family_name ?? ''));
                    $studentNumber = $arrStudentInfo[0]->generated_stud_id ?? $studentId;
                    $fileName = 'student_card_'.$studentNumber.'_'.$studentName.'.pdf';
                    $fileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', $fileName); // Clean filename

                    $filePath = $tempDir.'/'.$fileName;
                    $pdf->save($filePath);

                    $generatedCards[] = [
                        'student_id' => $studentId,
                        'student_name' => $arrStudentInfo[0]->first_name.' '.$arrStudentInfo[0]->family_name,
                        'student_number' => $studentNumber,
                        'file_path' => $filePath,
                        'file_name' => $fileName,
                    ];

                } catch (\Exception $e) {
                    $failedCards[] = [
                        'student_id' => $studentId,
                        'reason' => 'Error generating card: '.$e->getMessage(),
                    ];
                }
            }

            // Create ZIP file if multiple cards generated
            if (count($generatedCards) > 1) {
                $zipFileName = 'bulk_student_cards_'.date('Y-m-d_H-i-s').'.zip';
                $zipPath = storage_path('app/temp/'.$zipFileName);

                $zip = new \ZipArchive;
                if ($zip->open($zipPath, \ZipArchive::CREATE) === true) {
                    foreach ($generatedCards as $card) {
                        $zip->addFile($card['file_path'], $card['file_name']);
                    }
                    $zip->close();

                    // Clean up individual PDF files
                    foreach ($generatedCards as $card) {
                        if (File::exists($card['file_path'])) {
                            File::delete($card['file_path']);
                        }
                    }

                    // Remove temporary directory
                    if (File::exists($tempDir)) {
                        File::deleteDirectory($tempDir);
                    }

                    $downloadUrl = url('storage/temp/'.$zipFileName);

                    return $this->successResponse('Bulk student ID cards generated successfully.', 'data', [
                        'download_url' => $downloadUrl,
                        'zip_path' => $zipPath,
                        'generated_count' => count($generatedCards),
                        'failed_count' => count($failedCards),
                        'generated_cards' => $generatedCards,
                        'failed_cards' => $failedCards,
                    ]);
                } else {
                    return $this->errorResponse('Failed to create ZIP file.', 'data', [], 500);
                }
            } elseif (count($generatedCards) === 1) {
                // Single card - return direct download
                $card = $generatedCards[0];
                $downloadUrl = url('storage/temp/'.basename($card['file_path']));

                return $this->successResponse('Student ID card generated successfully.', 'data', [
                    'download_url' => $downloadUrl,
                    'file_path' => $card['file_path'],
                    'generated_count' => 1,
                    'failed_count' => count($failedCards),
                    'generated_cards' => $generatedCards,
                    'failed_cards' => $failedCards,
                ]);
            } else {
                return $this->errorResponse('No student ID cards could be generated.', 'data', [
                    'failed_cards' => $failedCards,
                ], 400);
            }

        } catch (\Exception $e) {
            return $this->errorResponse('Error during bulk generation: '.$e->getMessage(), 'data', [], 500);
        }
    }

    public function studentResetPasswordEmail(Request $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        try {

            $status = Password::sendResetLink([
                'email' => $student->email,
            ]);
            // $returnData['type'] = $status === Password::RESET_LINK_SENT ? 'alert-success' : 'alert-error';
            // $returnData['message'] = $status === Password::RESET_LINK_SENT ? 'Password reset link sent to user email' : __($status);

            if ($status === Password::RESET_LINK_SENT) {
                return $this->successResponse('Password reset link sent to user email', 'data', '');
            } else {
                return $this->errorResponse(__($status), 'data', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getTcsiStudentDetail(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getTcsiStudentDetailData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveStudentTcsiDetails(SaveStudentTcsiDetailsRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentTcsiDetailsData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function studentReActivationEmail(Request $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        try {
            $user = Users::where('username', $student->generated_stud_id)->first();
            if ($user) {
                $user->sendEmailVerificationNotification();

                return $this->successResponse('Student Re-activation email sent successfully', 'data', '');
            } else {
                return $this->errorResponse('Something want wrong', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getSaHelpFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getSaHelpFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveSaHelpData(SaveStudentSaHelpRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveSaHelpDetailData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getOsHelpFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getOsHelpFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveOsHelpData(SaveStudentOsHelpRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveOsHelpDetailData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getStudentCourseInformationFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentCourseInformationFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getStudentCourseInformationFromCourseId(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getStudentCourseInformationFromCourseIdData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getNarrowTypeList(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getNarrowTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function getSubNarrowTypeList(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getSubNarrowTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveStudentCourseInformation(SaveStudentCourseInformationRequest $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveStudentCourseInformationData($request->DTO());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getDisabilityInformationFormData(Request $request)
    {
        $arrStudentInfo = $this->studentProfileMoreActionServicess->getDisabilityInformationFormData($request->input());

        return $this->successResponse('Data Found', 'data', $arrStudentInfo);
    }

    public function saveDisabilityInformationDetails(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->saveDisabilityInformationDetailsData($request->input(), $request->user()->id);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getOShelpInformationFromCourseId(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getOShelpInformationFromCourseIdData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    /* Manage Course Variant */
    public function getCourseVariantList(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseVariantLogs(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantLogData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseVariantDetail(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getCourseVariantDetailData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function saveCourseVariant(SaveCourseVariantRequest $request, SaveCourseVariantProcess $process)
    {
        // $data = $this->studentProfileMoreActionServicess->saveCourseVariantData($request);
        $requestData = $request->DTO();
        $postData = $requestData->toArray();

        DB::beginTransaction();
        try {
            $processData = [
                'studentId' => $postData['student_id'],
                'studCourseId' => $postData['student_course_id'],
                'newStatus' => $postData['course_status'],
                'courseVariantData' => $postData,
            ];

            // Run the course variant process
            $result = $process->run($processData);

            if ($result['status'] == 'success') {
                DB::commit();

                return $this->successResponse('Course Variant details saved successfully.', 'data', $result);
            } else {
                DB::rollBack();

                return $this->errorResponse($result['message'], 'data', $result);
            }
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), 'data', []);
        }
    }

    public function updateCourseVariant(SaveCourseVariantRequest $request, SaveCourseVariantProcess $process)
    {
        $requestData = $request->DTO();
        $postData = $requestData->toArray();

        try {
            $processData = [
                'is_update' => true,
                'course_variant_id' => $request->input('id'),
                'studentId' => $postData['student_id'],
                'studCourseId' => $postData['student_course_id'],
                'newStatus' => $postData['course_status'],
                'courseVariantData' => $postData,
            ];

            $result = $process->run($processData);
            if ($result['status'] == 'success') {
                DB::commit();

                return $this->successResponse('Course variant updated successfully', 'data', $result);
            } else {
                DB::rollBack();

                return $this->errorResponse($result['message'], 'data', $result);
            }

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', []);
        }
    }

    public function printCourseVariant($courseVariantId)
    {
        // $courseVariantId = $request->input('id');

        if ($courseVariantId > 0) {
            $courseVariantInfo = $this->studentProfileMoreActionServicess->printCourseVariantData($courseVariantId);
            if ($courseVariantInfo) {
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $data['clg_logo'] = $destinationPath['view'].$courseVariantInfo->logo;
                $data['arrCourseVariantInfo'] = $courseVariantInfo;
                $pdf = App::make('dompdf.wrapper');
                $pdf->loadView('v2.sadmin.student.pages.course-variant-pdf-view', $data);

                return $pdf->download('invoice.pdf');
            } else {
                return $this->errorResponse('Data not found', 'data', [], 200);
            }
        }

        return $this->errorResponse('ID not found', 'data', [], 200);
    }

    /* TCSI Credit Offer */
    public function getTcsiCreditOfferData(Request $request)
    {
        $data = $this->studentProfileMoreActionServicess->getTcsiCreditOfferData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTcsiCreditOfferInfo(Request $request)
    {
        $res = $this->studentProfileMoreActionServicess->getTcsiCreditOfferInfo($request->input('id'));

        return $this->successResponse('Data found successfully', 'data', $res);
    }

    public function saveTcsiCreditOfferData(TcsiStudentCreditOfferRequest $request)
    {
        $res = $this->studentProfileMoreActionServicess->saveTcsiCreditOfferData($request->DTO());
        if ($res['status'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res);
        } else {
            return $this->errorResponse($res['message'], 'data', $res);
        }
    }

    public function updateTcsiCreditOfferData(TcsiStudentCreditOfferRequest $request)
    {
        $res = $this->studentProfileMoreActionServicess->updateTcsiCreditOfferData($request->DTO());
        if ($res['status'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res);
        } else {
            return $this->errorResponse($res['message'], 'data', $res);
        }
    }

    public function deleteTcsiCreditOfferData(Request $request)
    {
        $res = $this->studentProfileMoreActionServicess->deleteTcsiCreditOfferData($request->input('id'));

        return $this->successResponse('Deleted Successfully', 'data', $res);
    }
}
