<template>
    <div class="inline-flex flex-col items-start justify-start gap-1">
        <div :class="labelClass">{{ label }}</div>
        <div v-if="isBatchDuration">
            <FormatDate
                :date="computeDate('start')"
                :endDate="computeDate('end')"
                :infix="'To'"
                :formatType=dateFormat
            />
        </div>
        <div :class="valueClass" v-else>{{ value }}</div>
    </div>
</template>

<script setup>
import { defineProps, computed } from "vue";
import FormatDate from "@spa/components/FormatDate.vue";
import { DEFAULT_DATE_FORMAT } from "@spa/helpers/constants.js";

const props = defineProps({
    label: {
        type: String,
        required: true,
    },
    value: {
        type: [String, Number],
        required: true,
    },
    labelClass: {
        type: String,
        default: "text-gray-900 text-xs font-medium font-roboto leading-none",
    },
    valueClass: {
        type: String,
        default:
            "text-gray-700 text-xs font-normal font-roboto leading-tight tracking-wide",
    },
    loader: Boolean,
});

const isBatchDuration = computed(() => props.label === "Batch Duration");
const dateFormat = DEFAULT_DATE_FORMAT;

const computeDate = (type) => {
    if (typeof props.value === "string") {
        return type === "start"
            ? props.value.split(":")[0]
            : props.value.split(":")[1];
    }
    return null;
};
</script>

<style scoped>
/* Add your CSS classes and styles here */
</style>
