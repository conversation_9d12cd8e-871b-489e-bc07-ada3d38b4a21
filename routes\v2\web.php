<?php

use App\Http\Controllers\Login\BillingController;
use App\Http\Controllers\ScriptController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Laravel\Fortify\Http\Controllers\ConfirmablePasswordController;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

Route::middleware([
    'web',
    'guest',
    /* InitializeTenancyByDomain::class, */
    PreventAccessFromCentralDomains::class,
])->group(function () {

    if (config('features.xero')) {
        Route::post('webhooks/xero', 'XeroWebhookController@capture')->name('xero-webhook');
    }

    Route::post('galaxy/webhook/test', function () {
        info('galaxy webhook test', [request()->all()]);

        return response()->json(['message' => 'Webhook received']);
    });

    /* api to verify student email and reset password for students registring for short course */
    Route::get('shortcourse/verify/{code}', ['as' => 'shortcourse.verify.student',     'uses' => 'v3\api\StudentApiController@verifyStudentEmail']);

    Route::get('shortcourse/reset/{code}', ['as' => 'shortcourse.reset.student',     'uses' => 'v3\api\StudentApiController@resetStudentPassword']);
    Route::post('shortcourse/reset/{code}', ['as' => 'shortcourse.reset.student.update',     'uses' => 'v3\api\StudentApiController@updateStudentPassword']);
    Route::get('shortcourse/new-password/{token}', ['as' => 'shortcourse.show.passwordform',     'uses' => 'v3\api\StudentApiController@showPasswordResetForm']);
    Route::post('shortcourse/new-password/{token}', ['as' => 'shortcourse.update.password',     'uses' => 'v3\api\StudentApiController@updateStudentPassword']);

    /* short course routes ends */

    if (! config('features.jetstream')) {
        Auth::routes(['login' => false, 'register' => false]);
        Route::match(['get', 'post'], 'login', ['as' => 'user_login',                  'uses' => 'v2\sadmin\LoginController@login']);
        Route::match(['get', 'post'], 'forgot_password', ['as' => 'forgot_password',             'uses' => 'v2\sadmin\LoginController@forgotPassword']);
        Route::match(['get', 'post'], 'reset-password/{param?}', ['as' => 'reset-password',              'uses' => 'v2\sadmin\LoginController@resetPassword']);
        Route::match(['get', 'post'], 'invitation-link/{param?}', ['as' => 'invitation-link',             'uses' => 'v2\sadmin\LoginController@invitationLink']);
    } else {
        Route::get('user-login', function () {
            return redirect()->route('login');
        })->name('user_login');
    }

    Route::get('impersonate-user/{uuid}', 'ScriptController@impersonateWithUUID')->name('tenant.impersonate-user');

});

Route::get('verify/certificate/{id}', ['as' => 'verify.certificate', 'uses' => 'Spa\CertificateVerificationController@verify']);

if (config('features.tenant_stripe')) {
    require base_path('src/Domains/Customers/Billing/routes/web.php');
}

// Route::match(['get', 'post'],   'login',                                  ['as' => 'user_login',                      'uses' => 'v2\sadmin\LoginController@login']);
// Route::match(['get', 'post'],   'forgot_password',                        ['as' => 'forgot_password',                 'uses' => 'v2\sadmin\LoginController@forgotPassword']);
// Route::match(['get', 'post'],   'reset-password/{param?}',                ['as' => 'reset-password',                  'uses' => 'v2\sadmin\LoginController@resetPassword']);

Route::match(['get'], 'organisation-setup', ['as' => 'organisation-setup',              'uses' => 'v2\sadmin\StudentOrientationController@organisation']);
Route::match(['get'], 'student-orientation', ['as' => 'student-orientation',             'uses' => 'v2\sadmin\StudentOrientationController@index']);

$userPrefix = '';
Route::group([
    'prefix' => $userPrefix,
    'middleware' => ['auth', 'verified', 'admin', /*  InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class],
], function () {

    Route::get('global/redirect/{id}/{type}', [\App\Http\Controllers\GlobalSearchRedirectController::class, 'handleRedirect'])->name('globalsearch.redirect');
    Route::get('script/test', [ScriptController::class, 'test']);
    Route::match(['get', 'post'], 'failed-attendance', ['as' => 'failed-attendance',               'uses' => 'v2\sadmin\StudentController@failedAttendanceJobs']);
    // Route::match(['get', 'post'],     'get-cache-data/{key?}',            ['as' => 'get-cache-data',                  'uses' => 'v2\sadmin\StudentController@getCacheData']);

    Route::match(['get'], 'student-orientation', ['as' => 'student-orientation',             'uses' => 'v2\sadmin\StudentOrientationController@index']);
    Route::match(['get'], 'search-student', ['as' => 'search-student',                  'uses' => 'v2\sadmin\StudentController@index']);

    if (config('features.scout_beta_search')) {
        Route::match(['get'], 'search-student-scout', ['as' => 'search-student-scout',            'uses' => 'v2\sadmin\StudentController@scout']);
        Route::match(['get', 'post'], 'export-student-scout-data', ['as' => 'export-student-scout-data',       'uses' => 'v2\sadmin\StudentController@getExportStudentScoutData']);
    }

    Route::match(['get'], 'agent-students/{id}', ['as' => 'agent-students',                  'uses' => 'v2\sadmin\StudentController@agentStudents']);
    Route::match(['get'], 'student/{action?}/{id?}', ['as' => 'student',                         'uses' => 'v2\sadmin\StudentController@studentAction']);
    Route::match(['get', 'post'], 'communication/{page}/{id?}', ['as' => 'communication',                   'uses' => 'v2\sadmin\CommunicationController@index']);

    if (config('features.jetstream')) {
        Route::get('confirm-password', function () {
            return view('jetstream.auth.confirm-password');
        })->name('password-confirm');

        // Route::post('confirm-password', [ConfirmablePasswordController::class, 'store'])
        // ->middleware([config('fortify.auth_middleware', 'auth').':'.config('fortify.guard')])
        // ->name('password-confirm');
    }

    /* Xero Setup */
    if (config('features.xero')) {
        Route::match(['get', 'post'], 'xero-landing', ['as' => 'xero-landing',                    'uses' => '\Domains\Xero\Controllers\XeroController@landing']);
        Route::match(['get', 'post'], 'xero-setup', ['as' => 'xero-setup',                      'uses' => '\Domains\Xero\Controllers\XeroController@index'])->middleware(['password.confirm:password-confirm']);
        Route::match(['get', 'post'], 'xero/connect', ['as' => 'xero-connect',                    'uses' => '\Domains\Xero\Controllers\XeroController@connect']);
        Route::match(['get', 'post'], 'xero/disconnect', ['as' => 'xero-disconnect',                 'uses' => '\Domains\Xero\Controllers\XeroController@disconnect']);
    }

    Route::match(['get'], 'online-public-application', ['as' => 'online-public-application',       'uses' => 'v2\sadmin\OnlineApplicationController@index']);

    Route::view('kitchen-sink', 'v2.sadmin.kitchensink.index');
    //  New Dashboard Routes
    // Route::view('dashboard-v2', 'v2.dashboard.dashboard');
    Route::match(['get', 'post'], 'admin-dashboard', ['as' => 'admin-dashboard',                           'uses' => 'v2\sadmin\DashboardController@index']);

    if (config('features.testing_ground')) {
        Route::get('testing-grounds', 'TestingGroundController@ground')->name('testing.ground');
        Route::get('auth/callback', 'TestingGroundController@authCallback');
    }

    Route::match(['get', 'post'], 'run-patch', ['as' => 'run-patch',                           'uses' => 'v2\sadmin\RunPatchController@runPatch']);
    Route::match(['get', 'post'], 'avetmiss-import', ['as' => 'avetmiss-import',                     'uses' => 'v2\sadmin\AvetmissImportController@uploadFile'])->middleware(['auth.onlySAdmin']);
    Route::match(['get', 'post'], 'user-onboard', ['as' => 'user-onboard',                    'uses' => 'v2\sadmin\OnboardController@dashboard']);
    Route::match(['get', 'post'], 'settings', ['as' => 'user-settings',                   'uses' => 'v2\sadmin\OnboardController@settingDashboard'])->middleware(['auth.onlySAdmin']);
    Route::match(['get', 'post'], 'onboard-setup/{id?}', ['as' => 'onboard-setup',                   'uses' => 'v2\sadmin\OnboardController@index']);
    Route::match(['get', 'post'], 'view-campus/{id?}/{venue_id?}', ['as' => 'view-campus',                     'uses' => 'v2\sadmin\OnboardController@campusView']);

    // Setting
    Route::match(['get', 'post'], 'general-info', ['as' => 'general-info',                    'uses' => 'v2\sadmin\OnboardSettingController@generalInfo']);
    Route::match(['get', 'post'], 'training-organisation', ['as' => 'training-organisation',           'uses' => 'v2\sadmin\OnboardSettingController@trainingOrg']);
    Route::match(['get', 'post'], 'main-location', ['as' => 'main-location',                   'uses' => 'v2\sadmin\OnboardSettingController@mainLocation']);
    Route::match(['get', 'post'], 'bank-details', ['as' => 'bank-details',                    'uses' => 'v2\sadmin\OnboardSettingController@bankDetails']);
    Route::match(['get', 'post'], 'vsl-info', ['as' => 'vsl-info',                        'uses' => 'v2\sadmin\OnboardSettingController@VSLinfo']);
    Route::match(['get', 'post'], 'manage-section', ['as' => 'manage-section',                  'uses' => 'v2\sadmin\OnboardSettingController@manageSection']);
    Route::match(['get', 'post'], 'offer-letter-checklist', ['as' => 'offer-letter-checklist',          'uses' => 'v2\sadmin\OnboardSettingController@offerLetter']);
    Route::match(['get', 'post'], 'agent-document', ['as' => 'agent-document',                  'uses' => 'v2\sadmin\OnboardSettingController@agentsDocument']);
    Route::match(['get', 'post'], 'offer-tracking', ['as' => 'offer-tracking',                  'uses' => 'v2\sadmin\OnboardSettingController@offerTracking']);
    Route::match(['get', 'post'], 'custom-checklist', ['as' => 'custom-checklist',                'uses' => 'v2\sadmin\OnboardSettingController@customChecklist']);
    Route::match(['get', 'post'], 'cvr-info', ['as' => 'cvr-info',                        'uses' => 'v2\sadmin\OnboardSettingController@CVRlist']);
    Route::match(['get', 'post'], 'smtp-setup', ['as' => 'smtp-setup',                      'uses' => 'v2\sadmin\OnboardSettingController@smtpSetup']);
    Route::match(['get', 'post'], 'course-types', ['as' => 'course-types',                    'uses' => 'v2\sadmin\OnboardSettingController@courseType']);
    Route::match(['get', 'post'], 'country-list', ['as' => 'country-list',                    'uses' => 'v2\sadmin\OnboardSettingController@countryList']);
    Route::match(['get', 'post'], 'gte-document', ['as' => 'gte-document',                    'uses' => 'v2\sadmin\OnboardSettingController@getGteDocument']);
    Route::match(['get', 'post'], 'added-services-fee-list', ['as' => 'added-services-fee-list',         'uses' => 'v2\sadmin\OnboardSettingController@getAddedServicesFeeList']);
    Route::match(['get', 'post'], 'language-list', ['as' => 'language-list',                   'uses' => 'v2\sadmin\OnboardSettingController@getLanguageView']);
    Route::match(['get', 'post'], 'agent-status-view', ['as' => 'agent-status-view',               'uses' => 'v2\sadmin\OnboardSettingController@getAgentStatus']);
    Route::match(['get', 'post'], 'competency-result-grade', ['as' => 'competency-result-grade',         'uses' => 'v2\sadmin\OnboardSettingController@getCompetencyResultGrade']);
    Route::match(['get', 'post'], 'oshc-infos', ['as' => 'oshc-infos',                      'uses' => 'v2\sadmin\OnboardSettingController@getOSHCInfo']);
    Route::match(['get', 'post'], 'failed-jobs', ['as' => 'failed-jobs',                     'uses' => 'v2\sadmin\OnboardSettingController@failedJobs']);
    Route::match(['get', 'post'], 'intervention-strategy-view', ['as' => 'intervention-strategy-view',      'uses' => 'v2\sadmin\OnboardSettingController@getInterventionStrategy']);
    Route::match(['get', 'post'], 'certificate-id-format-view', ['as' => 'certificate-id-format-view',      'uses' => 'v2\sadmin\OnboardSettingController@getCertificateIdFormate']);
    Route::match(['get', 'post'], 'assessment-date-extension', ['as' => 'assessment-date-extension',       'uses' => 'v2\sadmin\OnboardSettingController@getAssessmentDueDateExtention']);
    Route::match(['get', 'post'], 'invoice-settings', ['as' => 'invoice-settings',                'uses' => 'v2\sadmin\OnboardSettingController@invoiceSetting']);
    Route::match(['get', 'post'], 'agent-email-template-setting-view', ['as' => 'agent-email-template-setting-view', 'uses' => 'v2\sadmin\OnboardSettingController@getAgentEmailTemplateSetting']);
    Route::match(['get', 'post'], 'letter-setting-view', ['as' => 'letter-setting-view',             'uses' => 'v2\sadmin\OnboardSettingController@getAgentLetterSetting']);
    Route::match(['get', 'post'], 'student-id-card-format', ['as' => 'student-id-card-format',       'uses' => 'v2\sadmin\OnboardSettingController@getStudentIdCardFormat']);
    Route::match(['get', 'post'], 'enrollment-fees', ['as' => 'enrollment-fees',                 'uses' => 'v2\sadmin\OnboardSettingController@enrollmentFees']);
    Route::match(['get', 'post'], 'offer-labels', ['as' => 'offer-labels',                    'uses' => 'v2\sadmin\OnboardSettingController@offerLabel']);
    Route::match(['get', 'post'], 'schedule-type', ['as' => 'schedule-type',                    'uses' => 'v2\sadmin\OnboardSettingController@scheduleType']);

    // onboard-setting
    Route::match(['get', 'post'], 'contract-code', ['as' => 'contract-code',                   'uses' => 'v2\sadmin\OnboardSettingController@contractCode']);
    Route::match(['get', 'post'], 'contract-funding-source', ['as' => 'contract-funding-source',         'uses' => 'v2\sadmin\OnboardSettingController@contractFundingSource']);
    Route::match(['get', 'post'], 'course-site', ['as' => 'course-site',                     'uses' => 'v2\sadmin\OnboardSettingController@courseSite']);
    Route::match(['get', 'post'], 'elearning-link-list', ['as' => 'elearning-link-list',             'uses' => 'v2\sadmin\OnboardSettingController@learningLinkList']);
    Route::match(['get', 'post'], 'failed-email', ['as' => 'failed-email',                    'uses' => 'v2\sadmin\OnboardSettingController@failedEmail']);
    Route::match(['get', 'post'], 'manage-report', ['as' => 'manage-report',                   'uses' => 'v2\sadmin\OnboardSettingController@manageReport']);
    Route::match(['get', 'post'], 'bank-list', ['as' => 'bank-list',                       'uses' => 'v2\sadmin\OnboardSettingController@bankList']);
    Route::match(['get', 'post'], 'holiday-list', ['as' => 'holiday-list',                    'uses' => 'v2\sadmin\OnboardSettingController@holiDayList']);
    Route::match(['get', 'post'], 'course-template', ['as' => 'course-template',                 'uses' => 'v2\sadmin\OnboardSettingController@courseTemplate']);
    Route::match(['get', 'post'], 'sms-template-list', ['as' => 'sms-template-list',               'uses' => 'v2\sadmin\OnboardSettingController@smsTemplateList']);
    Route::match(['get', 'post'], 'email-template-list', ['as' => 'email-template-list',             'uses' => 'v2\sadmin\OnboardSettingController@emailTemplateList']);
    Route::match(['get', 'post'], 'checklist', ['as' => 'checklist',                       'uses' => 'v2\sadmin\OnboardSettingController@checkList']);
    Route::match(['get', 'post'], 'letter-template-list', ['as' => 'letter-template-list',            'uses' => 'v2\sadmin\OnboardSettingController@letterTemplateList']);
    Route::match(['get', 'post'], 'failed-transactions', ['as' => 'failed-transactions',             'uses' => 'v2\sadmin\OnboardSettingController@failedTransaction']);

    Route::match(['get', 'post'], 'student-id-formate-beta', ['as' => 'student-id-formate-beta',         'uses' => 'v2\sadmin\OnboardSettingController@studentIdFormate']);
    Route::match(['get', 'post'], 'global-queue', ['as' => 'global-queue',                    'uses' => 'v2\sadmin\OnboardSettingController@globalQueue']);

    // VSL Info
    Route::match(['get', 'post'], 'communication/{page}/{id?}', ['as' => 'communication',                   'uses' => 'v2\sadmin\CommunicationController@index']);
    // Route::match(['get'],           'student-profile-view/{id}',        ['as' => 'student-profile-view',            'uses' => 'v2\sadmin\StudentController@viewProfile']);
    Route::match(['get'], 'student-profile-view/{id}', ['as' => 'student-profile-view',            'uses' => 'v2\sadmin\StudentController@viewProfile']);

    Route::match(['get', 'post'], 'preview-student-offer-letter/{courseID}/{id}/{studentCourseId}/{download?}', ['as' => 'preview-student-offer-letter',        'uses' => 'v2\sadmin\StudentController@offerLetterPreview']);

    Route::match(['get'], 'manage-offer', ['as' => 'manage-offer',                    'uses' => 'v2\sadmin\StudentGteProcessController@manageOffer']);
    Route::match(['get'], 'gte-dashboard/{id}/{scid?}', ['as' => 'gte-dashboard',                   'uses' => 'v2\sadmin\StudentGteProcessController@gteDashboard']);

    // VPMS
    Route::match(['get', 'post'], 'placement-provider', ['as' => 'placement-provider',              'uses' => 'v2\sadmin\VpmsPlacementProviderController@placementProvider']);
    Route::match(['get', 'post'], 'student-placement', ['as' => 'student-placement',               'uses' => 'v2\sadmin\VpmsPlacementProviderController@studentPlacement']);

    // Timetable
    Route::match(['get', 'post'], 'timetable-beta', ['as' => 'timetable-beta',                  'uses' => 'v2\sadmin\TimeTableController@index']);
    Route::match(['get', 'post'], 'detail-timetable', ['as' => 'detail-timetable',                'uses' => 'v2\sadmin\TimeTableController@setTimetableDetailData']);
    Route::match(['get', 'post'],
        'print-attendance/{subject}/{semester}/{term}/{batch}/{week}', ['as' => 'print-attendance',                'uses' => 'v2\sadmin\TimeTableController@downloadAttendancePdf']
    );
    Route::match(['get', 'post'], 'timetable-import', ['as' => 'timetable-import',                'uses' => 'TimetableImportController@index']);

    // TCSI
    Route::match(['get', 'post'], 'tcsi-report', ['as' => 'tcsi-report',                     'uses' => 'v2\sadmin\TcsiController@reportGenerate']);

    // Onboard-setting for Course
    Route::match(['get', 'post'], 'courses-upfront-fee', ['as' => 'courses-upfront-fee',             'uses' => 'v2\sadmin\OnboardSettingController@coursesUpfrontFee']);
    Route::match(['get', 'post'], 'view-elicos-discount', ['as' => 'view-elicos-discount',            'uses' => 'v2\sadmin\OnboardSettingController@viewDiscountList']);
    Route::match(['get', 'post'], 'promotion-price', ['as' => 'promotion-price',                 'uses' => 'v2\sadmin\OnboardSettingController@coursePromotionPrice']);
    Route::match(['get', 'post'], 'courses-intake-date', ['as' => 'courses-intake-date',             'uses' => 'v2\sadmin\OnboardSettingController@coursesIntakeDate']);

    Route::match(['get', 'post'], 'get-read-csv-file', ['as' => 'get-read-csv-file',             'uses' => 'v2\sadmin\ReadCsvController@index']);

    Route::group(['middleware' => ['auth.owner']], function () {
        // Route::get('profile?tab=billing',        [\App\Http\Controllers\Login\UsersController::class, 'profile'])->name('billing');
        Route::get('billing/upgrade/{uuid}/{plan}', [BillingController::class, 'swapPlan'])->name('billing.swapPlan');
        Route::get('billing/cancel/{uuid}', [BillingController::class, 'cancelPlan'])->name('billing.cancelPlan');
        Route::get('billing/resume/{uuid}', [BillingController::class, 'resumePlan'])->name('billing.resumePlan');
    });

    Route::match(['get'], 'user-audit-log', ['as' => 'user-audit-log', 'uses' => 'v2\sadmin\UserAuditLogController@index']);

    Route::match(['get', 'post'], 'add-teams-condition', ['as' => 'add-teams-condition',             'uses' => 'v2\sadmin\OnboardSettingController@shortCourseTeamsCondition']);
    Route::match(['get', 'post'], 'add-privacy-policy', ['as' => 'add-privacy-policy',             'uses' => 'v2\sadmin\OnboardSettingController@shortCoursePrivacyPolicy']);

});

// Agent portal web
$agentPrefix = 'agent';
Route::group(['prefix' => 'agent', 'middleware' => ['web', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {
    Route::match(['get'], 'your-students', ['as' => 'your-students',                   'uses' => 'v2\agent\AgentStudentController@agentStudents']);
    Route::match(['get'], 'agent-commissions', ['as' => 'agent-commissions',               'uses' => 'v2\agent\AgentStudentController@agentCommission']);
    Route::match(['get'], 'agents-payment-history', ['as' => 'agents-payment-history',          'uses' => 'v2\agent\AgentPaymentController@paymentHistory']);
    Route::match(['get'], 'payment-advice', ['as' => 'payment-advice',                  'uses' => 'v2\agent\AgentPaymentController@paymentAdvice']);
    Route::match(['get'], 'agent-documents', ['as' => 'agent-documents',                 'uses' => 'v2\agent\AgentDocumentController@agentDocument']);
    Route::match(['get'], 'gte-dashboard/{id}', ['as' => 'agent-gte-dashboard',             'uses' => 'v2\agent\AgentGteProcessController@gteDashboard']);
    Route::match(['get'], 'download-gte-documents', ['as' => 'download-gte-documents',          'uses' => 'v2\agent\AgentGteProcessController@downloadGteDocuments']);
});

$common = '';
Route::group(['prefix' => '', 'middleware' => ['auth', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {
    Route::get('preview/{path}', [\App\Http\Controllers\FilesController::class, 'preview'])->where('path', '.*')->name('preview-files');
    Route::get('download/{path}', [\App\Http\Controllers\FilesController::class, 'download'])->where('path', '.*')->name('download-files');
});
Route::get('fdownload/{path}', [\App\Http\Controllers\FilesController::class, 'fDownload'])->where('path', '.*')->name('fdownload');
