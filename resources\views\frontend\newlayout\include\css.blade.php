<meta charset="UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
@if(!isset($spa))
<title>GALAXY 360 | @yield('title')</title>
@endif
<link rel="preconnect" href="https://fonts.gstatic.com" />
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet" />

@if(config('features.tailwind'))
{{-- <link rel="stylesheet" href="{{ asset(mix('build/css/tailwind.css')) }}"/> --}}
@vite(['resources/assets/sass/tailwind.css'])
@else
<link href="{{ asset('newdev/css/tailwind.css') }}" rel="stylesheet" />
@endif

<link rel="stylesheet" href="{{ asset('v2/css/menu.css') }}">

<!-- Bootstrap 3.3.6 -->
@if(!isset($hideBootstrap))
<link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
@endif
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
<!-- Theme style -->
<link rel="stylesheet" href="{{ asset('dist/css/jquery.dataTables.css') }}">
<link rel="stylesheet" href="{{ asset('dist/css/style.css') }}">
<!-- AdminLTE Skins. Choose a skin from the css/skins folder instead of downloading all of them to reduce the load. -->
<!-- iCheck for checkboxes and radio inputs -->
@if(!isset($spa))
<link rel="stylesheet" href="{{ asset('dist/css/AdminLTE.min.css') }}">
{{-- <link rel="stylesheet" href="{{ asset('v2/css/kendo.default-v2.min.css') }}"/> --}}
@endif
<link rel="stylesheet" href="{{ asset('plugins/iCheck/all.css') }}">

<!-- bootstrap datepicker -->
<link rel="stylesheet" href="{{ asset('plugins/datepicker/datepicker3.css') }}">

<!-- bootstrap timepicker -->
<link rel="stylesheet" href="{{ asset('plugins/timepicker/bootstrap-timepicker.min.css') }}">

<link rel="stylesheet" href="{{ asset('plugins/toastr/toastr.min.css') }}">
<!-- daterange picker -->
<link rel="stylesheet" href="{{ asset('plugins/daterangepicker/daterangepicker.css') }}">
 <!-- Select2 -->
<link rel="stylesheet" href="{{ asset('plugins/select2/select2.min.css') }}">

<link rel="stylesheet" href="{{ asset('dist/css/skins/_all-skins.min.css') }}">

<link rel="stylesheet" href="{{ asset('dist/css/selectric.css') }}">

<link rel="shortcut icon" type="image/png" href="{{ asset('dist/img/galaxy360-favicon.png') }}"/>

<link rel="stylesheet" href="https://unpkg.com/@progress/kendo-font-icons/dist/index.css" />
@if(!isset($spa))
<!-- kendo common css -->
<link rel="stylesheet" href="{{ asset('v2/css/kendo.common.min.css') }}">
<!-- New Kendo Theme -->
{{-- <link rel="stylesheet" href="{{ asset('v2/css/kendo.default-v2.min.css') }}"/> --}}
@endif
{{-- <link rel="stylesheet" href="{{ asset(mix('build/css/kendo-theme.css'))}}"/> --}}
@vite(['resources/assets/sass/kendo-theme.scss'])
<!--For Autocomplete-->
<link rel="stylesheet" href="{{ asset('dist/css/jquery-ui.css') }}">

<link rel="stylesheet" href="{{ asset('dist/css/newdesigntailwind.css') }}">
<!-- -->
<link rel="stylesheet" href="{{ asset('v2/css/common.css') }}">
{{-- <script defer src="https://cdn.jsdelivr.net/npm/@alpinejs/collapse@3.x.x/dist/cdn.min.js"></script>
<script defer
                src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script> --}}

@if(!empty($plugincss))
@foreach ($plugincss as $pcss)
<link href="{{ asset('plugins') }}/{{ $pcss }}" rel="stylesheet">
@endforeach
@endif

@if(!empty($css))
@foreach ($css as $ccss)
@if ($ccss != '')
    <link href="{{ asset('css/frontend') }}/{{ $ccss }}" rel="stylesheet">
@endif
@endforeach
@endif

@if (config('features.sidebar'))
{{-- <link rel="stylesheet" href="{{ asset(mix('build/css/main.css')) }}"/> --}}
@vite(['resources/assets/sass/main.scss'])
@endif
<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
<!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->
<script>
    var site_url = "{{ url('/') }}/";
    var SMSApiKey = "{{ Config::get('constants.SMSApiKey') }}";
</script>
<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
<!--[if lt IE 9]>
<script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->
@livewireStyles