// -----------------------------------------------------------------------------
// This file contains all styles related to the header of the site/application.
// -----------------------------------------------------------------------------
.header {
    .k-autocomplete {
        width: fit-content;
    }
    #user-menu svg {
        transition: all 300ms ease-in-out;
    }
    .open #user-menu svg {
        transform: rotate(180deg);
        transition: all 300ms ease-in-out;
    }

    #globalSearchResult {
        & #searchTabStrip {
            .k-tabstrip-items .k-link {
                padding-inline: 0.25rem !important;
            }
            ul {
                gap: 2rem;
                border-bottom: 1px solid var(--color-gray-200);
            }

            // &.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item.k-state-active {
            //   border-bottom: 0 !important;
            //   position: relative;
            //   &::after {
            //     content: "";
            //     position: absolute;
            //     width: 100%;
            //     height: 3px;
            //     bottom: 3px;
            //     background-color: var(--color-primary-blue-500);
            //   }
            // }
        }

        .selectedSearch {
            padding-inline: 0.5rem;
            border-radius: 0.5rem;
        }
    }
}

#activityTabStrip,
#activityTabStripActivityLogTab {
    &.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item.k-state-active {
        border-bottom-color: transparent;
    }
}

.tw-sidebar {
    transition: all 300ms ease-in-out;
    &__expanded {
        .tw-menu-list {
            padding-inline: 0.5rem !important;
        }
        .tw-menu-link {
            padding: 1rem 0.75rem 1rem 0.5rem !important;
            @media (screen(lg)) {
                padding: 0.5rem 0.75rem 0.5rem 0.5rem !important;
            }
        }
        .tw-toggle-icon {
            display: block;
        }
    }
    &__collapsed {
        .tw-menu-list {
            padding-inline: 1.25rem !important;
        }
        .tw-menu-link {
            padding: 0.5rem !important;
        }
        .tw-toggle-icon {
            display: none;
        }
        .tw-menu-divider {
            display: none;
        }
    }

    &__toggle-btn {
        &:hover {
            background-color: var(--color-primary-blue-500) !important;
            box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px !important;
        }
    }

    .tw-menu-list {
        margin-bottom: 0;
    }

    .tw-menu-link {
        .icon-wrapper {
            color: var(--color-gray-400);
        }

        .tw-menu-link-text,
        .tw-menu-toggle {
            color: var(--color-gray-300);
        }
        &.active {
            background-color: var(--color-primary-blue-500);
            color: var(--color-white);

            .icon-wrapper {
                color: var(--color-primary-blue-50);
            }

            .tw-menu-link-text,
            .tw-menu-toggle {
                color: var(--color-white);
            }

            &:hover {
                background-color: var(--color-primary-blue-500);
            }
        }

        &:hover {
            background-color: var(--color-gray-700);
        }
    }
}

.tw-transparent-header {
    #release-notes {
        color: var(--color-gray-800);
        background: var(--color-gray-200);
        &:hover {
            background: var(--color-primary-blue-100);
        }
    }
}
