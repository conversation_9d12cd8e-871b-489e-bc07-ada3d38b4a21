let myEditor11;
let myEditor22;
let myEditor33;
let FILTER_DATA = {};
let defaultWhitelist = {};

var tagify1; // for Email
var tagify2; // for SMS
var tagify3; // for Letter

var emailSubject = '';
var emailContent = '';
var letterSubject = '';
var letterContent = '';
var existEmailAttachmentId = [];
var existLetterAttachmentId = [];
var confirmEmail = false;
var confirmLatter = false;
var scoutFilterFunctionCalled = true;
var whitelistForReplyToEmail = {};

let isSelectAll = false;

var studNameListForEmailId = '#student_name_email_list';
var studNameListForLetterId = '#student_name_letter_list';
var issueLetterStudentModalId = '#issueLetterStudentModal';
var studentsFilterPanelBarId = '#studentsFilterPanelbar';
var letterDiscardConfirmation = '#letterDiscardConfirmation';
let attachedFiles = [];
$(document).ready(function () {
    CKEDITOR.ClassicEditor.create(document.querySelector('#email_content'), {
        ckfinder: {
            uploadUrl: site_url + 'api/upload-file-email-text-editor',
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: window.tagJson,
                    minimumCharacters: 0,
                },
            ],
        },
        removePlugins: [
            'RealTimeCollaborativeComments',
            'RealTimeCollaborativeTrackChanges',
            'RealTimeCollaborativeRevisionHistory',
            'PresenceList',
            'Comments',
            'TrackChanges',
            'TrackChangesData',
            'RevisionHistory',
            'Pagination',
            'WProofreader',
            'MathType',
        ],
    })
        .then((editor) => {
            editor.ui.view.editable.element.style.height = '300px';
            myEditor33 = editor;
        })
        .catch((error) => {
            console.error(error);
        });

    var gridID = '#studentList';
    var selectedStudent = [];
    var filterLoad = true;
    $(document).find('html').addClass('overflow-hidden');

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    $.ajaxSetup({
        headers: { Authorization: api_token },
    });

    $('body').on('keyup', '.sidebarSearchForType ', function (e) {
        let searchText = $(this).val();
        let action = $(this).closest('li').siblings('li');
        let studentType = $(this).data('value');
        action.each(function () {
            if ($(this).hasClass(studentType)) {
                if (searchText.length > 0) {
                    if (
                        $(this)
                            .find('label')
                            .data('val')
                            .toUpperCase()
                            .includes(searchText.toUpperCase())
                    )
                        $(this).fadeIn();
                    else $(this).fadeOut();
                } else {
                    $(this).fadeIn();
                }
            }
        });
    });

    $('body').on('keyup', '.sidebarSearch', function (e) {
        let searchText = $(this).val();
        let action = $(this).closest('li').siblings('li');
        action.each(function () {
            if (searchText.length > 0) {
                if (
                    $(this)
                        .find('label')
                        .data('val')
                        .toUpperCase()
                        .includes(searchText.toUpperCase())
                )
                    $(this).fadeIn();
                else $(this).fadeOut();
            } else {
                $(this).fadeIn();
            }
        });
    });

    $('body').on('change', '#filterKey_list', function (e) {
        if ($(this).val() == 'between') {
            $('#intake_end_list').closest('li').show();
        } else {
            $('#intake_end_list').closest('li').hide();
        }
    });

    $('body').on('click', '.remove_student_intake', function (e) {
        let intakeId = $(this).data('intake-id');
        let inputId = `2_checkbox_${intakeId}`;
        let index = inputIds.indexOf(inputId);

        // If the ID is found in the array
        if (index !== -1) {
            // Remove the ID from the array
            inputIds.splice(index, 1);
        }

        $(this).closest('li').remove();
    });

    function hideShowMoreCourses(parentId) {
        let liCount = 4;
        let customUl = $(document)
            .find('#panelbar')
            .find('.filter-panel-title-' + parentId)
            .closest('li')
            .find('ul');
        if (parentId == 2) {
            customUl = $(document).find('#course_list').closest('ul');
            customUl.addClass('custom-intake-size');
            liCount = 8;
        } else {
            customUl.addClass('custom-panel-size');
        }

        let cList = customUl.find('li:gt(' + liCount + ')');
        if (!customUl.hasClass('expanded')) {
            cList.hide();
        } else {
            cList.show();
        }

        if (cList.length > 0) {
            let tempLiHtmlMore =
                '<span class="k-link"><a class="text-xs leading-tight text-blue-500" href="javascript:void(0);">See More</a></span>';
            let tempLiHtmlLess =
                '<span class="k-link"><a class="text-xs leading-tight text-blue-500" href="javascript:void(0);">See Less</a></span>';
            customUl.append(
                $('<li class="expand px-4 py-1">' + tempLiHtmlMore + '</li>').click(
                    function (event) {
                        let expandible = $(this).closest('ul');
                        expandible.toggleClass('expanded');
                        if (!expandible.hasClass('expanded')) {
                            $(this).html(tempLiHtmlMore);
                        } else {
                            $(this).html(tempLiHtmlLess);
                        }
                        cList.toggle();
                        event.preventDefault();
                    }
                )
            );
        }
    }

    var queryParams = getQueryParams();
    var gridHeight = getGridTableHeight(gridID, 0);

    $(gridID).kendoGrid({
        dataSource: customDataSource(
            'api/student-data-scout',
            {
                profile_picture: { type: 'string' },
                student_name: { type: 'string' },
                student_type: { type: 'string' },
                course_list: { type: 'string' },
                student_id: { type: 'string' },
                DOB: { type: 'date' },
                campus: { type: 'string' },
            },
            queryParams,
            ['DOB'],
            25
        ),
        height: gridHeight,
        pageable: customPageableArr(),
        // detailTemplate: kendo.template($("#rowDetailTemplate").html()),
        dataBinding: function (e) {
            // Disable the header checkbox before the grid starts loading data
            $('.header-checkbox .k-checkbox').prop('disabled', true); // Disable header checkbox initially
        },
        dataBound: function (e) {
            // Re-enable the header checkbox after data is bound
            setTimeout(function () {
                $('.header-checkbox .k-checkbox').prop('disabled', false); // Enable header checkbox after data is loaded
                $('.search-loading').hide();
            }, 100);
            $('.filter_title').show();
            $('.totalStudentCount').show();
            defaultHideShowColumn();
            //customDataBoundWithActionMenu(gridID, e);
            setTimeout(function () {
                setFilterIcon(gridID);
                manageFilterBaseTitle();
                togglePagination(gridID);
                showRowHoverActions();
            }, 100);
            setTimeout(function () {
                showExistingFilter();
            }, 2000);

            // This below code is for isSelectAll
            let grid = this;
            let data = grid.dataSource.view();
            data.forEach((item) => {
                let row = grid.table.find("tr[data-uid='" + item.uid + "']");
                if (isSelectAll) {
                    grid.select(row);
                }
                // } else {
                //     grid.clearSelection(row);
                // }
            });

            getActionTooltip('.student-tooltip-target', 120, 'left');
        },
        persistSelection: true,
        change: onChange,
        columnResize: columnResizeAllTimetable,
        filterable: false,
        sortable: true,
        resizable: true,
        //navigatable: true,
        columns: [
            {
                selectable: true,
                width: '50px',

                headerAttributes: {
                    class: 'header-checkbox',
                },
            },
            {
                template:
                    "<div class='course-name text-sm leading-5 text-gray-600 action-div'>#: student_id #</div>",
                field: 'generated_stud_id',
                title: 'Student ID',
                minResizableWidth: 80,
                width: $.cookie('studentlist-student_id')
                    ? $.cookie('studentlist-student_id') + 'px'
                    : 115,
            },
            {
                template: function (dataItem) {
                    return manageStudentProfilePic(
                        dataItem.id,
                        dataItem.secure_id,
                        dataItem.mini_profile_pic,
                        dataItem.student_name,
                        dataItem.contact
                    );
                },
                field: 'first_name',
                title: 'Full Name',
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Full Name</a>",
                sortable: false,
                minResizableWidth: 260,
                width: $.cookie('studentlist-student_name')
                    ? $.cookie('studentlist-student_name') + 'px'
                    : 180,
            },
            {
                hidden: true,
                template:
                    '<div class=\'course-name text-sm leading-5 text-gray-600 action-div\'> #: kendo.toString(DOB, "dd MMM yyyy") # </div>',
                field: 'DOB',
                title: 'DOB',
                minResizableWidth: 80,
                sortable: false,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>DOB</a>",
                width: $.cookie('studentlist-DOB') ? $.cookie('studentlist-DOB') + 'px' : '',
            },
            {
                // template:
                //     "<div class='course-name text-sm leading-5 text-gray-600 action-div'>#: allCampusName #</div>",
                template: function (dataItem) {
                    return manageAllCampusName(dataItem.allCampusName);
                },
                field: 'allCampusName',
                title: 'Campus',
                minResizableWidth: 80,
                sortable: false,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Campus</a>",
                width: $.cookie('studentlist-campus') ? $.cookie('studentlist-campus') + 'px' : '',
            },
            {
                template:
                    "<div class='course-name text-sm font-normal leading-5 !text-gray-600 action-div'>#: (student_type) ? student_type:'-' #</div>",
                field: 'student_type',
                title: 'Type',
                minResizableWidth: 80,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 ' style='cursor: default !important;'>Type</a>",
                filterable: {
                    multi: true,
                    dataSource: [
                        { student_type: 'Domestic' },
                        { student_type: 'Offshore' },
                        { student_type: 'Onshore' },
                    ],
                },
                width: ($.cookie('studentlist-student_type') ?? '100') + 'px',
            },
            {
                template: function (dataItem) {
                    return manageCourseTd(dataItem.student_courses);
                },
                field: 'course_list',
                title: 'Courses',
                minResizableWidth: 80,
                sortable: false,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 ' style='cursor: default !important;'>Courses</a>",
                filterable: {
                    multi: true,
                    search: true,
                    dataSource: {
                        transport: {
                            read: {
                                url: site_url + 'api/student-course-list',
                                dataType: 'json',
                                type: 'POST',
                                data: {
                                    field: 'course_list',
                                },
                            },
                        },
                        schema: {
                            data: 'data',
                        },
                    },
                },
                width: $.cookie('studentlist-course_list')
                    ? $.cookie('studentlist-course_list') + 'px'
                    : '',
            },
            {
                template: function (dataItem) {
                    return dataItem.student_courses.length
                        ? manageCourseProgressTd(dataItem.student_courses)
                        : null;
                },
                field: 'course_progress',
                title: 'Course Progress',
                minResizableWidth: 80,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 ' style='cursor: default !important;'>Course Progress</a>",
                sortable: false,
                filterable: false,
                width: $.cookie('studentlist-course_progress')
                    ? $.cookie('studentlist-course_progress') + 'px'
                    : '',
            },
            {
                template: function (dataItem) {
                    return manageAttendanceTd(dataItem);
                },
                field: 'attendance',
                title: 'Attendance',
                minResizableWidth: 100,
                filterable: false,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 ' style='cursor: default !important;'>Attendance</a>",
                sortable: false,
                width: $.cookie('studentlist-attendance')
                    ? $.cookie('studentlist-attendance') + 'px'
                    : '',
            },
            {
                template: function (dataItem) {
                    return managePaymentTd(dataItem);
                },
                field: 'payment',
                title: 'Payment',
                minResizableWidth: 80,
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 ' style='cursor: default !important;'>Payment</a>",
                filterable: false,
                sortable: false,
                width: $.cookie('studentlist-payment')
                    ? $.cookie('studentlist-payment') + 'px'
                    : '',
            },
            {
                template: function (dataItem) {
                    return manageActionTd(dataItem.id, dataItem.student_id);
                },
                field: '',
                title: '',
                minResizableWidth: 0,
                // headerTemplate:
                //     "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>Action</a>",
                width: 60,
                filterable: false,
                sortable: false,
                attributes: {
                    class: 'tw-sticky-cell',
                },
                headerAttributes: {
                    class: 'tw-sticky-header',
                },
            },
        ],
        noRecords: noRecordTemplate(),
        excel: {
            fileName: 'Student.xlsx',
            filterable: true,
            //allPages:true
        },
    });

    function shouldLoop(dataFilter) {
        for (const key in dataFilter) {
            if (Object.prototype.hasOwnProperty.call(dataFilter, key)) {
                const value = dataFilter[key];
                if (Array.isArray(value) && value.length > 0) {
                    return true;
                } else if (typeof value === 'string' && value.length > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    function showExistingFilter() {
        // const filterCookie = $.cookie("studentScoutGridFilter");
        const filterCookie = getQueryParams();
        if (filterCookie) {
            // const filterObject = JSON.parse(filterCookie);
            const filterObject = filterCookie;
            if (shouldLoop(filterObject)) {
                $.each(filterObject, function (key, values) {
                    if (Array.isArray(values) && values.length > 0) {
                        const panelBar = $(studentsFilterPanelBarId).data('kendoPanelBar');
                        panelBar.expand($(`#${key}`).parent().parent());

                        $.each(values, function (index, value) {
                            const checkbox = $(`input[name="${key}"][value="${value}"]`);
                            if (checkbox.length) {
                                checkbox.prop('checked', true);
                            }
                        });
                    }
                });

                if (scoutFilterFunctionCalled) {
                    scoutFilterFunctionCalled = false;
                    setTimeout(() => {
                        let course_id = $('input[name="course_id"]').is(':checked') ? 1 : 0;
                        let campus_id = $('input[name="campus_id"]').is(':checked') ? 1 : 0;
                        let student_type = $('input[name="student_type"]').is(':checked') ? 1 : 0;
                        let nationality = $('input[name="nationality"]').is(':checked') ? 1 : 0;
                        let status = $('input[name="status"]').is(':checked') ? 1 : 0;
                        let batch = $('input[name="batch"]').is(':checked') ? 1 : 0;
                        let teacher = $('input[name="teacher"]').is(':checked') ? 1 : 0;
                        let student_intake = $('input[name="student_intake"]').is(':checked')
                            ? 1
                            : 0;
                        let filterCount =
                            course_id +
                            campus_id +
                            student_type +
                            nationality +
                            status +
                            batch +
                            teacher +
                            student_intake;

                        if (filterCount == 0) {
                            $(document).find('.filterCountDiv').addClass('hidden');
                            $(document).find('#filterBtn').removeClass('border-primary-blue-500');
                            $(document).find('#filterBtn').addClass('border-gray-300');
                            $(document).find('#filterBtn').removeClass('bg-primary-blue-50');
                        } else {
                            $('#filterBtn').trigger('click');
                            $(document).find('.filterCountDiv').removeClass('hidden');
                            $(document)
                                .find('#filterBtn')
                                .addClass('border-primary-blue-500 bg-primary-blue-50');
                            $(document).find('#filterBtn').removeClass('border-gray-300 bg-white');
                            $(document).find('#filterBtn').addClass('bg-primary-blue-50');
                        }
                        $(document).find('#filterBtn .filterCount').text(filterCount);
                        applyStudentFilterV2();
                    }, 500);
                }
            }
        }
    }

    customGridHtml(gridID);
    $('.k-filter-menu-container').find('.k-button[type=submit]').prop('disabled', true);

    function manageStudentProfilePic(normalId, id, profile_pic, nameStr, contact) {
        // Manage user name with profile picture or default 2 characters
        let html = '';
        if (profile_pic == '') {
            let displayName = 'NA';
            if (typeof nameStr !== undefined && nameStr != null) {
                let name = nameStr.toUpperCase().split(/\s+/);
                displayName =
                    name.length >= 2
                        ? name[0].charAt(0) + name[1].charAt(0)
                        : name[0].substring(0, 2);
            } else {
                nameStr = 'N/A';
            }
            html = `<div class='flex items-center scout_${normalId} stud_${id} space-x-2 studentNameDiv'>
                        <div class='user-profile-pic h-7 w-7 max-w-7 flex items-center justify-center flex-shrink-0 rounded-full bg-blue-500'>
                            <span class='text-xs leading-none font-medium'>${displayName}</span>
                        </div>
                        &nbsp;
                        <button type='button'
                                data-name='${nameStr}'
                                data-student-id='${id}'
                                data-profile='${profile_pic}'
                                data-contact='${contact}'
                                class='view_profile student-first-name text-sm leading-5 text-gray-700 action-div hover:text-primary-blue-500 student-name-hover font-medium'>
                            ${nameStr}
                        </button>
                    </div>`;
        } else {
            html = `<div class="flex items-center scout_${normalId} stud_${id} space-x-2 studentNameDiv">
                        <img class="h-7 w-7 object-cover rounded-full flex object-top" src="${profile_pic}" alt="" />
                        &nbsp;
                        <button
                          type="button"
                          data-name="${nameStr}"
                          data-student-id="${id}"
                          data-profile="${profile_pic}"
                          data-contact="${contact}"
                          class="view_profile student-first-name text-sm leading-5 text-gray-700 action-div hover:text-primary-blue-500 student-name-hover font-medium">
                          ${nameStr}
                        </button>
                    </div>`;
        }
        return html;
    }

    function createMultiSelectStudents(element) {
        element.removeAttr('data-bind');
        element.kendoMultiSelect({
            dataSource: {
                transport: {
                    read: {
                        url: site_url + 'api/student-full-name',
                        dataType: 'json',
                        type: 'POST',
                        data: {
                            field: 'student_name',
                        },
                    },
                },
                schema: {
                    data: 'data',
                },
            },
            change: function (e) {
                let tempFilter = { logic: 'or', filters: [] };
                let values = this.value();
                $.each(values, function (i, v) {
                    tempFilter.filters.push({
                        field: 'student_name',
                        operator: 'eq',
                        value: v,
                    });
                    //filter.filters.push({field: "student_name", operator: "contains", value: v });
                });
                $('.k-filter-menu-container')
                    .find('.k-multiselect')
                    .siblings()
                    .find('.k-button[type=submit]')
                    .hide();
                //$(".k-filter-menu-container").find('.k-multiselect').siblings().find(".k-button[type=submit]").prop("disabled", true);
                //$(gridID).data('kendoGrid').dataSource.filter(filter);
                addMoreFilters(gridID, tempFilter, 'student_name');
            },
        });

        /*multiSelect.bind("dataBound", function () {
      $(".k-filter-menu-container").find(".k-button[type=submit]").prop("disabled", true);
    });*/
    }

    function addMoreFilters(gridID, addFilterArr, fieldName) {
        let grid = $(gridID).data('kendoGrid');
        if (typeof grid.dataSource.filter() != 'undefined' && grid.dataSource.filter() != null) {
            let flag = true;
            let oldFilterArr = grid.dataSource.filter().filters;
            Object.entries(oldFilterArr).forEach((filter) => {
                if (filter[1].field != 'extra' && filter[1].field != 'searchKey') {
                    if (fieldName == filter[1].field) {
                        oldFilterArr.splice(filter, 1);
                        /*let indexToRemove = 1;
                        oldFilterArr.splice(indexToRemove, 1)
                        filter.splice(indexToRemove, 1)

                        else if (filter[1].filters.length > 0 && fieldName == filter[1].filters[0].field) {
                        oldFilterArr.splice(filter, 1);
                        }*/
                    } else {
                        return false;
                        oldFilterArr.splice(filter, 1);
                    }
                }
            });

            if (flag) {
                oldFilterArr.push(addFilterArr);
            }

            $(gridID).data('kendoGrid').dataSource.filter(oldFilterArr);
            //grid.dataSource.read();
            //grid.dataSource.filter().filters.push(tempArr);
        } else {
            $(gridID).data('kendoGrid').dataSource.filter(addFilterArr);
        }
    }

    function defaultHideShowColumn() {
        $(document)
            .find('.manageColumnBox .k-checkbox')
            .each(function () {
                if ($(this).is(':checked')) {
                    $(gridID).data('kendoGrid').showColumn($(this).val());
                } else {
                    $(gridID).data('kendoGrid').hideColumn($(this).val());
                }
            });
    }

    function columnResizeAllTimetable(e) {
        $.cookie('studentlist-' + e.column.field, e.newWidth);
    }

    function onChange(e) {
        selectedStudent = [];
        let selectedStudentWithUserName = [];
        let studentIds = [];
        $('#mailToUser').html('');
        var rows = e.sender.select();

        rows.each(function (e) {
            var grid = $(gridID).data('kendoGrid');
            var dataItem = grid.dataItem(this);
            studentIds.push(dataItem.id);
            if (dataItem.username == null) {
                selectedStudentWithUserName.push({
                    id: dataItem.id,
                });
            }
            selectedStudent.push({
                id: dataItem.id,
                name: dataItem.student_name,
                contact: dataItem.contact,
                username: dataItem.username,
                profile_pic: dataItem.mini_profile_pic,
            });
        });
        if (studentIds.length > 5) {
            $('.studentNameList').addClass('max-h-20');
        } else {
            $('.studentNameList').removeClass('max-h-20');
        }
        $(document).find('.studentIds').val(studentIds);
        // $(document).find('.studentNameList').html(getStudentStrData(selectedStudent));
        let selectedCount = 0;
        let uncheckedItems = [];
        let checkedItems = [];
        if (isSelectAll) {
            let gridUncheck = e.sender;
            let dataUncheck = gridUncheck.dataSource.view();
            // Loop through all data items
            dataUncheck.forEach((item) => {
                let row2 = gridUncheck.table.find("tr[data-uid='" + item.uid + "']");
                if (!gridUncheck.select().filter(`[data-uid='${item.uid}']`).length) {
                    uncheckedItems.push({
                        id: item.id,
                    });
                }
                if (gridUncheck.select().filter(`[data-uid='${item.uid}']`).length) {
                    checkedItems.push({
                        id: item.id,
                    });
                }
            });

            // // Now `uncheckedItems` contains all the unchecked (unselected) items
            selectedCount = $('.totalStudent').first().text() - uncheckedItems.length;
        } else {
            selectedCount = selectedStudent.length;
            // selectedCount = ($('.totalStudent').first().text());
        }

        let selectedTitle = 'No student selected';
        if (selectedCount > 0) {
            //selectedTitle = selectedCount == 1 ? "1 student selected" : selectedCount + " students selected";
            selectedTitle =
                selectedCount == 1
                    ? "<span class='filterStudCount'> 1 </span> student selected"
                    : `<span class='filterStudCount'>${selectedCount}</span> students selected`;
            $('#action').addClass('bottomaction').removeClass('heightzero');
            $('#selectedStudents').prop('checked', true);
            $('.changeBulkEmail').text('Send Email');
            $('.changeBulkSms').text('Send SMS');
        } else {
            $('#action').removeClass('bottomaction').addClass('heightzero');
        }
        $(document).find('#selected_title').html(selectedTitle);

        onChangeForGridCheckbox(selectedCount);

        if (selectedCount > 1) {
            $('.changeBulkEmail').text('Bulk Email');
            $('.changeBulkSms').text('Bulk SMS');
        }
        $('#inviteToGalaxy').toggle(selectedStudentWithUserName.length > 0);
        if (isSelectAll) {
            const uncheckedIds = uncheckedItems.map((item) => item.id);
            const filteredData = defaultWhitelist.filter((item) => !uncheckedIds.includes(item.id));
            selectedStudent = [];
            selectedStudent = filteredData;
        } else {
            let checkedStudentList = this.selectedKeyNames();
            const filteredData = defaultWhitelist.filter((item) =>
                checkedStudentList.includes(item.id.toString())
            );
            selectedStudent = [];
            selectedStudent = filteredData;
        }
    }

    function getStudentStrData(selectedStudent) {
        let prev = {};
        let studentNameListStr = '';
        selectedStudent.filter(function (arr) {
            var key = arr['id'];
            if (prev[key]) return false;
            if ((prev[key] = true)) {
                studentNameListStr +=
                    '<div class="inline-flex items-center justify-center px-2.5 py-1 m-0.5 bg-gray-300 rounded-full">' +
                    '<span class="text-sm">' +
                    arr['name'] +
                    '</span>&nbsp;' +
                    '<span class="cursor-pointer k-icon k-i-close remove_stud" data-sid="' +
                    arr['id'] +
                    '"></span>' +
                    '</div>';
            }
        });
        return studentNameListStr;
    }

    /* TODO: commented out because this was duplicate, please verify if there are any side effects */
    /* $("#sms_template").kendoDropDownList({
    dataTextField: "name",
    dataValueField: "id",
    dataSource: {
      schema: {
        data: "data",
      },
      transport: {
        read: {
          url: site_url + "api/sms-template-list",
          dataType: "json",
          type: "POST",
        },
      },
    },
  }); */

    function manageCourseTd(course_detail) {
        let courseTdHtml = '';
        if (course_detail.length > 0) {
            let courseTitle =
                course_detail[0].course.course_code + ' - ' + course_detail[0].course.course_name;
            courseTdHtml +=
                '<div class="inline-flex flex-col items-start justify-center w-full">' +
                '<div class="inline-flex space-x-1 items-center justify-start flex-1 w-full">' +
                '<div class="w-4/5 widthfitcontent">' +
                '<p class="text-sm leading-5 text-gray-600 action-div cursor-default">' +
                courseTitle +
                '</p>' +
                '</div>';
            if (course_detail.length > 1) {
                courseTdHtml +=
                    '<div class="flex items-center justify-start px-2.5 py-0.5 bg-gray-100 rounded w-10"><p class="text-xs leading-5 text-center text-gray-600">+' +
                    (course_detail.length - 1) +
                    '</p></div>';
            }
            courseTdHtml += '</div></div>';
        }
        /*if (course_list_str != '') {
            let course = course_list_str.split(',@@,');
            if(course.length > 0){
                let courseName = course[0];
                if(course[0].length > 30) course[0] = course[0].substring(0,30) + '...';
                courseTdHtml += '<div class="inline-flex flex-col items-start justify-center"><div class="inline-flex space-x-1 items-center justify-start flex-1"><div class="flex items-center justify-center px-2 py-2 bg-gray-100 rounded"><p class="text-xs leading-none text-center text-gray-800">'+ course[0] +'</p></div>';
                if(course.length > 1) {
                    courseTdHtml += '<div class="flex items-center justify-center px-2 py-2 bg-gray-100 rounded"><p class="text-xs leading-none text-center text-gray-600">+'+ (course.length - 1) + '</p></div>';
                }
                courseTdHtml += '</div></div>';
            }
        }*/
        return courseTdHtml;
    }

    function manageAllCampusName(allCampusName) {
        let allCampusNameHtml = '';
        if (allCampusName.length > 0) {
            allCampusNameHtml += `
        <div class="inline-flex flex-col items-start justify-center w-full">
            <div class="inline-flex space-x-1 items-center justify-start flex-1 w-full">
                <div class="w-4/5 widthfitcontent">
                    <p class="text-sm leading-5 text-gray-600 action-div cursor-default">
                        ${allCampusName[0]}
                    </p>
                </div>
    `;

            if (allCampusName.length > 1) {
                allCampusNameHtml += `
                <div class="inline-flex items-center justify-center px-2 py-0.5 bg-gray-100 rounded w-8 action-tooltip">
                    <p class="text-xs leading-5 text-center text-gray-600">+${allCampusName.length - 1}</p>
                </div>
        `;
            }

            allCampusNameHtml += `
            </div>
        </div>
    `;
        }
        return allCampusNameHtml;
    }

    function manageCourseProgressTd(studentCourse) {
        let enrollments = studentCourse[0]['course_process'];
        let courseProgressHtml = '';
        if (enrollments.length > 0) {
            let course_progress = enrollments;
            if (course_progress.length > 0 && typeof course_progress[0]['color'] != 'undefined') {
                courseProgressHtml +=
                    '<div class="action-div inline-flex space-x-0.5 items-start justify-center mt-1">';
                for (let i = 0; i < course_progress.length; i++) {
                    /* use bg-emerald-500, bg-red-500 and bg-gray-200 as per figma */
                    let selectedUnit =
                        course_progress[i]['is_active'] == 1
                            ? '<div class="arrow-down represent"></div>'
                            : '';
                    courseProgressHtml +=
                        '<div class="items-center justify-center w-1.5 h-5 bg-' +
                        course_progress[i]['color'] +
                        ' rounded cursor-pointer course-unit" data-unit-title="' +
                        course_progress[i]['unit_name'] +
                        '">' +
                        selectedUnit +
                        '</div>';
                }
                courseProgressHtml += '</div>';
            }
        } else if (studentCourse[0]['course']['course_subjects'].length > 0) {
            let course_subjects = studentCourse[0]['course']['course_subjects'];
            courseProgressHtml +=
                '<div class="action-div inline-flex space-x-0.5 items-start justify-center mt-1">';
            for (let i = 0; i < course_subjects.length; i++) {
                let unit_name =
                    course_subjects[i]['unit'] != null
                        ? course_subjects[i]['unit']['unit_code'] +
                          ' : ' +
                          course_subjects[i]['unit']['unit_name']
                        : '-';
                /* use bg-emerald-500, bg-red-500 and bg-gray-200 as per figma */
                let selectedUnit =
                    course_subjects[i]['is_active'] == 1
                        ? '<div class="arrow-down represent"></div>'
                        : '';
                courseProgressHtml +=
                    '<div class="items-center justify-center w-1.5 h-6 bg-gray-200 ' +
                    ' rounded cursor-pointer course-unit" data-unit-title="' +
                    unit_name +
                    '">' +
                    selectedUnit +
                    '</div>';
            }
            courseProgressHtml += '</div>';
        }

        return courseProgressHtml;
        /*return '<div class="inline-flex space-x-0.5 items-start justify-center mt-1">' +
            '<div class="flex items-center justify-center w-1.5 h-6 bg-green-500 rounded cursor-pointer course-unit" data-title="text here..."></div>'.repeat(randomIntFromInterval(7, 9)) +
            '<div class="flex items-center justify-center w-1.5 h-6 bg-red-500 rounded cursor-pointer course-unit" data-title="text here..."><div class="arrow-down"></div></div>' +
            '<div class="flex items-center justify-center w-1.5 h-6 bg-gray-200 rounded cursor-pointer course-unit" data-title="text here..."></div>'.repeat(randomIntFromInterval(3, 6)) +
            '</div>';*/
    }

    function manageAttendanceTd(course_detail) {
        let cAttendanceHtml = '';
        cAttendanceHtml =
            '<div class="action-div w-full bg-gray-200 rounded-full overflow-hidden h-3 dark:bg-green-500">' +
            '<div class="bg-yellow-500 h-3 dark:bg-gray-200" style="width: ' +
            calCulateAttendance(course_detail.total_day, course_detail.absent_day) +
            '%">' +
            '<div class="bg-green-teal-500 h-3 dark:bg-gray-200" style="width: ' +
            calCulateAttendance(course_detail.total_day, course_detail.present_day) +
            '%"></div>' +
            '</div>' +
            '</div>';

        return cAttendanceHtml;
    }

    function managePaymentTd(course_detail) {
        let cPaymentHtml = '';
        if (course_detail.initial_payments.length > 0) {
            let paid = course_detail.initial_payments[0].paid_count;
            let missed = course_detail.initial_payments[0].missed_count;
            let unpaid = course_detail.initial_payments[0].unpaid_count;

            /* use bg-emerald-500, bg-amber-500 and bg-gray-200 as per figma */

            if (paid > 0 || missed > 0 || unpaid > 0) {
                cPaymentHtml =
                    '<div class="action-div flex align-center justify-start flex-wrap gap-1"> ' +
                    '<div class="w-2.5 h-2.5 rounded-sm bg-green-teal-500"></div>'.repeat(paid) +
                    '<div class="w-2.5 h-2.5 rounded-sm bg-yellow-500"></div>'.repeat(missed) +
                    '<div class="w-2.5 h-2.5 rounded-sm bg-gray-200"></div>'.repeat(unpaid) +
                    '</div>';
            }
        }
        if (cPaymentHtml.length == 0) {
            cPaymentHtml = '-';
        }
        return cPaymentHtml;
    }

    function manageActionTd(id, student_id) {
        return `<div class="actionButtonHide action-div action-only text-center student-tooltip-target inline-block" title="View all actions">
          <a class="inline-flex flex-col items-center justify-center w-6 h-6 p-0.5 bg-white rounded shadow relative " href="javascript:void(0);" data-id="${id}" data-student-id="${student_id}" aria-label="Expand" tabindex="-1">
            <span class="k-icon k-i-more-horizontal"></span>
          </a>
      </div>`;
    }

    function calCulateAttendance(totalday, calcuday) {
        // min and max included
        if (totalday == 0) {
            return 0;
        }
        return (100 * calcuday) / totalday;
    }

    function manageFilterBaseTitle() {
        let totalCount = $(gridID).data('kendoGrid').dataSource.total();

        // $(document).find(".filter_title").html("").html($(gridID).find("span.k-pager-info").text().replace("results", "students"));
        // countText = (totalCount > 1000) ? "1000+":totalCount;
        // $(document).find(".filter_title").html("").html($(gridID).find("span.k-pager-info").text().replace(totalCount, countText));

        const $filterTitle = $(document).find('.filter_title');
        const pagerInfoText = $(gridID).find('span.k-pager-info').text();

        // Update title with total count information
        $('.totalStudent').text(totalCount);
        const countText = totalCount < 1000 ? totalCount : '1000+';
        $filterTitle.html(
            pagerInfoText.replace('results', 'students').replace(totalCount, countText)
        );
        $('.gridInfo').find('.k-pager-sizes').hide();
        $('.gridInfo').find('.k-pager-info').hide();
    }

    function refreshGridData() {
        $(gridID).data('kendoGrid').refresh();
        $(gridID).data('kendoGrid').dataSource.read();
        // $(".k-i-close").trigger("click");
        $('.closeAction').trigger('click');
    }

    function kendowindowOpen(windowID) {
        const isLetterParamsId = windowID === '#letterParameterModal';
        let kendoWindow = $(document).find(windowID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal gradientbackground')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');

        if (isLetterParamsId) {
            kendoWindow.parent('div').addClass('tw-dialog k-dialog');
        }
        // Check if the windowID is #emailTemplatesModal and add a new class to the parent
        const modalIDs = [
            '#emailTemplatesModal',
            '#letterTemplatesModal',
            '#letterParameterModal',
            '#closeModelConformation',
            '#closeLetterModelConformation',
            '#statusForSendEmailModal',
            '#loaderForEmail',
            '#loaderForLetter',
        ];
        if (modalIDs.includes(windowID)) {
            kendoWindow.parent('div').addClass('k-modal-window tw-dialog');
        }
    }

    function windowSlideFormat(title) {
        return {
            title: title,
            width: '40%',
            height: '100%',
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: 0,
                left: '60%',
            },
            animation: animationPropertyForWindow(),
            visible: false,
        };
    }

    function windowFullSlideFormat(title) {
        return {
            title: title,
            width: '60%',
            height: '100%',
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: 0,
                left: '40%',
            },
            animation: {
                open: {
                    effects: 'slideIn:left',
                    duration: 300,
                },
                close: {
                    effects: 'slideIn:left',
                    reverse: true,
                    duration: 300,
                },
            },
            visible: false,
        };
    }

    function sendSmsUsingApi(phoneNumber, smsContent) {
        if (
            window.location.hostname == 'www.rtomanager.dev' ||
            window.location.origin == 'http://www.rtomanager.dev'
        ) {
            notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
            return false;
        } else {
            var xhr = new XMLHttpRequest();
            xhr.open(
                'GET',
                sendSmsUrl +
                    '?apiKey=' +
                    SMSApiKey +
                    '&to=' +
                    phoneNumber +
                    '&content=' +
                    smsContent +
                    '',
                true
            );
            xhr.onreadystatechange = function () {
                if (xhr.readyState == 4 && xhr.status == 200) {
                    //console.log('success');
                }
                var data = JSON.parse(xhr.response);
                if (data.messages[0].accepted == false) {
                    var errorMsg =
                        data.messages[0].error == 'null' || data.messages[0].error == ''
                            ? 'Invalid destination address'
                            : data.messages[0].error;
                    notificationDisplay(errorMsg, 'SMS Error', 'error');
                } else {
                    notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
                }
            };
            xhr.send();
            return true;
        }
    }

    $('#studentMiniProfileModal').kendoWindow(windowSlideFormat('Student Profile'));
    $('#sendMailStudentModal').kendoWindow(windowFullSlideFormat('Send Email'));
    $('#loaderForEmail').kendoWindow(windowGenerateLetter('Sending Email'));
    $('#statusForSendEmailModal').kendoWindow(openCenterWindow('Email Send Status', 40, 15, 30));
    $('#sendSmsStudentModal').kendoWindow(windowFullSlideFormat('Send SMS'));

    function openCenterWindow(titleText, widthVal = 34, topVal = 25, leftVal = 33) {
        return {
            title: titleText,
            width: widthVal + '%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: defaultCloseAnimation(),
        };
    }

    $('#emailTemplatesModal').kendoWindow({
        title: 'Email Templates',
        width: '66%',
        height: '76%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: '12%',
            left: '17%',
        },
        // animation: {
        //     close: {
        //         effects: "fade:out",
        //     },
        // },
        animation: false,
    });

    $('#letterTemplatesModal').kendoWindow({
        title: 'Letter Templates',
        width: '66%',
        height: '76%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: '12%',
            left: '17%',
        },
        animation: false,
        // animation: {
        //     close: {
        //         effects: "fade:out",
        //     },
        // },
    });

    $('#letterParameterModal').kendoWindow({
        title: 'Letter Parameter Replace',
        width: '66%',
        height: '76%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: '12%',
            left: '17%',
        },
        animation: false,
        // animation: {
        //     close: {
        //         effects: "fade:out",
        //     },
        // },
    });

    /*var insertTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
        transport: {
            read: {
                url: site_url + "api/get-mail-template-list",
                dataType: "json",
                type: "POST",
            },
        },
        schema: {
            data: "data",
            model: {
                id: "id",
                hasChildren: "hasChildren",
                children: "sub_list",
            },
        },
    });
    var insertLetterTemplateSidebarMenu = new kendo.data.HierarchicalDataSource(
        {
            transport: {
                read: {
                    url: site_url + "api/get-letter-template-list",
                    dataType: "json",
                    type: "POST",
                },
            },
            schema: {
                data: "data",
                model: {
                    id: "id",
                    hasChildren: "hasChildren",
                    children: "sub_list",
                },
            },
        },
    );

    $("#insertTemplatePanelBar").kendoPanelBar({
        template: kendo.template($("#emailPanelBarTemplate").html()),
        dataSource: insertTemplateSidebarMenu,
    });
    $("#insertLetterTemplatePanelBar").kendoPanelBar({
        template: kendo.template($("#letterPanelBarTemplate").html()),
        dataSource: insertLetterTemplateSidebarMenu,
    });*/

    $('#sms_template').kendoDropDownList({
        dataTextField: 'name',
        dataValueField: 'id',
        dataSource: {
            schema: {
                data: 'data',
            },
            transport: {
                read: {
                    url: site_url + 'api/sms-template-list',
                    dataType: 'json',
                    type: 'POST',
                },
            },
        },
    });

    $('body').on('click', '#exportData', function (e) {
        e.preventDefault();
        var queryParams = getQueryParams();
        startAjaxLoader();
        $.ajax({
            url: site_url + 'export-student-scout-data',
            type: 'POST',
            data: queryParams,
            xhrFields: {
                responseType: 'blob', // Set response type to blob for file download
            },
            success: function (data) {
                stopAjaxLoader();
                // Create a URL for the file and trigger a download
                const url = window.URL.createObjectURL(new Blob([data]));
                const a = document.createElement('a');
                a.href = url;
                a.download = 'Students.xlsx';
                document.body.append(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            },
            error: function (xhr, status, error) {
                stopAjaxLoader();
                //console.error("Export failed:", error);
            },
        });
    });

    $('body').on('click', '#manageColumns', function (e) {
        //e.preventDefault();
        let checkHtml = $(document).find('.manageColumnBox');
        if (checkHtml.parent().hasClass('active')) {
            checkHtml.removeClass('active');
            checkHtml.parent().removeClass('active');
            checkHtml.find('.manage-column-box__dropdonw').slideUp(300);
        } else {
            checkHtml.addClass('active');
            checkHtml.parent().addClass('active');
            checkHtml.find('.manage-column-box__dropdonw').slideDown(300);
        }
    });

    $('body').on('click', '.column_filter', function (e) {
        e.preventDefault();
        let columnHtml = $(document).find('.manageColumnBox .k-checkbox');
        if ($(this).hasClass('reset')) columnHtml.prop('checked', true);
        if ($(this).hasClass('clear') || $(this).hasClass('save')) {
            $(document).find('#manageColumns').trigger('click');
        }
        let selectedScoutFields = [];
        let selectedScoutExportFields = [];
        $(document)
            .find('.manageColumnBox .k-checkbox')
            .each(function () {
                var fieldObj = {};
                var fieldObjExport = {};
                fieldObj[$(this).val()] = $(this).is(':checked').toString();
                selectedScoutFields.push(fieldObj);
                // fieldObjExport[$(this).next('label').text()] = $(this).is(":checked").toString();
                fieldObjExport['text'] = $(this).next('label').text();
                fieldObjExport['textMain'] = $(this).val();
                fieldObjExport['value'] = $(this).is(':checked').toString();
                selectedScoutExportFields.push(fieldObjExport);
            });
        $.cookie('studentScoutListSelectedFields', JSON.stringify(selectedScoutFields));
        $.cookie('studentScoutListSelectedExportFields', JSON.stringify(selectedScoutExportFields));
        defaultHideShowColumn();
    });

    $('body').on('contextmenu', '.k-master-row td', function (e) {
        return false;
    });

    $(gridID).on('change', 'thead .k-checkbox', function () {
        if (!$(this).is(':checked')) {
            $('.selectAllStudents').trigger('click');
        }
    });

    $('body').on('change', '#selectedStudents', function () {
        isSelectAll = false;
        $('.k-checkbox').each(function () {
            if ($(this).closest('tr').is('.k-state-selected')) {
                $(this).click();
            }
        });
        manageFooterGridFilterText();
    });

    $('body').on('click', '.closeAction', function () {
        $('#action').removeClass('bottomaction').addClass('heightzero');
        $('#selectedStudents').trigger('click');
        $(document).find('#moreActionModal').hide();
        isSelectAll = false;
        let grid = $('#studentList').data('kendoGrid');
        let data = grid.dataSource.view();
        data.forEach((item) => {
            let row = grid.table.find("tr[data-uid='" + item.uid + "']");
            if (isSelectAll) {
                grid.select(row);
            } else {
                grid.clearSelection(row);
            }
        });
    });

    $('body').on('click', '#moreAction', function () {
        $(document).find('#moreActionModal').toggle();
    });

    $(document).on('click', '.view_miniprofile', function () {
        let viewProfile = $(document).find('#studentMiniProfileModal');
        let studentID = $(this).attr('data-student-id');
        let attrCollection = {
            'data-student-id': studentID,
            'data-name': $(this).attr('data-name'),
            'data-profile': $(this).attr('data-profile'),
            'data-contact': $(this).attr('data-contact'),
        };
        $('#studentMiniProfileModal_wnd_title').text($(this).attr('data-name'));
        kendowindowOpen('#studentMiniProfileModal');
        viewProfile.find('#single_email').attr(attrCollection);
        viewProfile.find('#single_sms').attr(attrCollection);
        viewProfile.find('#single_letter').attr(attrCollection);
        viewProfile.find('#viewProfileBtn').attr('data-student-id', studentID);
    });

    $(document).on('click', '.view_profile', function (event) {
        if (event.ctrlKey || event.metaKey) {
            let studentID = $(this).attr('data-student-id');
            if (studentID != null) {
                let redirectUrl = site_url + 'student-profile-view/' + studentID;
                window.open(redirectUrl, '_blank');
            }
        } else {
            let studentID = $(this).attr('data-student-id');
            if (studentID != null) {
                let redirectUrl = site_url + 'student-profile-view/' + studentID;
                window.location.href = redirectUrl;
            }
        }
    });

    $(document).on('click', '#cancelProfileBtn', function () {
        $('#studentMiniProfileModal_wnd_title')
            .parent('div')
            .find('.k-window-actions a')
            .trigger('click');
    });

    fetch(site_url + 'api/get-college-email-list')
        .then((RES) => RES.json())
        .then(function (newWhitelist) {
            whitelistForReplyToEmail = newWhitelist.data;
        });

    $(document).on('click', '#bulk_email', function () {
        $('#emailLoadingState').show();
        startAjaxLoader();
        myEditor11.setData('');
        let selectedStudentTags = getSelectedStudentTags();
        let filterCourseIds = $('#course_id .k-checkbox:checked')
            .map(function () {
                return this.value;
            })
            .get();

        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-data-for-bulk-email',
            dataType: 'json',
            data: { studIds: selectedStudentTags, course_id: filterCourseIds },
            success: function (response) {
                if (response.status == 'error') {
                    showToster(response.status, response.message);
                    return false;
                }

                kendowindowOpen('#sendMailStudentModal');
                let resArr = response.data;

                // setFromEmail('#sendMailStudentModal');
                $('#sendMailStudentModal').find('#email_from').val(resArr.email);
                /*setCourseDropdownListWithData(
                    "#course_list_for_email",
                    resArr.studentCourse,
                    courseIds[0],
                );

                let emailTypeVal = $('input[name="email_type"]:checked').val();
                let isCourseSelected = emailTypeVal === 'course';
                let isEmailTypeOption = resArr.isEmailTypeOption;
                let isMailNotification = (isEmailTypeOption && isCourseSelected) ? true : false;

                $('.mail__course').toggle(isCourseSelected);
                $('.mail__type').toggle(isEmailTypeOption);
                $('.mail_notification').toggle(isMailNotification);*/

                let isSingleCourse = filterCourseIds.length == 1 && resArr.filterCourseData.id > 0;
                $('.filter_course').toggle(isSingleCourse);
                $('.filter_course_id').val(isSingleCourse ? resArr.filterCourseData.id : '');
                $('.filter_course_name').text(isSingleCourse ? resArr.filterCourseData.text : '');

                setReplyToEmailSetup('#reply_to_email', whitelistForReplyToEmail);

                var input = document.querySelector('input[name=email_bcc]');
                let tagifyEmailBCC = new Tagify(input, {
                    pattern:
                        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                    callbacks: {
                        invalid: onInvalidTag,
                    },
                    templates: {
                        tag: tagTemplateV2,
                    },
                });
                tagifyEmailBCC.removeAllTags();
                var input = document.querySelector('input[name=email_cc]');
                let tagifyEmailCC = new Tagify(input, {
                    pattern:
                        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                    callbacks: {
                        invalid: onInvalidTag,
                    },
                    templates: {
                        tag: tagTemplateV2,
                    },
                });
                tagifyEmailCC.removeAllTags();

                generateSpecialStudentListDropdownForEmail(
                    selectedStudentTags,
                    studNameListForEmailId
                );
                // $(".notReceivedEmailStudentCount").text(
                //     resArr.withoutCourseStudentIds.length,
                // );

                /*let inputV2 = document.querySelector("#student_name_remove_email_list");
                tagifyV2 = new Tagify(
                    inputV2,
                    setTagifyPropertyWithoutCourseStudent(),
                );*/
                //setTagifyDataV2(tagifyV2, resArr.withoutCourseStudentIds);
                //manageActionTagify(tagifyV2);
                /*$("#notReceivedEmailStudentListModal")
                    .find(".tagify__input")
                    .hide();
                $("#notReceivedEmailStudentListModal_wnd_title").text(
                    resArr.withoutCourseStudentIds.length +
                        " Students not receiving email",
                );
                if (resArr.withoutCourseStudentIds.length <= 0) {
                    $(".notReceivedEmailStudentListBtn").hide();
                } else {
                    $(".notReceivedEmailStudentListBtn").show();
                }*/

                setEmailTemplateSelectionData(resArr.emailTemplate);
                setTimeout(() => {
                    $('#emailLoadingState').hide();
                    setTimeout(() => {
                        stopAjaxLoader();
                    }, 100);
                }, 1000);
            },
        });
    });

    $(document).on('click', '.single_email', function () {
        $('#emailLoadingState').show();
        $('.notReceivedEmailStudentListBtn').hide();
        let selectedStudentTags = [
            {
                id: $(this).attr('data-student-id'),
                value: $(this).attr('data-name'),
                name: $(this).attr('data-name'),
                profile_pic: $(this).attr('data-profile'),
                contact: $(this).attr('data-contact'),
            },
        ];
        selectedStudent = selectedStudentTags;
        //setCourseDropdownList("#course_list_for_email", selectedStudentTags);
        setTimeout(function () {
            kendowindowOpen('#sendMailStudentModal');
            generateSpecialStudentListDropdownForEmail(selectedStudentTags, studNameListForEmailId);
        }, 500);
        setTimeout(() => {
            $('#emailLoadingState').hide();
            setTimeout(() => {
                stopAjaxLoader();
            }, 100);
            $('.filter_course').hide();
        }, 1000);
    });

    $('body').on('click', '#insertEmailTemplate', function () {
        kendowindowOpen('#emailTemplatesModal');
    });

    $('body').on('click', '.email_template', function () {
        let templateID = $(this).attr('data-id');
        myEditor11.setData('');

        ajaxActionV2(
            'api/get-mail-content',
            'POST',
            { template_id: templateID },
            function (response) {
                emailSubject = response.data[0].email_subject;
                emailContent = response.data[0].content;

                let emailAttachment = kendo.template($('#attachmentList').html())({
                    files: response.data[0].files,
                });

                $(document).find('.email_template_id').val(templateID);
                $(document).find('.exist_attachment_div').show();
                $(document).find('.exist_attachment').html('').html(emailAttachment);
                manageExistEmailAttachmentId();

                $(document).find('.email_subject').html('').html(emailSubject);
                $(document).find('.email_content').html('').html(emailContent);
            }
        );
    });

    $('body').on('click', '#useEmailTemplateBtn', function () {
        $(document).find('#email_subject').val(emailSubject);
        $(document).find('.isTemplateSelect').text(emailSubject);
        $(document).find('.email_subject_txt').val(emailSubject);
        myEditor11.setData(emailContent);
        $(document).find('.existing_attachment_id').val(existEmailAttachmentId);
        $('#emailTemplatesModal').data('kendoWindow').close();
        //$(document).find("#emailTemplatesModal").getKendoWindow().close();
    });

    $('body').on('click', '.remove_stud', function () {
        let studId = $(this).attr('data-sid');
        let dataRow = $(document)
            .find('.scout_' + studId)
            .parents('tr');
        dataRow.each(function () {
            if ($(this).hasClass('k-state-selected')) {
                $(this).trigger('click');
            }
        });
    });

    $('body').on('click', '.ccmail', function () {
        $(document).find('#emailccbox').toggle();
    });

    $('body').on('click', '.bccmail', function () {
        $(document).find('#emailbccbox').toggle();
    });

    $('body').on('click', '.student_comm_log', function () {
        let ckBox = $(this).prev();
        let ckStatus = !ckBox.is(':checked');
        ckBox.prop('checked', ckStatus);
    });

    /*$("body").on("change", "#email_attachment", function (e) {
        var filename = e.target.files[0].name;
        if (e.target.files.length == 1) {
            $(".selected_file").text(filename);
        } else {
            var totalLength = e.target.files.length - 1;
            var name = filename + " +" + totalLength + " others";
            $(".selected_file").text(name);
        }
    });*/

    $('#email_attachment').change(function (event) {
        const files = event.target.files;
        const attachedFilesContainer = $('#attachedFilesContainer');
        $.each(files, function (index, file) {
            const fileName = file;
            // Create a new element to display the file name
            const fileElement = $('<div>').addClass(
                'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
            );
            const fileNameElement = $('<span>')
                .addClass('truncate')
                .attr('title', fileName.name)
                .text(fileName.name);
            const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                // Remove the file from the list, array, and update UI
                fileElement.remove();
                attachedFiles.splice(attachedFiles.indexOf(fileName), 1);
                $('#email_attachment').val(''); // Reset the file input
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.addClass('mt-2').append(fileElement);

            // Add the file name to the array
            attachedFiles.push(fileName);
        });
    });

    $('body').on('click', '.resetEmail', function () {
        $(document).find('#comments').text('');
        myEditor11.setData('');
        $('.isTemplateSelect').text('');
        $('#student_comm_log').prop('checked', false);
        $('#email_subject').val('');
        $('.existing_attachment_id').val('');
        $('.email_template_id').val('');
        $('.email_template_id').val('');
        $('.selected_file').text('');
    });

    $('body').on('click', '.sendmail', function () {
        let modalDiv = $(this).parent('.k-window');
        let formTag = $('#emailTemplateAddForm');
        let totalImageCount = emailTotalAttachmentAllowed;
        let formData = new FormData(formTag[0]);

        let uqTagsSelected = getSelectedTagsForStudent(formTag);
        if (uqTagsSelected.length == 0) {
            notificationDisplay('Please Select student data', '', 'error');
            return false;
        }

        if (formTag.find('#email_subject').val().length == 0) {
            notificationDisplay('Email subject is required', '', 'error');
            return false;
        }
        if (myEditor11.getData().length == 0) {
            notificationDisplay('Email content is required', '', 'error');
            return false;
        }
        let totalFiles = $('#email_attachment')[0].files.length;
        if (totalFiles <= totalImageCount) {
            let j = 1;
            for (var index = 0; index < totalFiles; index++) {
                formData.append('attachment_file[]', $('#email_attachment')[0].files[index]);
                j++;
            }
        } else {
            notificationDisplay(
                'Please select Only ' + totalImageCount + ' attachments files',
                '',
                'error'
            );
            return false;
        }

        formData.append('email_content', myEditor11.getData());
        formData.append('log_type', 'offer');
        let uqTags = selectedStudent.map((item) => item.id);
        let totalSelected = [...new Set(uqTags.concat(uqTagsSelected).map(Number))];
        formData.append('student_id', totalSelected);

        formData.delete('selected_stud_id');

        if (formData.get('email_cc')) {
            let tags_cc = JSON.parse(formData.get('email_cc'));
            formData.set('email_cc', tags_cc.map((item) => item.value).toString());
        }
        if (formData.get('email_bcc')) {
            let tags_bccc = JSON.parse(formData.get('email_bcc'));
            formData.set('email_bcc', tags_bccc.map((item) => item.value).toString());
        }
        if (formData.get('reply_to_email')) {
            let parsedRes = JSON.parse(formData.get('reply_to_email'));
            if (parsedRes.length > 0 && parsedRes[0].value) {
                formData.set('reply_to_email', parsedRes[0].value);
            }
        }
        modalDiv.addClass('blur-modal');
        let apiUrl = queue_email ? 'api/student-send-email-v2' : 'api/student-send-email';

        ajaxCallWithMethodFileKendoV2('Email', apiUrl, formData, 'POST', function (output) {
            modalDiv.removeClass('blur-modal');
            if (output.responseJSON.status == 'error') {
                kendo.ui.progress($(`#loaderForEmail`), false);
                $('#loaderForEmail').data('kendoWindow').close();
                notificationDisplay(output.responseJSON.message, '', 'error');
            }
            isSelectAll = false;
        });
    });

    $(document).on('click', '#bulk_sms', function () {
        let selectedStudentTags = getSelectedStudentTags();
        setTimeout(function () {
            $('#sms_template').trigger('change');
            kendowindowOpen('#sendSmsStudentModal');
            generateSpecialStudentListDropdownForSms(selectedStudentTags, '#student_name_sms_list');
        }, 500);
    });

    $(document).on('click', '.single_sms', function () {
        let selectedStudentTags = [
            {
                id: $(this).attr('data-student-id'),
                value: $(this).attr('data-name'),
                name: $(this).attr('data-name'),
                profile_pic: $(this).attr('data-profile'),
                contact: $(this).attr('data-contact'),
            },
        ];
        setTimeout(function () {
            $('#sms_template').trigger('change');
            kendowindowOpen('#sendSmsStudentModal');
            generateSpecialStudentListDropdownForSms(selectedStudentTags, '#student_name_sms_list');
        }, 500);
    });

    $('body').on('change', '#sms_template', function () {
        let template_id = $(this).val();
        if (template_id != '' && template_id != 'undefined') {
            let dataArr = { template_id: template_id };
            ajaxcallwithMethod('api/get-sms-template-contain', dataArr, 'POST', function (output) {
                $(document).find('#sms_text').val(output.data);
            });
        }
    });

    $('body').on('click', '#resetSMS', function () {
        $(document).find('#sms_text').val('');
    });

    $('body').on('click', '#sendMsg', function () {
        let formTag = $('#smsTemplateAddForm');
        let idS = getSelectedTagsForStudentWithContact(formTag);
        if (idS.length == 0) {
            notificationDisplay('Please Select student data', '', 'error');
            return false;
        }

        let sms_text = $('.sms_text').val();
        // $(dataArr).each(function (index, studentData) {
        //   dataArr[index].is_send = true;
        //   dataArr[index].message = sms_text;
        // });
        let url = site_url + 'api/student-add-sms-details';
        let modalDiv = $(this).parent('.k-window');
        let dataArr = { id: idS, message: sms_text };
        //selectedStudent replace with uqTags
        ajaxcallwithMethod(url, { dataArr }, 'POST', function (output) {
            notificationDisplay('Send SMS', 'Send SMS', 'success');
            $('#sendSmsStudentModal').data('kendoWindow').close();
            modalDiv.removeClass('blur-modal');
        });
    });

    $('body').on('click', '.existing_attachment', function () {
        $(this).closest('div').remove();
        manageExistEmailAttachmentId();
    });

    $(document).on('click', '.copy_data', function () {
        $(document).find('.copy_data').removeClass('active');
        $(this).addClass('active');
        var textVal = $(this).attr('data-text');
        var $temp = $("<input name='copy'>");
        $('body').append($temp);
        $temp.val(textVal).select();
        document.execCommand('copy');
        $temp.remove();
    });

    $(gridID)
        .kendoTooltip({
            filter: '.student-name-hover', //"td:nth-child(4)",
            // autoHide: false, // For design fix only
            callout: true,
            position: 'right',
            width: 560,
            height: 204,
            showAfter: 500,
            offset: -6,
            animation: {
                open: {
                    effects: 'fade:in',
                    duration: 300,
                },
            },
            show: function (e) {
                // Remove the arrow element from the tooltip
                e.sender.popup.element.find('.k-callout').addClass('k-callout-white');
                e.sender.popup.element.find('.k-callout').css({
                    transform: 'translateY(-50%)',
                });
            },
            content: function (e) {
                return manageTooltip(e, 'full-name');
            },
        })
        .data('kendoTooltip');
    $(gridID).kendoTooltip({
        filter: 'td:nth-child(5) .action-tooltip',
        position: 'bottom-center',
        //width: 300,
        showAfter: 100,
        animation: {
            open: {
                effects: 'fade:in',
                duration: 200,
            },
        },
        content: function (e) {
            return manageTooltip(e, 'college');
        },
    });

    $(gridID).kendoTooltip({
        filter: 'td:nth-child(7) .action-div',
        position: 'bottom-center',
        //width: 300,
        showAfter: 100,
        animation: {
            open: {
                effects: 'fade:in',
                duration: 200,
            },
        },
        content: function (e) {
            return manageTooltip(e, 'current-course');
        },
    });

    $(gridID).kendoTooltip({
        filter: 'td:nth-child(8) .course-unit',
        position: 'bottom-center',
        showAfter: 100,
        width: 200,
        animation: {
            open: {
                effects: 'fade:in',
                duration: 200,
            },
        },
        content: function (e) {
            return manageTooltip(e, 'course-progress');
        },
    });

    $(gridID).kendoTooltip({
        filter: 'td:nth-child(9) .action-div',
        position: 'bottom-center',
        width: 250,
        showAfter: 400,
        animation: {
            open: {
                effects: 'fade:in',
                duration: 200,
            },
        },
        content: function (e) {
            return manageTooltip(e, 'attendance');
        },
    });

    $(gridID).kendoTooltip({
        filter: 'td:nth-child(10) > .action-div',
        position: 'bottom-center',
        width: 250,
        showAfter: 400,
        animation: {
            open: {
                effects: 'fade:in',
                duration: 200,
            },
        },
        content: function (e) {
            return manageTooltip(e, 'payment');
        },
    });

    var actionTooltip = $(gridID)
        .kendoTooltip({
            filter: 'td:nth-child(11) > .actionButtonHide',
            position: 'bottom',
            width: 150,
            showAfter: 100,
            showOn: 'click',
            show: function (e) {
                // Remove the arrow element from the tooltip
                e.sender.popup.element.find('.k-callout').remove();
                e.sender.popup.wrapper.addClass('tw-fadein');
                e.sender.popup.wrapper.css({
                    right: '24px',
                    left: 'unset',
                });
            },
            content: function (e) {
                return manageTooltip(e, 'action-list');
            },
        })
        .data('kendoTooltip');

    $('body').on('mousemove', function (e) {
        var tooltipCell = $('#studentList td:nth-child(11)');
        var tooltipElement = $('.k-tooltip-content');

        // Check if the mouse is outside the tooltip bounds
        if (
            !tooltipElement.is(e.target) &&
            tooltipElement.has(e.target).length === 0 &&
            !tooltipCell.is(e.target) &&
            tooltipCell.has(e.target).length === 0
        ) {
            actionTooltip.hide();
        }
    });

    var forceLeftFlag = true;

    function tooltipLeftForceFully() {
        if (forceLeftFlag) {
            setTimeout(() => {
                $(document).find('.firstTdTooltip').closest('.k-widget').css({ left: '10px' });
            }, 50);
            forceLeftFlag = false;
        }
    }

    function manageTooltip(e, type) {
        let dataItem = $(gridID).data('kendoGrid').dataItem(e.target.closest('tr'));
        let dataArr = { arr: dataItem };
        const course_list = dataItem.course_list;
        // var course_list_array = course_list.split(",@@,");
        if (type == 'full-name') {
            manageStudHistory(dataItem);
            let resHtml = kendo.template($('#tooltipTemplateForFullName').html())(dataArr);
            tooltipLeftForceFully();
            return resHtml;
        } else if (type == 'current-course') {
            dataItem.student_courses[0].course_fullname =
                dataItem.student_courses[0].course.course_code +
                ' - ' +
                dataItem.student_courses[0].course.course_name;

            // DB::raw('(SUM(CASE WHEN a1.final_outcome != "" THEN 1 ELSE 0 END)) as total_unit'),
            // DB::raw('(SUM(CASE WHEN a1.final_outcome = "C" OR a1.final_outcome = "NYC" THEN 1 ELSE 0 END)) as use_unit'),
            // DB::raw('CONCAT(SUM(CASE WHEN a1.final_outcome = "C" THEN 1 ELSE 0 END), " C, ", SUM(CASE WHEN a1.final_outcome = "NYC" THEN 1 ELSE 0 END), " NYC") as title')
            let course_process = [];
            let total_unit = 0;
            let use_unit = 0;
            let c_use_unit = 0;
            let nyc_use_unit = 0;
            $.each(dataItem.student_courses[0]['course_process'], function (index, row) {
                total_unit += row.final_outcome != '' ? 1 : 0;
                use_unit += row.final_outcome == 'C' || (row.final_outcome = 'NYC') ? 1 : 0;
                c_use_unit += row.final_outcome == 'C' ? 1 : 0;
                nyc_use_unit += row.final_outcome == 'NYC' ? 1 : 0;
            });
            course_process.push({
                total_unit: total_unit,
                use_unit: use_unit,
                title: c_use_unit + ' C, ' + nyc_use_unit + ' NYC ',
            });
            return kendo.template($('#tooltipTemplateForCurrentCourse').html())({
                arr: dataItem.student_courses[0],
                arr2: course_process,
                course_list: dataItem.student_courses,
            });
        } else if (type == 'course-progress') {
            return kendo.template($('#tooltipTemplateForCourseProgress').html())({
                unit_name: e.target.attr('data-unit-title'),
            });
        } else if (type == 'attendance') {
            return kendo.template($('#tooltipTemplateForAttendance').html())({
                arr: dataItem,
            });
        } else if (type == 'payment') {
            if (dataItem.initial_payments.length > 0) {
                return kendo.template($('#tooltipTemplateForPayment').html())({
                    arr: dataItem.initial_payments[0],
                });
            }
        } else if (type == 'action-list') {
            manageStudHistory(dataItem);
            return kendo.template($('#actionListTemplate').html())({
                id: dataItem.secure_id,
                sId: dataItem.id,
                username: dataItem.username,
                student_id: dataItem.student_id,
                student_name: dataItem.student_name,
                email: dataItem.email,
                contact: dataItem.contact,
                profile: dataItem.profile_pic,
                college_name: dataItem.college_name,
                first_name: dataItem.first_name,
                family_name: dataItem.family_name,
            });
        } else if (type == 'college') {
            return kendo.template($('#tooltipTemplateForCollege').html())({
                campuses: dataItem.allCampusName,
            });
        }
    }

    function manageStudHistory(dataItem) {
        let dataItemArr = {
            arr: dataItem,
            arr1: dataItem.student_courses[0],
            // arr2: dataItem.student_courses[0]["current_course"][0],
            // arr3: dataItem.student_courses[0]["course_attendance"],
            arr2: [],
            arr3: [],
        };
        let studHistoryTemplate = kendo.template($('#studHistoryTemplate').html())(dataItemArr);
        $(document).find('#studHistoryData').html(studHistoryTemplate);
        // manageStepperData(dataItem.course_detail[0].course_progress);
    }

    // manage click for outside any div or right click call
    $(document).click(function (e) {
        // use for columns filter on header
        if (
            !$('#manageColumns').is(e.target) &&
            $('#manageColumns').has(e.target).length === 0 &&
            $('.active').has(e.target).length === 0
        ) {
            $('#manageColumns').parent().removeClass('active');
        }
    });

    // On escape press remove modal with selected data
    document.onkeydown = function (evt) {
        evt = evt || window.event;
        var isEscape = false;
        if ('key' in evt) {
            isEscape = evt.key === 'Escape' || evt.key === 'Esc';
        } else {
            isEscape = evt.keyCode === 27;
        }
        if (isEscape) {
            if ($(document).find('#action').hasClass('bottomaction')) {
                $('#selectedStudents').trigger('click');
                $(document).find('#moreActionModal').hide();
            }
        }
    };

    CKEDITOR.ClassicEditor.create(document.querySelector('#comments'), {
        ckfinder: {
            uploadUrl: site_url + 'api/upload-file-email-text-editor',
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: window.tagJson,
                    minimumCharacters: 0,
                },
            ],
        },
        removePlugins: [
            'RealTimeCollaborativeComments',
            'RealTimeCollaborativeTrackChanges',
            'RealTimeCollaborativeRevisionHistory',
            'PresenceList',
            'Comments',
            'TrackChanges',
            'TrackChangesData',
            'RevisionHistory',
            'Pagination',
            'WProofreader',
            'MathType',
        ],
    })
        .then((editor) => {
            editor.ui.view.editable.element.style.height = '200px';
            myEditor11 = editor;
        })
        .catch((error) => {
            console.error(error);
        });

    $('#closeLetterModelConformation').kendoWindow(openCenterWindow('Discard Letter'));

    // Issue letter
    $(issueLetterStudentModalId).kendoWindow(
        defaultWindowSlideFormat('Generate Warning Letter', 95)
    );
    $(issueLetterStudentModalId)
        .data('kendoWindow')
        .bind('open', function (e) {
            confirmLatter = false;
        });

    $(issueLetterStudentModalId)
        .data('kendoWindow')
        .bind('close', function (e) {
            if (!confirmLatter) {
                kendowindowOpen('#closeLetterModelConformation');
                confirmLatter = false;
                e.preventDefault();
            }
        });

    $('body').on('click', '.letterDiscard-yes', function (e) {
        confirmLatter = true;
        $('#closeLetterModelConformation').data('kendoWindow').close();
        $(issueLetterStudentModalId).data('kendoWindow').close();
        myEditor22.setData('');
        $('#selectedStudents').trigger('click');
        $('#student_comm_log_for_letter').prop('checked', false);
        removeTagifyForcecfully();
        studentGridResetAfterDiscard();
    });

    $('body').on('click', '.discard-no', function (e) {
        $('#closeLetterModelConformation').data('kendoWindow').close();
    });

    $('#loaderForLetter').kendoWindow(windowGenerateLetter('Generate Warning Letter'));
    $('#statusForSendLetterModal').kendoWindow(windowGenerateLetter('Letter Status'));

    $('body').on('click', '#insertLetterTemplate', function () {
        kendowindowOpen('#letterTemplatesModal');
    });

    $('body').on('click', '.letter_template', function () {
        let templateID = $(this).attr('data-id');
        myEditor22.setData('');

        ajaxActionV2(
            'api/get-letter-content',
            'POST',
            { template_id: templateID },
            function (response) {
                let letterSubject = response.data[0].letter_name;
                letterContent = response.data[0].content;

                let letterAttachment = kendo.template($('#letterAttachmentList').html())({
                    files: response.data[0].files,
                });
                $(document).find('.letter_exist_attachment_div').show();
                $(document).find('.letter_exist_attachment').html('').html(letterAttachment);
                manageExistLetterAttachmentId();

                $(document).find('.letter_template_id').val(templateID);
                $(document).find('.letter_subject').html('').html(letterSubject);
                $(document).find('.letter_content').html('').html(letterContent);
                $(document).find('#previewLetter').html(letterContent);
            }
        );
    });

    $('body').on('click', '#useLetterTemplateBtn', function (e) {
        e.preventDefault();
        let formTag = $('#generateWarningLetterForm');
        let uqTags = getSelectedTagsForStudent(formTag);
        let filterCourseIds = $('#course_id .k-checkbox:checked')
            .map(function () {
                return this.value;
            })
            .get();
        startAjaxLoader();

        if (uqTags.length > 1) {
            $(document).find('#letter_subject').val(letterSubject);
            $(document).find('.letter_subject_txt').val(letterSubject);
            myEditor22.setData(letterContent);
            $(document).find('.letter_existing_attachment_id').val(existLetterAttachmentId);
            $(document).find('.preview-placeholder').hide();
            $(document).find('#previewLetter').show().html(letterContent);
            $('#letterTemplatesModal').data('kendoWindow').close();
            let dataItems = {
                letter_template_id: $('.letter_template_id').val(),
                student_id: uqTags[uqTags.length - 1],
                course_id:
                    filterCourseIds.length == 1 && filterCourseIds[0] > 0 ? filterCourseIds[0] : '',
            };
            setLetterParameterInModel(dataItems);
        } else {
            ajaxActionV2(
                'api/get-letter-content-with-tag-value',
                'POST',
                {
                    letterContent: letterContent,
                    student_id: uqTags[uqTags.length - 1],
                    course_id:
                        filterCourseIds.length == 1 && filterCourseIds[0] > 0
                            ? filterCourseIds[0]
                            : '',
                },
                function (response) {
                    $(document).find('#letter_subject').val(letterSubject);
                    $(document).find('.letter_subject_txt').val(letterSubject);
                    myEditor22.setData(response.data);
                    $(document).find('.letter_existing_attachment_id').val(existLetterAttachmentId);
                    $(document).find('.preview-placeholder').hide();
                    $(document).find('#previewLetter').show().html(response.data);
                    $('#letterTemplatesModal').data('kendoWindow').close();
                    let dataItems = {
                        letter_template_id: $('.letter_template_id').val(),
                        student_id: uqTags[uqTags.length - 1],
                        course_id:
                            filterCourseIds.length == 1 && filterCourseIds[0] > 0
                                ? filterCourseIds[0]
                                : '',
                    };
                    setLetterParameterInModel(dataItems);
                    stopAjaxLoader();
                }
            );
        }
        // ajaxActionV2(
        //     "api/get-letter-content-with-tag-value",
        //     "POST",
        //     {
        //         letterContent: letterContent,
        //         student_id: uqTags[uqTags.length - 1],
        //         course_id:
        //             filterCourseIds.length == 1 && filterCourseIds[0] > 0
        //                 ? filterCourseIds[0]
        //                 : "", //filterCourseIds,
        //         //student_course_id: selectedStudCourseID,
        //     },
        //     function (response) {
        //         $(document).find("#letter_subject").val(letterSubject);
        //         $(document).find(".letter_subject_txt").val(letterSubject);
        //         myEditor22.setData(response.data);
        //         $(document)
        //             .find(".letter_existing_attachment_id")
        //             .val(existLetterAttachmentId);
        //         $(document).find(".preview-placeholder").hide();
        //         // generateContentToPdf(response.data);
        //         $(document).find("#previewLetter").show().html(response.data);
        //         $("#letterTemplatesModal").data("kendoWindow").close();
        //         let dataItems = {
        //             letter_template_id: $(".letter_template_id").val(),
        //             student_id: uqTags[uqTags.length - 1],
        //             course_id:
        //                 filterCourseIds.length == 1 && filterCourseIds[0] > 0
        //                     ? filterCourseIds[0]
        //                     : "",
        //         };
        //         setLetterParameterInModel(dataItems);
        //         stopAjaxLoader();
        //     },
        // );
    });

    function generateContentToPdf(content) {
        let dataArr = [{ contentText: content }];

        // Convert to JSON and send as data in the request body
        let url = site_url + 'api/generate-content-to-pdf';

        $('.embed-content').show();

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                data: JSON.stringify(dataArr),
            },
            xhrFields: {
                responseType: 'blob', // Important for binary data
            },
            success: function (response) {
                getPdf(response);
            },
            error: function (error) {
                console.error('Error: ', error);
            },
        });
    }

    function setLetterParameterInModel(dataItems) {
        let formTag = $('#generateWarningLetterForm');
        let uqTags = getSelectedTagsForStudent(formTag);
        $('#letterParameterModal').find('#student_id').val(dataItems.student_id);
        $('#letterParameterModal').find('#course_id').val(dataItems.course_id);
        dataItems.request_from = 'student-list';
        dataItems.student_count = uqTags.length;

        startAjaxLoader();
        ajaxActionV2('api/get-letter-parameter-list', 'POST', dataItems, function (response) {
            if (response.data.parameter.length > 0 && !response.data.isCourseParameterFound) {
                displayParameterList(response.data.parameter);
                kendowindowOpen('#letterParameterModal');
            }
            if (response.data.isCourseParameterFound) {
                resticUserToGenerateLetter(response.data.parameter);
                //notificationDisplay("Course parameter found in the letter template. Please select the course from the filter.", "", "error");
            }
            startAjaxLoader();
        });
    }

    function resticUserToGenerateLetter(parameter) {
        $(letterDiscardConfirmation).kendoDialog({
            width: '400px',
            title: 'Alert',
            content:
                'You are using course parameters / tags  on your template. Please choose course from the filter before proceeding.<br/><br/> Eg - {OfferNo},{OfferId},{CampusName},{CourseCode},{CourseName},{CourseStartDate},{CourseEndDate},{CourseDuration},{AgencyName},{AgentEmail},{AgentTelephone},{CourseType},{Campus}',
            actions: [
                // { text: "Close" },
                {
                    text: 'Close',
                    primary: true,
                    action: function () {
                        $('.letterDiscard-yes').trigger('click');
                    },
                },
            ],
            visible: false,
        });

        let $enrollModal = $(letterDiscardConfirmation);
        $enrollModal.data('kendoDialog').open();
    }
    function displayParameterList(parameter) {
        if (parameter.length > 0) {
            $('#addDeptModal').modal('show');
            var html = '';
            $.each(parameter, function (i, item) {
                html +=
                    '<div class="inline-flex flex-col space-y-1 items-start justify-start w-full">' +
                    '<label class="k-label k-form-label">' +
                    item +
                    ' : <span class="required-field">*</span></label>' +
                    '<div class="inline-flex flex-col space-y-1 items-start justify-start w-full">' +
                    '<input name="' +
                    item +
                    '" data-name="' +
                    item +
                    '" type="text" class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full newParameter" placeholder="Enter ' +
                    item +
                    ' Value">' +
                    '</div>' +
                    '</div>';
            });
            $('#parameterList').html(html);
        }
    }
    $('body').on('click', '.replaceParameter', function () {
        assignParameterValue();
    });
    function assignParameterValue() {
        var arr = [];
        var flag = true;
        $('.newParameter').each(function () {
            if ($(this).val()) {
                $(this).removeClass('error');
                arr.push({
                    key: $(this).attr('data-name'),
                    value: $(this).val(),
                });
            } else {
                flag = false;
                $(this).addClass('error');
            }
        });
        if (flag) {
            replaceInputParameterValue(arr);
        }
    }

    function replaceInputParameterValue(arr) {
        let formTag = $('#generateWarningLetterForm');
        let uqTags = getSelectedTagsForStudent(formTag);
        let student_id = $('#letterParameterModal').find('#student_id').val();
        let course_id = $('#letterParameterModal').find('#course_id').val();
        let letterId = $('.letter_template_id').val();
        let request_from = 'student-list';
        let student_count = uqTags.length;
        var dataItems = {
            letter_template_id: letterId,
            student_id: student_id,
            course_id: course_id,
            arr: arr,
            request_from: request_from,
            student_count: student_count,
        };
        if (letterId > 0) {
            ajaxActionV2(
                'api/get-replace-parameter-content',
                'POST',
                dataItems,
                function (response) {
                    myEditor22.setData(response.data.content);
                    $('#letterParameterModal').data('kendoWindow').close();
                }
            );
        }
    }
    $('body').on('click', '.letter_existing_attachment', function () {
        $(this).closest('div').remove();
        manageExistLetterAttachmentId();
    });

    $(document).on('click', '.issue_letter', function () {
        $('#emailLoadingState').show();
        startAjaxLoader();
        myEditor22.setData('');
        $(document).find('#previewLetter').html('');
        $('#student_comm_log_for_letter').prop('checked', false);
        let selectedStudentTags = getSelectedStudentTags();
        //setCourseDropdownList("#course_list_for_letter", selectedStudentTags);

        let filterCourseIds = $('#course_id .k-checkbox:checked')
            .map(function () {
                return this.value;
            })
            .get();

        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-data-for-bulk-letter',
            dataType: 'json',
            data: { studIds: selectedStudentTags, course_id: filterCourseIds },
            success: function (response) {
                if (response.status == 'error') {
                    showToster(response.status, response.message);
                    return false;
                }

                kendowindowOpen(issueLetterStudentModalId);
                let resArr = response.data;

                let isSingleCourse = filterCourseIds.length == 1 && resArr.filterCourseData.id > 0;
                $('.filter_course').toggle(isSingleCourse);
                $('.filter_course_id').val(isSingleCourse ? resArr.filterCourseData.id : '');
                $('.filter_course_name').text(isSingleCourse ? resArr.filterCourseData.text : '');

                generateSpecialStudentListDropdownForLetter(
                    selectedStudentTags,
                    studNameListForLetterId
                );

                /*let inputV2 = document.querySelector("#student_name_remove_email_list");
                tagifyV2 = new Tagify(
                    inputV2,
                    setTagifyPropertyWithoutCourseStudent()
                );
                manageActionTagify(tagifyV2);*/

                setLetterTemplateSelectionData(resArr.letterTemplate);

                setTimeout(() => {
                    $('#emailLoadingState').hide();
                    setTimeout(() => {
                        stopAjaxLoader();
                    }, 100);
                }, 1000);
            },
        });

        setTimeout(function () {
            kendowindowOpen(issueLetterStudentModalId);
            generateSpecialStudentListDropdownForLetter(
                selectedStudentTags,
                studNameListForLetterId
            );
        }, 500);
    });
    $(document).on('click', '.issue_single_letter', function () {
        let selectedStudentTags = [
            {
                id: $(this).attr('data-student-id'),
                value: $(this).attr('data-name'),
                name: $(this).attr('data-name'),
                profile_pic: $(this).attr('data-profile'),
                contact: $(this).attr('data-contact'),
            },
        ];
        let filterCourseIds = $('#course_id .k-checkbox:checked')
            .map(function () {
                return this.value;
            })
            .get();

        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-data-for-bulk-letter',
            dataType: 'json',
            data: { studIds: selectedStudentTags, course_id: filterCourseIds },
            success: function (response) {
                if (response.status == 'error') {
                    showToster(response.status, response.message);
                    return false;
                }

                let resArr = response.data;

                let isSingleCourse = filterCourseIds.length == 1 && resArr.filterCourseData.id > 0;
                $('.filter_course').toggle(isSingleCourse);
                $('.filter_course_id').val(isSingleCourse ? resArr.filterCourseData.id : '');
                $('.filter_course_name').text(isSingleCourse ? resArr.filterCourseData.text : '');

                generateSpecialStudentListDropdownForLetter(
                    selectedStudentTags,
                    studNameListForLetterId
                );
                setLetterTemplateSelectionData(resArr.letterTemplate);
                kendowindowOpen(issueLetterStudentModalId);
                setTimeout(() => {
                    $('#emailLoadingState').hide();
                    setTimeout(() => {
                        stopAjaxLoader();
                    }, 100);
                }, 1000);
            },
        });
    });

    $(document).on('click', '.student_comm_log_for_letter', function () {
        let ckBox = $(this).prev();
        let ckStatus = !ckBox.is(':checked');
        ckBox.prop('checked', ckStatus);
    });

    $(document).on('click', '.sendLetterEmail', function () {
        let modalDiv = $(this).parent('.k-window');
        let formTag = $('#generateWarningLetterForm');

        if (validateGenerateLetterForm(formTag)) {
            let formData = getAndSetTotalSelectedStudent(formTag);
            updateFormData(formData, 'letter_content', myEditor22.getData());
            updateFormData(formData, 'log_type', 'offer');

            modalDiv.addClass('blur-modal');
            ajaxCallWithMethodFileKendoV2(
                'sendLetter',
                'api/student-issue-letter-email',
                formData,
                'POST',
                function (output) {
                    //refreshGridData();
                    modalDiv.removeClass('blur-modal');
                    $(document).find('.letterZipDownload').hide();
                }
            );
        }
    });

    $(document).on('click', '.generateLetter', function () {
        let formTag = $('#generateWarningLetterForm');
        if (validateGenerateLetterForm(formTag)) {
            generateLetterProcess('api/student-issue-letter-generate');
        }
    });

    $(document).on('click', '.generateLetterWithWatermark', function () {
        let formTag = $('#generateWarningLetterForm');
        if (validateGenerateLetterForm(formTag)) {
            ajaxActionV2(
                'api/verify-letter-watermark',
                'POST',
                { college_id: '' },
                function (response) {
                    if (response.status == 'success') {
                        generateLetterProcess('api/student-issue-letter-generate-with-watermark');
                    } else {
                        notificationDisplay(response.message, '', 'error');
                        return false;
                    }
                }
            );
        }
    });

    function validateGenerateLetterForm(formTag) {
        let uqTags = getSelectedTagsForStudent(formTag);

        if (uqTags.length == 0) {
            notificationDisplay('Please Select student data', '', 'error');
            return false;
        }

        if (myEditor22.getData().length == 0) {
            notificationDisplay('Letter content is required', '', 'error');
            return false;
        }
        return true;
    }

    function getAndSetTotalSelectedStudent(formTag) {
        let formData = new FormData(formTag[0]);
        let uqTagsSelected = getSelectedTagsForStudent(formTag);

        let uqTags = selectedStudent.map((item) => item.id);
        let totalSelected = [...new Set(uqTags.concat(uqTagsSelected).map(Number))];

        // Update form data with the new totalSelected array
        updateFormData(formData, 'student_id', totalSelected);

        return formData;
    }

    function generateLetterProcess(apiURL) {
        let modalDiv = $(this).parent('.k-window');
        let formTag = $('#generateWarningLetterForm');
        let formData = getAndSetTotalSelectedStudent(formTag);

        updateFormData(formData, 'letter_content', myEditor22.getData());
        updateFormData(formData, 'log_type', 'offer');

        modalDiv.addClass('blur-modal');
        ajaxCallWithMethodFileKendoV2(
            'generateLetter',
            apiURL,
            formData,
            'POST',
            function (output) {
                modalDiv.removeClass('blur-modal');
            }
        );
    }

    $(document).on('click', '.closeAndRefreshGrid', function () {
        let type = $(this).attr('data-type');
        $(`#statusForSend${type}Modal`).data('kendoWindow').close();
        $('.k-i-close').trigger('click');
        //refreshGridData();
    });

    function setCourseDropdownList(inputIdName, selectedStudentTags) {
        let studIds = $.map(selectedStudentTags, function (item) {
            return item.id;
        });
        $(inputIdName)
            .html('')
            .kendoDropDownList({
                dataTextField: 'text',
                dataValueField: 'value',
                dataSource: {
                    schema: {
                        data: 'data',
                    },
                    transport: {
                        read: {
                            //url: site_url + "api/communication-course-data",
                            url: site_url + 'api/student-course-list',
                            dataType: 'json',
                            type: 'POST',
                            data: { studIds: studIds },
                        },
                    },
                },
            });
        //$("#course_name").data("kendoDropDownList").list.width("auto");
    }

    function setCourseDropdownListWithData(inputIdName, dataItems, value) {
        $(inputIdName).html('').kendoDropDownList({
            dataTextField: 'text',
            dataValueField: 'value',
            dataSource: dataItems,
            value: value,
        });
    }

    function getSelectedStudentTags() {
        let selectedStudentTags = [];
        $.each(selectedStudent, function (index, row) {
            selectedStudentTags.push({
                id: row.id,
                value: row.id,
                name: row.name,
                profile_pic: manageStudentProfilePicForTagify(
                    row.id,
                    row.profile_pic,
                    row.name,
                    row.contact
                ),
                contact: row.contact,
            });
        });
        return selectedStudentTags;
    }

    function zipDownloadSetup(res, typeName) {
        let zipDownloadDiv = $(document).find('.letterZipDownload');
        zipDownloadDiv.html('');
        if (res && res.file_name && typeName == 'generateLetter') {
            let downloadURL = site_url + res.file_name;
            zipDownloadDiv.html(
                `<button class="ml-3 flex justify-center w-auto h-6 px-3 py-1 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500">
                    <a href="${downloadURL}" id="downloadZipLink" class="flex space-x-2 items-center justify-start" title="Download Zip File">
                        <p class="text-sm font-medium leading-tight text-white">Download</p>
                    </a>
                </button>`
            );
        }
        $(document)
            .find('.queueOptionDiv')
            .toggle(typeName === 'sendLetter');
    }

    $('#downloadZipLink').on('click', function (e) {
        e.preventDefault(); // Prevent default behavior to ensure download works

        let url = $(this).attr('href');
        if (url) {
            let a = document.createElement('a');
            a.href = url;
            a.download = url.split('/').pop(); // This extracts the filename from the URL
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a); // Clean up after the download
        }
    });

    function zipDownload(formData) {
        ajaxActionV2('api/student-issue-letter-zip', 'POST', formData, function (response) {
            let zipDownloadDiv = $(document).find('.letterZipDownload');
            zipDownloadDiv.html('');
            if (response.status == 'success') {
                let downloadURL = site_url + response.data.download_path;
                let ApiUrl =
                    site_url + response.data.url + '?filePath=' + response.data.download_path;
                let mainUrl = response.data.url == '' ? downloadURL : ApiUrl;
                let downloadIcon = site_url + '/v2/img/download_arrow.svg';
                let zipDownloadHtml = `<button class="ml-3 flex justify-center w-auto h-6 px-3 py-1 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500">
                                        <a download href="${mainUrl}" class="flex space-x-2 items-center justify-start" title="Download Zip File">
                                            <p class="text-sm font-medium leading-tight text-white">Download</p>
                                        </a>
                                    </button>`;
                zipDownloadDiv.html(zipDownloadHtml);
            }
        });
    }

    function manageStudentProfilePicForTagify(id, profile_pic, nameStr, contact) {
        // Manage user name with profile picture or default 2 characters
        let html = '';
        if (profile_pic == '') {
            let displayName = 'NA';
            if (typeof nameStr !== undefined && nameStr != null) {
                let name = nameStr.toUpperCase().split(/\s+/);
                displayName =
                    name.length >= 2
                        ? name[0].charAt(0) + name[1].charAt(0)
                        : name[0].substring(0, 2);
            } else {
                nameStr = 'N/A';
            }
            html =
                "<div class='flex items-center stud_" +
                id +
                " space-x-2 studentNameDiv'><div class='user-profile-pic h-8 w-8 max-w-8 flex items-center justify-center flex-shrink-0 rounded-full bg-blue-500'><span class='text-xs leading-6 font-medium'>" +
                displayName +
                '</span></div></div>';
        } else {
            html =
                "<div class='flex items-center stud_" +
                id +
                " space-x-2 studentNameDiv'><img class='h-8 w-8 object-cover rounded-full flex object-top' src='" +
                profile_pic +
                "' alt=''></div>";
        }
        return html;
    }

    function windowGenerateLetter(title) {
        return {
            title: title,
            width: '30%',
            height: '30%',
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: '30%',
                left: '35%',
            },
            animation: {
                close: {
                    effects: 'fade:out',
                },
            },
        };
    }

    function manageExistEmailAttachmentId() {
        let existDocDiv = $(document).find('.existing_attachment');
        existEmailAttachmentId = [];
        if (existDocDiv.length == 0) {
            let noEmailAttachmentHtml =
                '<p class="text-xs leading-tight text-center text-gray-800"> No attachments available </p>';
            $(document).find('.exist_attachment').html('').html(noEmailAttachmentHtml);
        } else {
            existDocDiv.each(function () {
                existEmailAttachmentId.push($(this).attr('data-file-id'));
            });
        }

        $(document).find('.existing_attachment_id').val(existEmailAttachmentId);

        const attachedFilesContainer = $('#templateFilesContainer');
        attachedFilesContainer.html('');
        $.each(existDocDiv, function () {
            const that = $(this);
            const fileName = $(this).attr('data-file-name');

            // Create a new element to display the file name
            const fileElement = $('<div>').addClass(
                'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
            );
            const fileNameElement = $('<span>')
                .addClass('truncate')
                .attr('title', fileName)
                .text(fileName);
            const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                fileElement.remove();
                that.closest('div').remove();
                manageExistEmailAttachmentId();
                return false;
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.addClass('mt-2').append(fileElement);
        });
    }

    function manageExistLetterAttachmentId() {
        let existDocDiv = $(document).find('.letter_existing_attachment');
        existLetterAttachmentId = [];
        if (existDocDiv.length == 0) {
            let noLetterAttachmentHtml =
                '<p class="text-xs leading-tight text-center text-gray-800"> No attachments available </p>';
            $(document).find('.letter_exist_attachment').html('').html(noLetterAttachmentHtml);
        } else {
            existDocDiv.each(function () {
                existLetterAttachmentId.push($(this).attr('data-file-id'));
            });
        }
    }

    function ajaxCallWithMethodFileKendoV2(typeName, url, data, method, callback) {
        let type = typeName == 'generateLetter' || typeName == 'sendLetter' ? 'Letter' : typeName;
        var rtrn = $.ajax({
            type: method,
            url: url,
            data: data,
            processData: false,
            contentType: false,
            headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
            beforeSend: function () {
                kendowindowOpen(`#loaderFor${type}`);
                kendo.ui.progress($(`#loader${type}`), true);
            },
            success: function (result) {
                let studRes = result.data;
                // let studRes = JSON.parse(result)
                kendo.ui.progress($(`#loaderFor${type}`), false);
                $('#loaderFor' + type)
                    .data('kendoWindow')
                    .close();

                $(document).find('.letter_content').html('');
                $(document).find('#previewLetter').html('');
                myEditor22.setData('');
                $('#student_comm_log_for_letter').prop('checked', false);

                kendowindowOpen(`#statusForSend${type}Modal`);

                let successMsgText = studRes.success_msg ?? '';
                let failMsgText = studRes.fail_msg ?? '';

                $(`#title${type}SuccessMsg`).text(successMsgText);
                $(`#title${type}FailMsg`).text(failMsgText);
                $(`.title${type}FailMsg`).toggle(!!studRes.fail_msg);

                if (result.status == 'success') {
                    zipDownloadSetup(studRes, typeName); //zipDownload();
                }
            },
            error: function (result) {
                // kendo.ui.progress($(document.body), false);
                callback(result);
            },
        });
        return rtrn;
    }

    function convertSpecialData(tagData, type = '') {
        let nameStr = tagData.name;
        let shortName = 'NA';
        let detailData = '';
        if (tagData.profile_pic == '') {
            if (typeof nameStr !== undefined && nameStr != null) {
                let name = nameStr.toUpperCase().split(/\s+/);
                shortName =
                    name.length >= 2
                        ? name[0].charAt(0) + name[1].charAt(0)
                        : name[0].substring(0, 2);
            } else {
                nameStr = 'N/A';
            }
        }
        if (type == 'email') {
            detailData =
                typeof tagData.email !== undefined && tagData.email != null ? tagData.email : 'N/A';
        } else if (type == 'sms') {
            detailData =
                typeof tagData.contact !== undefined && tagData.contact != null
                    ? tagData.contact
                    : 'N/A';
        }
        return {
            id: tagData.id,
            shortName: shortName,
            fullName: nameStr,
            profilePic: tagData.profile_pic,
            detail: detailData,
        };
    }

    function getSelectedTagsForStudent(formTag) {
        let tagDiv = $(formTag).find('.tagify').find('.tagify__tag');
        let tags = [];
        for (var i = tagDiv.length; i--; ) {
            tags.push($(tagDiv[i]).attr('id'));
        }
        let uqTags = $.unique(tags);
        return uqTags;
    }

    function getSelectedTagsForStudentWithContact(formTag) {
        let tagDiv = $(formTag).find('.tagify').find('.tagify__tag');
        let tags = [];
        for (var i = tagDiv.length; i--; ) {
            tags.push({
                id: $(tagDiv[i]).attr('id'),
                contact: $(tagDiv[i]).attr('contact'),
            });
        }
        let uqTags = $.unique(tags);
        return uqTags;
    }

    function tagTemplate(tagData) {
        let res = convertSpecialData(tagData);

        /*let studHtml = '';
        if (res.profilePic == '') {
            studHtml = `<div class='flex items-center tag-div'>&nbsp;<div class='text-xs leading-normal text-gray-900 pl-1'>${res.fullName}</div></div>`;
        }else{
            studHtml = `<div class="tag-div">&nbsp;<div class='w-5/6 text-base leading-normal text-gray-900 pl-1'>${res.fullName}</div></div>`
        }*/

        if (res.id == '0') {
            return `<tag title="${
                res.fullName
            }" contenteditable='false' readonly='true' spellcheck='false' tabIndex="-1" class="tagify__tag--more tagify__tag bg-blue-200 " ${this.getAttributes(
                tagData
            )}>
                        <x title='' class='tagify__tag__removeBtn removeBtn' ${this.getAttributes(
                            tagData
                        )} role='button' aria-label='remove tag'></x>
                        <div class='flex items-center tag-div cursor-pointer showOthersStudentList'>&nbsp;
                        <div class='text-xs text-blue-900 pl-1 leading-tight tracking-wide'>${
                            res.fullName
                        }<span class='k-icon k-i-arrow-60-down'></span></div>
                        </div>
                    </tag>`;
        } else {
            return `<tag title="${
                res.fullName
            }" contenteditable='false'  spellcheck='false' tabIndex="-1" class="tagify__tag bg-gray-300" ${this.getAttributes(
                tagData
            )}>
                    <x title='' class='tagify__tag__removeBtn removeBtn' ${this.getAttributes(
                        tagData
                    )} role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>&nbsp;
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${res.fullName}</div>
                    </div>
                </tag>`;
        }
    }

    function suggestionItemTemplateEmail(tagData) {
        let res = convertSpecialData(tagData, 'email');
        return `<div ${this.getAttributes(tagData)} class='tagify__dropdown__item ${
            tagData.class ? tagData.class : ''
        }' tabindex="0" role="option">${convertStudentHtml(res)}</div>`;
    }

    function suggestionItemTemplateSms(tagData) {
        let res = convertSpecialData(tagData, 'sms');
        return `<div ${this.getAttributes(tagData)} class='tagify__dropdown__item ${
            tagData.class ? tagData.class : ''
        }' tabindex="0" role="option">${convertStudentHtml(res)}</div>`;
    }

    function tagTemplateStudentList(tagData) {
        let res = convertSpecialData(tagData);
        return `<tag title="${res.fullName}" contenteditable='false' spellcheck='false' tabIndex="-1" class="tagify__tag w-full" ${this.getAttributes(tagData)}>
                    <x title='' class='tagify__tag__removeBtn' ${this.getAttributes(
                        tagData
                    )} role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>
                    &nbsp;
                    ${tagData.profile_pic}
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${res.fullName}</div>
                    </div>
                </tag>`;
    }

    function convertStudentHtml(res) {
        let studHtml = '';
        if (res.profilePic == '') {
            studHtml = `<div class='flex items-center'>
                            <div class='user-profile-pic h-6 w-6 rounded-full bg-blue-500'>
                                <span class='text-xs leading-none tracking-wider'>${res.shortName}</span>
                            </div>&nbsp;
                            <div class="inline-flex flex-col items-start justify-start flex-1">
                                <p class="text-xs font-medium leading-tight text-gray-800">${res.fullName}</p>
                                <p class="text-xs leading-none text-gray-800">${res.detail}</p>
                            </div>
                        </div>`;
        } else {
            studHtml = `<div class='flex items-center'>
                            <img class='h-6 w-6 rounded-full' src='${res.profilePic}' alt=''>&nbsp;
                            <div class="w-5/6 inline-flex flex-col items-start justify-start flex-1">
                                <p class="text-xs font-medium leading-tight text-gray-800">${res.fullName}</p>
                                <p class="text-xs leading-none text-gray-800">${res.detail}</p>
                            </div>
                        </div>`;
        }
        return studHtml;
    }
    var queryParams = getQueryParams();
    $.ajax({
        url: site_url + 'api/student-list-data',
        method: 'POST',
        dataType: 'json',
        data: queryParams,
        success: function (response) {
            defaultWhitelist = response.data;
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.error('Error fetching data:', textStatus, errorThrown);
        },
    });

    function setTagifyPropertyEmail() {
        defaultWhitelist.push({
            id: '0',
            value: 'ss',
            name: 'View More',
            profile_pic:
                "<div class='flex items-center stud_ss space-x-2 studentNameDiv'><img class='h-8 w-8 object-cover rounded-full flex object-top' src='' alt='' /><div class='flex items-center stud_72 space-x-2 studentNameDiv'><div class='user-profile-pic h-8 w-8 max-w-8 flex items-center justify-center flex-shrink-0 rounded-full bg-blue-500'><span class='text-xs leading-6 font-medium'>MA</span></div></div>' alt=''></div>",
            contact: '',
        });
        return {
            delimiters: ',| ',
            trim: false,
            tagTextProp: 'name',
            skipInvalid: true,
            dropdown: {
                closeOnSelect: false,
                enabled: 1,
                position: 'text',
                classname: 'student-list',
                searchKeys: ['name'],
            },
            whitelist: defaultWhitelist,
            //whitelist: selectedStudent,
            placeholder: 'Search Student Here...',
            enforceWhitelist: true,
            templates: {
                tag: tagTemplate,
                dropdownItem: suggestionItemTemplateEmail,
                dropdownItemNoMatch: function (data) {
                    return `<div class='${this.settings.classNames.dropdownItem}' value="noMatch" tabindex="0" role="option">
                                No student found for: <strong>${data.value}</strong>
                            </div>`;
                },
            },
        };
    }

    function setTagifyPropertySms() {
        return {
            delimiters: ',| ',
            trim: false,
            tagTextProp: 'name',
            skipInvalid: true,
            dropdown: {
                closeOnSelect: false,
                enabled: 1,
                position: 'text',
                classname: 'student-list',
                searchKeys: ['name'],
            },
            whitelist: defaultWhitelist,
            //whitelist: selectedStudent,
            placeholder: 'Search Student Here...',
            enforceWhitelist: true,
            templates: {
                tag: tagTemplate,
                dropdownItem: suggestionItemTemplateSms,
                dropdownItemNoMatch: function (data) {
                    return `<div class='${this.settings.classNames.dropdownItem}' value="noMatch" tabindex="0" role="option">
                                No student found for: <strong>${data.value}</strong>
                            </div>`;
                },
            },
        };
    }

    function setTagifyData(tagifyObj, selectedStudent, flag = false) {
        if (flag) {
            tagifyObj.removeAllTags();
        }
        let showSelectedStudent = [];
        if (selectedStudent.length > 5) {
            showSelectedStudent = selectedStudent.slice(0, 5);
            showSelectedStudent.push({
                id: '0',
                value: 'ss',
                name: '+' + (selectedStudent.length - 5) + ' Others',
                profile_pic:
                    "<div class='flex items-center stud_ss space-x-2 studentNameDiv'><img class='h-8 w-8 object-cover rounded-full flex object-top' src='' alt='' /><div class='flex items-center stud_72 space-x-2 studentNameDiv'><div class='user-profile-pic h-8 w-8 max-w-8 flex items-center justify-center flex-shrink-0 rounded-full bg-blue-500'><span class='text-xs leading-6 font-medium'>MA</span></div></div></div>",
                contact: '',
            });
        } else {
            showSelectedStudent = selectedStudent;
        }
        tagifyObj.addTags(showSelectedStudent);
        //tagifyObj.whitelist = defaultWhitelist;
    }

    function setTagifyDataV2(tagifyObj, selectedStudent, flag = false) {
        if (flag) {
            tagifyObj.removeAllTags();
        }
        tagifyObj.addTags(selectedStudent);
        //tagifyObj.whitelist = defaultWhitelist;
    }

    var emailTagifyFlag = true;
    function generateSpecialStudentListDropdownForEmail(selectedStudent, inputId) {
        if (emailTagifyFlag) {
            if (tagify1) {
                tagify1.destroy();
            }

            let input1 = document.querySelector(inputId);
            tagify1 = new Tagify(input1, setTagifyPropertyEmail());

            selectedStudent = selectedStudent.filter((student) => student.id != '0');

            /*tagify1.on("dropdown:select", function (e) {
                // Perform your actions here when an item is selected from the dropdown.
                //  selectedStudent.push(e.detail.data);
            });*/
            setTimeout(function () {
                setTagifyData(tagify1, selectedStudent, true);
                // emailTagifyFlag = false;
            }, 1000);
        } else {
            setTagifyData(tagify1, selectedStudent, true);
        }
    }

    var smsTagifyFlag = true;
    function generateSpecialStudentListDropdownForSms(selectedStudent, inputId) {
        if (smsTagifyFlag) {
            var input2 = document.querySelector(inputId);
            tagify2 = new Tagify(input2, setTagifyPropertySms());
            setTimeout(function () {
                setTagifyData(tagify2, selectedStudent);
                smsTagifyFlag = false;
            }, 1000);
        } else {
            setTagifyData(tagify2, selectedStudent, true);
        }
    }

    var letterTagifyFlag = true;
    function generateSpecialStudentListDropdownForLetter(selectedStudent, inputId) {
        if (letterTagifyFlag) {
            if (tagify3) {
                tagify3.destroy();
            }
            var input3 = document.querySelector(inputId);
            tagify3 = new Tagify(input3, setTagifyPropertyEmail());

            selectedStudent = selectedStudent.filter((student) => student.id != '0');

            setTimeout(function () {
                setTagifyData(tagify3, selectedStudent, true);
                //letterTagifyFlag = false;
            }, 1000);
        } else {
            setTagifyData(tagify3, selectedStudent, true);
        }
    }

    CKEDITOR.ClassicEditor.create(document.querySelector('#letters'), {
        ckfinder: {
            uploadUrl: site_url + 'api/upload-file-email-text-editor',
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: window.tagJson,
                    minimumCharacters: 0,
                },
            ],
        },
        removePlugins: [
            'RealTimeCollaborativeComments',
            'RealTimeCollaborativeTrackChanges',
            'RealTimeCollaborativeRevisionHistory',
            'PresenceList',
            'Comments',
            'TrackChanges',
            'TrackChangesData',
            'RevisionHistory',
            'Pagination',
            'WProofreader',
            'MathType',
        ],
    })
        .then((editor) => {
            editor.model.document.on('change:data', () => {
                const content = editor.getData();
                if (content.length > 0) {
                    $(document).find('.preview-placeholder').hide();
                    generateContentToPdf(content);
                    $(document).find('#previewLetter').show().html(content);
                } else {
                    $(document).find('.preview-placeholder').show();
                    $(document).find('#previewLetter').hide();
                }
            });
            editor.ui.view.editable.element.style.height = '300px';
            myEditor22 = editor;
        })
        .catch((error) => {
            console.error(error);
        });

    // new Filter Start

    $(studentsFilterPanelBarId).kendoPanelBar();

    var panelBar = $(studentsFilterPanelBarId).data('kendoPanelBar');
    // expand the element with ID, "item1"
    // panelBar.expand($("li:first-child"));

    panelBar.element.find('li').each(function () {
        panelBar.expand($(this));
    });

    let filterType = {
        1: '#course_id',
        2: '#student_type',
        3: '#nationality',
        4: '#status',
        5: '#batch',
        6: '#teacher',
        /* 7,8,9 is already occupied */
        10: '#campus_id',
    };

    $.ajax({
        type: 'POST',
        url: site_url + 'api/student-filter-menu-html',
        data: { ids: [...Object.keys(filterType), 8, 9, 7] },
        success: function (result) {
            if (result.data) {
                FILTER_DATA = result.data;
                Object.keys(filterType).forEach((filterKey) => {
                    if (result.data[filterKey] && filterType[filterKey]) {
                        if (filterType[filterKey] == '#teacher') {
                            $('#teacher').kendoCheckBoxGroup({
                                items: result.data[filterKey],
                                value: result.data[filterKey].value,
                            });
                        }
                        $(filterType[filterKey]).kendoCheckBoxGroup({
                            items: result.data[filterKey],
                            value: result.data[filterKey].value,
                        });

                        if (filterType[filterKey] == '#course_id') {
                            hideShowMoreFilterData(studentsFilterPanelBarId, 1);
                        }
                    }
                });

                /* TODO: Initialized id 7 and 8 dropdown list after we receive the response here */
                initInitialDropdowns();
            }
        },
        error: function (err) {},
    });

    function initInitialDropdowns() {
        /*$("#filter_list").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            // dataSource: getDropdownDataSource("student-filter-menu-html", { id: 7 }),
            dataSource: FILTER_DATA[7],
            dataBound: function (e) {
                $(studentsFilterPanelBarId).find(".intake_between").hide();
            },
            change: function (e) {
                $("#student_intake").html("");
                if (this.value() == "between") {
                    $(studentsFilterPanelBarId).find(".intake_between").show();
                    let postArr = {
                        id: 9,
                        filter_list: $("#filter_list")
                            .data("kendoDropDownList")
                            .value(),
                        intake_year: $("#intake_year")
                            .data("kendoDropDownList")
                            .value(),
                        intake_year_2: $("#intake_year_2")
                            .data("kendoDropDownList")
                            .value(),
                    };
                    // TODO: this is duplicate request to with same response, so have added new method to set kendo dropdown with array source ?
                    let intakeSource = getDropdownDataSource(
                        "student-filter-menu-html",
                        postArr,
                    );
                    setDropdownListFromSource("intake_date", intakeSource);
                    setDropdownListFromSource("intake_date_2", intakeSource);
                } else {
                    let postArr = {
                        id: 9,
                        filter_list: $("#filter_list")
                            .data("kendoDropDownList")
                            .value(),
                        intake_year: $("#intake_year")
                            .data("kendoDropDownList")
                            .value(),
                        intake_year_2: $("#intake_year_2")
                            .data("kendoDropDownList")
                            .value(),
                    };
                    setDropdownList(
                        "intake_date",
                        "student-filter-menu-html",
                        postArr,
                    );
                    $(studentsFilterPanelBarId).find(".intake_between").hide();
                }
            },
        });*/

        $('#intake_year').kendoDropDownList({
            dataTextField: 'text',
            dataValueField: 'value',
            // dataSource: getDropdownDataSource("student-filter-menu-html", { id: 8 }),
            dataSource: FILTER_DATA[8],
            dataBound: function (e) {
                setTimeout(() => {
                    this.select(0);
                    this.trigger('change');
                    this.trigger('select');
                    let postArr = {
                        id: 9,
                        // filter_list: $("#filter_list")
                        //     .data("kendoDropDownList")
                        //     .value(),
                        intake_year: this.value(),
                    };
                    setDropdownList('intake_date', 'student-filter-menu-html', postArr);
                    $('#intake_year').data('kendoDropDownList').value('2024');
                }, 1000);
            },
            select: function (e) {
                if (e.dataItem) {
                    let postArr = {
                        id: 9,
                        // filter_list: $("#filter_list").data("kendoDropDownList").value(),
                        intake_year: e.dataItem.value,
                        // intake_year_2: $("#intake_year_2").data("kendoDropDownList").value(),
                    };
                    setDropdownList('intake_date', 'student-filter-menu-html', postArr);
                }
            },
        });

        /*$("#intake_year_2").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            // dataSource: getDropdownDataSource("student-filter-menu-html", { id: 8 }),
            dataSource: FILTER_DATA[8],
            dataBound: function (e) {
                setTimeout(() => {
                    this.select(0);
                    this.trigger("change");
                    this.trigger("select");
                    let postArr = {
                        id: 9,
                        filter_list: $("#filter_list")
                            .data("kendoDropDownList")
                            .value(),
                        intake_year: this.value(),
                    };
                    setDropdownList(
                        "intake_date",
                        "student-filter-menu-html",
                        postArr,
                    );
                }, 1000);
            },
            select: function (e) {
                if (e.dataItem) {
                    let postArr = {
                        id: 9,
                        filter_list: $("#filter_list")
                            .data("kendoDropDownList")
                            .value(),
                        intake_year: e.dataItem.value,
                        intake_year_2: $("#intake_year_2")
                            .data("kendoDropDownList")
                            .value(),
                    };
                    setDropdownList(
                        "intake_date_2",
                        "student-filter-menu-html",
                        postArr,
                    );
                }
            },
        });*/
    }

    $('body').on('keyup', '.filterSearchInput', function (e) {
        let searchText = $(this).val();
        let fieldId = $(this).attr('data-field');
        let action = $('#' + fieldId).find('li');
        action.each(function () {
            if (searchText.length > 0 && $(this).find('label')[0]) {
                if (
                    $(this)
                        .find('label')[0]
                        .innerText.toUpperCase()
                        .includes(searchText.toUpperCase())
                )
                    $(this).fadeIn();
                else $(this).fadeOut();
            } else {
                $(this).fadeIn();
            }
        });
    });

    setTimeout(() => {
        // hideShowMoreFilterData('studentsFilterPanelbar', 1);
        hideShowMoreFilterData(studentsFilterPanelBarId, 4);
        hideShowMoreFilterData(studentsFilterPanelBarId, 5);
        hideShowMoreFilterData(studentsFilterPanelBarId, 6);
        hideShowMoreFilterData(studentsFilterPanelBarId, 7);
    }, 6000);

    function setDropdownList(fieldID, api_url, postArr = {}) {
        $('#' + fieldID).kendoDropDownList({
            autoWidth: true,
            dataTextField: 'text',
            dataValueField: 'value',
            filter: 'contains',
            dataSource: getDropdownDataSource(api_url, postArr),
            dataBound: function (e) {
                this.select(0);
                this.trigger('change');
                this.trigger('select');
            },
        });
    }

    function setDropdownListFromSource(fieldID, data) {
        $('#' + fieldID).kendoDropDownList({
            autoWidth: true,
            dataTextField: 'text',
            dataValueField: 'value',
            filter: 'contains',
            dataSource: data,
            dataBound: function (e) {
                this.select(0);
                this.trigger('change');
                this.trigger('select');
            },
        });
    }

    $('#intake_date').kendoDropDownList();
    // $("#intake_date_2").kendoDropDownList();
    let inputIds = [];

    $('body').on('click', '#intake_add_btn', function (e) {
        // $(document).find('#student_intake').addClass('h-40');
        let dataArr = {
            filter_list: $('#filter_list').val(),
            intake_year: $('#intake_year').val(),
            intake_year_2: $('#intake_year_2').val(),
            intake_date: $('#intake_date').val(),
            intake_date_2: $('#intake_date_2').val(),
        };

        $.ajax({
            type: 'POST',
            url: site_url + 'api/student-intake-add',
            dataType: 'json',
            data: dataArr,
            success: function (result) {
                if (result.data.length > 0) {
                    $.each(result.data, function (i, arr) {
                        $('#student_intake').append(addCourseInFilterHtml(arr));
                    });
                    let inputElements = document.querySelectorAll('#student_intake .k-checkbox');
                    inputElements.forEach((input) => {
                        if (!isIdInArray(input.id)) {
                            // If not, add it to the array
                            inputIds.push(input.id);
                        }
                    });
                } else {
                    notificationDisplay(result.message, '', result.status);
                }
            },
        });
    });

    function isIdInArray(id) {
        return inputIds.includes(id);
    }

    $('body').on('change', '.student-filter', function () {
        updateFilterCount();
    });

    $('body').on('click', '.clear_applied_filter', function (e) {
        e.preventDefault();

        let filterId = $(this).attr('data-filter-id');
        let dropDownId = $(this).attr('data-key');
        if (filterId == 'all') {
            var uri = window.location.toString();
            if (uri.indexOf('?') > 0) {
                var clean_uri = uri.substring(0, uri.indexOf('?'));
                window.history.replaceState({}, document.title, clean_uri);
            }
            $(document).find('#appliedFilterList').html('').hide();
            clearStudentFilter();
            applyStudentFilter();
        } else {
            $(document)
                .find('input[value="' + filterId + '"]')
                .prop('checked', false);
            updateFilterCount();
        }
    });

    function addCourseInFilterHtml(arr) {
        // let intakeId = $("#filter_list").val() + "@" + arr.intake_start;
        let intakeId = arr.intake_start;
        if (!inputIds.includes('2_checkbox_' + arr.id)) {
            return (
                '<li class="k-checkbox-item" data-uid="">\n' +
                '                    <input class="k-checkbox" type="checkbox" value="' +
                intakeId +
                '" name="student_intake" data-category="intake" id="2_checkbox_' +
                arr.id +
                '" data-val="' +
                arr.text +
                '">\n' +
                '<div class="flex justify-between w-full">\n' +
                '                    <label class="k-checkbox-label" for="2_checkbox_' +
                arr.id +
                '" data-val="' +
                intakeId +
                '" title="' +
                arr.text +
                '">' +
                arr.text +
                '</label>\n' +
                '    <span class="cursor-pointer k-icon k-i-close remove_student_intake pl-6" data-intake-id="' +
                arr.id +
                '"></span>\n' +
                '    </span>' +
                '</div>\n' +
                '</li>'
            );
        }
    }

    function updateFilterCount() {
        let course_id = $('input[name="course_id"]').is(':checked') ? 1 : 0;
        let campus_id = $('input[name="campus_id"]').is(':checked') ? 1 : 0;
        let student_type = $('input[name="student_type"]').is(':checked') ? 1 : 0;
        let nationality = $('input[name="nationality"]').is(':checked') ? 1 : 0;
        let status = $('input[name="status"]').is(':checked') ? 1 : 0;
        let batch = $('input[name="batch"]').is(':checked') ? 1 : 0;
        let teacher = $('input[name="teacher"]').is(':checked') ? 1 : 0;
        let student_intake = $('input[name="student_intake"]').is(':checked') ? 1 : 0;
        let filterCount =
            course_id +
            campus_id +
            student_type +
            nationality +
            status +
            batch +
            teacher +
            student_intake;

        if (filterCount == 0) {
            $(document).find('.filterCountDiv').addClass('hidden');
            $(document).find('#filterBtn').removeClass('border-primary-blue-500');
            $(document).find('#filterBtn').addClass('border-gray-300');
            $(document).find('#filterBtn').removeClass('bg-primary-blue-50');
        } else {
            $(document).find('.filterCountDiv').removeClass('hidden');
            $(document).find('#filterBtn').addClass('border-primary-blue-500 bg-primary-blue-50');
            $(document).find('#filterBtn').removeClass('border-gray-300 bg-white');
            $(document).find('#filterBtn').addClass('bg-primary-blue-50');
        }
        $(document).find('#filterBtn .filterCount').text(filterCount);

        applyStudentFilter();
    }

    let searchDebounceTimeout;

    $(document).on('input', '.textInputField', function () {
        clearTimeout(searchDebounceTimeout);

        const input = $(this);
        const searchText = input.val().trim();

        if (searchText.length > 0 && searchText.length < 3) {
            $('#search-message').text('Type at least 3 characters.').show();
            return;
        }

        $('#search-message').hide();

        searchDebounceTimeout = setTimeout(() => {
            $('.search-loading').show();
            applyStudentFilter(searchText);
        }, 500);
    });

    $(document).on('blur', '.textInputField', function () {
        $('#search-message').hide();
    });
    function applyStudentFilter(searchText = '') {
        isSelectAll = false;
        $('.header-checkbox .k-checkbox').prop('disabled', true);
        let gridData = $('#studentList').data('kendoGrid');
        let data = gridData.dataSource.view();
        data.forEach((item) => {
            let row = gridData.table.find("tr[data-uid='" + item.uid + "']");
            gridData.clearSelection(row);
        });
        let appliedFilterArr = [];

        let extFilterArr = {
            searchText: '',
            course_id: [],
            campus_id: [],
            student_type: [],
            nationality: [],
            status: [],
            batch: [],
            teacher: [],
            student_intake: [],
        };

        $('#course_id .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['course_id'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                //value: $(this).next("label").text(),
                value: $(this)
                    .next('label')
                    .contents()
                    .filter(function () {
                        return this.nodeType === 3; // Only text nodes
                    })
                    .text()
                    .trim(),
                key: '',
            });
        });
        $('#campus_id .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['campus_id'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#student_type .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['student_type'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#nationality .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['nationality'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#status .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['status'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#batch .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['batch'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#teacher .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['teacher'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#student_intake .k-checkbox-item .k-checkbox:checked').each(function () {
            extFilterArr['student_intake'].push(this.value);
            appliedFilterArr.push({
                id: this.value,
                value: $(this).attr('data-val'),
                key: '',
            });
        });

        $(document).find('#appliedFilterList').show().html(setAppliedFilterData(appliedFilterArr));

        // Set Filter To Grid Cookie.
        $.cookie('studentScoutGridFilter', JSON.stringify(extFilterArr));
        objectToCommaSeparatedQueryString(extFilterArr);
        extFilterArr['searchText'] = searchText;
        var dataSource = new kendo.data.DataSource({
            type: 'json',
            transport: {
                read: {
                    url: site_url + 'api/student-data-scout',
                    dataType: 'json',
                    type: 'POST',
                    data: extFilterArr,
                },
            },
            schema: {
                data: 'data.data',
                total: 'data.total',
                model: {
                    id: 'id',
                    fields: {
                        profile_picture: { type: 'string' },
                        student_name: { type: 'string' },
                        student_type: { type: 'string' },
                        course_list: { type: 'string' },
                        student_id: { type: 'string' },
                        DOB: { type: 'date' },
                        campus: { type: 'string' },
                    },
                },
            },
            pageSize: 25,
            serverPaging: true,
            serverFiltering: true,
            serverSorting: true,
        });
        var gridHeight = getGridTableHeight(gridID, 0);
        var grid = $(gridID).data('kendoGrid');
        grid.wrapper.height(gridHeight);
        grid.setDataSource(dataSource);
        // $(document).find("#s/elected_title").text($('.totalStudent').first().text()+" students selected");

        $.ajax({
            url: site_url + 'api/student-list-data',
            method: 'POST',
            dataType: 'json',
            data: extFilterArr,
            success: function (response) {
                defaultWhitelist = response.data;
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error('Error fetching data:', textStatus, errorThrown);
            },
        });
    }

    function applyStudentFilterV2() {
        let appliedFilterArr = [];

        $('#course_id .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });
        $('#campus_id .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#student_type .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#nationality .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#status .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#batch .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#teacher .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).next('label').text(),
                key: '',
            });
        });

        $('#student_intake .k-checkbox-item .k-checkbox:checked').each(function () {
            appliedFilterArr.push({
                id: this.value,
                value: $(this).attr('data-val'),
                key: '',
            });
        });

        $(document).find('#appliedFilterList').show().html(setAppliedFilterData(appliedFilterArr));
    }

    function clearStudentFilter() {
        /*let grid = $(gridID).data('kendoGrid');
        var options = grid.getOptions();
        options.dataSource.serverFiltering = false;;
        grid.setOptions(options);
        customGridHtml(gridID);*/
        // $(gridID).data("kendoGrid").dataSource.filter([]);
        $(document).find('#appliedFilterList').hide().html('');
        $(document).find('#course_id .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#campus_id .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#student_type .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#nationality .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#status .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#batch .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#teacher .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#student_intake .k-checkbox-item .k-checkbox').prop('checked', false);
        $(document).find('#filterBtn').removeClass('border-primary-blue-500');
        $(document).find('#filterBtn').addClass('border-gray-300');
        $(document).find('#filterBtn').removeClass('bg-primary-blue-50');
        $(document).find('.filterCountDiv').addClass('hidden');
        $(document).find('#filterBtn .filterCount').text('');
    }

    function setAppliedFilterData(appliedFilterArr) {
        let filterHtml = '';
        if (appliedFilterArr.length > 0) {
            appliedFilterArr.filter(function (arr) {
                filterHtml +=
                    '<div class="inline-flex items-center justify-center space-x-2 px-2 py-1 bg-gray-100 rounded-full mr-2 mt-2"><span class="text-sm leading-5 text-center text-gray-800">' +
                    arr['value'] +
                    '</span><span class="cursor-pointer k-icon k-i-close clear_applied_filter text-blue-500" data-filter-id="' +
                    arr['id'] +
                    '" data-key= "' +
                    arr['key'] +
                    '"></span></div>';
            });
            filterHtml +=
                '<div class="inline-flex items-center justify-center space-x-2 mt-2"><button class="text-sm leading-5 font-medium text-primary-blue-500 clear_applied_filter" data-filter-id="all">Clear Filters</button></div>';
        }
        return filterHtml;
    }

    $(document).keyup(function (e) {
        if (e.key === 'Escape') {
            $('#studentMiniProfileModal_wnd_title')
                .parent('div')
                .find('.k-window-actions a')
                .trigger('click');
        }
    });

    // var typingTimer;
    // var doneTypingInterval = 1000; // 1000 milliseconds = 1 second
    //
    // $('.textInputField').on('input', function () {
    //     clearTimeout(typingTimer);
    //     typingTimer = setTimeout(doneTyping, doneTypingInterval);
    // });
    //
    // function doneTyping() {
    //     // Your code to be executed after the user stops typing
    //     // applyStudentFilter();
    // }

    /* $('body').on('keyup', '.searchInputField', function() {
        applyStudentFilter();
    });
    $('body').on('keyup', '.searchInputField', function() {
        let grid = $(gridID).data('kendoGrid');
        var options = grid.getOptions();
        if($(this).val() == ''){
            // if(!options.dataSource.serverFiltering){
                options.dataSource.serverFiltering = false;
                grid.setOptions(options);
                customGridHtml(gridID);
            // }
        } else {
            if(!options.dataSource.serverFiltering){
                options.dataSource.serverFiltering = true;
                grid.setOptions(options);
                customGridHtml(gridID);
            }
        }
    });

    $("body").on("focus", ".activeExpandBtn", function () {
      $(this).removeClass("w-40");
      $(this).attr("style", "width: 240px !important");
    });

    $("body").on("blur", ".activeExpandBtn", function () {
      $(this).removeAttr("style", "width: 240px !important");
      $(this).addClass("w-40");
    });*/

    var outerSplitter = $('#studentListSplitter')
        .kendoSplitter({
            draggable: false,
            animation: {
                open: {
                    effects: 'expand',
                    duration: 450,
                },
                close: {
                    effects: 'expand',
                    duration: 450,
                },
            },
            panes: [
                { collapsible: true, size: 320, min: 320 }, // Filter pane
                { collapsible: true }, // Table pane
            ],
            orientation: 'horizontal',
        })
        .data('kendoSplitter');

    resizeSplitter();

    outerSplitter.collapse('#toggleFilter');

    $(window).resize(resizeSplitter);

    function resizeSplitter() {
        outerSplitter.wrapper.height($(window).height() - 66); // Subtract height of Gradientbar and Adminbar
        outerSplitter.resize();
        // outerSplitter.element
        //     .find(".filter-result.k-pane.k-scrollable")
        //     .css("width", "100%");
    }

    $('#menuBtn').click(function () {
        var splitter = $('#studentListSplitter').data('kendoSplitter');
        setTimeout(function () {
            splitter.resize();
        }, 450);
    });

    $('body').on('click', '#filterBtn', function () {
        var splitter = $('#studentListSplitter').data('kendoSplitter');
        var filterPane = splitter.options.panes[0];
        var contentPane = splitter.options.panes[1];
        splitter.element.find('.filter-result.k-pane.k-scrollable').removeClass('!w-full');
        var sidebarStatus = $.cookie('sidebar') ? $.cookie('sidebar') : 'unslide';

        if (filterPane.collapsed) {
            if (sidebarStatus == 'unslide') {
                $('#menuBtn').click();
                setTimeout(function () {
                    splitter.resize();
                }, 300);
            }
            splitter.expand('#toggleFilter');
            splitter.size('#toggleFilter', 320);
            const filterPaneWidth = $('#toggleFilter').width();
            $('.toggelfilter').width(filterPaneWidth);
        } else {
            splitter.collapse('#toggleFilter');
            if (sidebarStatus == 'slide') {
                $('#menuBtn').click();
                setTimeout(function () {
                    splitter.resize();
                }, 300);
            }
        }
    });

    let isDragging = false;
    outerSplitter.wrapper.on('mousedown', '.k-splitbar', function (e) {
        isDragging = true;
    });

    $(document).on('mousemove', function (e) {
        if (isDragging) {
            setTimeout(function () {
                const filterPaneWidth = $('#toggleFilter').width();
                $('.toggelfilter').width(filterPaneWidth);
            }, 200);
        }
    });

    $(document).on('mouseup', function () {
        isDragging = false;
        setTimeout(function () {
            const filterPaneWidth = $('#toggleFilter').width();
            $('.toggelfilter').width(filterPaneWidth);
        }, 200);
    });

    $(gridID).on('click', 'tbody tr', function (e) {
        if (!$(e.target).is(':first-child, :last-child')) {
            var dataItem = $(gridID).data('kendoGrid').dataItem(this);
            if (dataItem.id) {
                let redirectUrl = site_url + 'student-profile-view/' + dataItem.id;
                window.location.href = redirectUrl;
            }
        }
    });

    function showRowHoverActions() {
        $('#studentList tbody tr').each(function () {
            $(this).addClass('cursor-pointer');
            // const rowData = $(gridID).data("kendoGrid").dataItem(this);
            $(this).attr('title', 'Click to view full profile');
            $(this).attr('data-tooltip-position', 'cursor');
        });
        initializeTooltips();
    }

    function onInvalidTag(e) {
        notificationDisplay('Enter email only ', '', 'error');
    }

    function tagTemplateV2(tagData) {
        return `<tag title="${tagData.value}" contenteditable='false' spellcheck='false' tabIndex="-1" class="tagify__tag bg-gray-300">
                    <x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>&nbsp;
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${tagData.value}</div>
                    </div>
                </tag>`;
    }

    $('#studentInviteTemplatesModal').kendoWindow(openCenterWindow('Invite Templates', 65, 15, 20));
    $('#inviteStudentToGalaxyModal').kendoWindow(
        defaultWindowSlideFormat('Invite Student to Galaxy', 65)
    );

    addModalClassToWindows(['#studentInviteTemplatesModal']);

    $('body').on('click', '#insertStudentInviteTemplate', function () {
        kendowindowOpen('#studentInviteTemplatesModal');
        $('#searchEmailTemplateStudentInvite').val('').trigger('keyup');
    });

    $('body').on('click', '#useStudentInviteTemplateBtn', function () {
        $(document).find('.isTemplateSelect').text('');
        $(document).find('.email_subject_txt').val(emailSubject);
        myEditor33.setData(emailContent);
        $('#studentInviteTemplatesModal').data('kendoWindow').close();
    });

    $(document).on('click', '.inviteToGalaxyBtn', function () {
        let isBulk = $(this).attr('data-isBulk');
        let studentID = $(this).attr('data-student-id');
        let first_name = $(this).attr('data-first_name');
        let family_name = $(this).attr('data-family_name');
        let fullName = $(this).attr('data-name');
        let email = $(this).attr('data-email');
        let dataArr = {
            template_name: 'studentInviteEmail',
            student_id: studentID,
        };
        ajaxcallwithMethod('api/get-email-title-template-data', dataArr, 'POST', function (output) {
            myEditor33.setData(output.data[0].content);
            let template_id = output.data[0].id;
            if (isBulk == 1) {
                $('#forOneInvite').hide();
                $('#forBulkInvite').show();

                setTimeout(function () {
                    let selectedStudentTags = getSelectedStudentTagsForBulkInvite();

                    generateSpecialStudentListDropdownForEmail(
                        selectedStudentTags,
                        '#invite_student_name_email_list'
                    );
                    // $("#inviteStudentToGalaxyInviteForm").find("#subject").val("Welcome to " + selectedStudentTags[0].college_name),
                    $('#inviteStudentToGalaxyInviteForm')
                        .find('#subject')
                        .val(output.data[0].email_subject),
                        $('#inviteStudentToGalaxyInviteForm').find('#template_id').val(template_id),
                        kendowindowOpen('#inviteStudentToGalaxyModal');
                }, 500);
            } else {
                $('#forOneInvite').hide();
                $('#forBulkInvite').show();
                let viewProfile = $(document).find('#inviteStudentToGalaxyModal');
                setTimeout(function () {
                    let selectedStudentTags = [];
                    selectedStudentTags.push({
                        id: studentID,
                        value: studentID,
                        name: fullName,
                    });
                    generateSpecialStudentListDropdownForEmail(
                        selectedStudentTags,
                        '#invite_student_name_email_list'
                    );
                    $('#inviteStudentToGalaxyInviteForm')
                        .find('#subject')
                        .val(output.data[0].email_subject),
                        $('#inviteStudentToGalaxyInviteForm')
                            .find('#invite_student_id')
                            .val(studentID),
                        $('#inviteStudentToGalaxyInviteForm').find('#template_id').val(template_id),
                        kendowindowOpen('#inviteStudentToGalaxyModal');
                }, 500);
            }
        });
    });

    function getSelectedStudentTagsForBulkInvite() {
        let selectedStudentTags = [];
        $.each(selectedStudent, function (index, row) {
            if (row.username == null) {
                selectedStudentTags.push({
                    id: row.id,
                    value: row.id,
                    name: row.name,
                    profile_pic: row.mini_profile_pic,
                    contact: row.contact,
                    username: row.username,
                    college_name: row.college_name,
                });
            }
        });
        return selectedStudentTags;
    }

    $('body').on('click', '.cancelBtn', function (e) {
        e.preventDefault();
        $(this).closest('.k-window-content').data('kendoWindow').close();
    });

    $('body').on('click', '#sendInviteMail', function () {
        let formTag = $('#forBulkInvite');
        let uqTags = getSelectedTagsForStudent(formTag);

        let modalDiv = $(this).parent('.k-window');
        let subject = $('#inviteStudentToGalaxyInviteForm').find('#subject').val();
        let studentId = $('#inviteStudentToGalaxyInviteForm').find('#invite_student_id').val();

        let template_id = $('#inviteStudentToGalaxyInviteForm').find('#template_id').val();
        if (myEditor33.getData().length == 0) {
            notificationDisplay('Invite content is required', '', 'error');
            return false;
        }

        if (uqTags.length == 0) {
            var dataArr = {
                subject: subject,
                student_id: studentId,
                template_id: template_id,
                email_content: myEditor33.getData(),
            };
        } else {
            var dataArr = {
                subject: subject,
                template_id: template_id,
                student_id: uqTags,
                email_content: myEditor33.getData(),
            };
        }
        modalDiv.addClass('blur-modal');
        let type = 'Email';
        var rtrn = $.ajax({
            type: 'POST',
            url: 'api/student-invite-mail',
            data: dataArr,
            headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
            beforeSend: function () {
                kendowindowOpen(`#loaderFor${type}`);
                kendo.ui.progress($(`#loader${type}`), true);
            },
            success: function (result) {
                modalDiv.removeClass('blur-modal');
                refreshGridData();
                myEditor33.setData('');
                $('#inviteStudentToGalaxyModal').data('kendoWindow').close();
                kendo.ui.progress($(`#loaderFor${type}`), false);
                $('#loaderFor' + type)
                    .data('kendoWindow')
                    .close();
                kendowindowOpen(`#statusForSend${type}Modal`);
                $(`#title${type}SuccessMsg`).text(result.success_msg);
                $(`#title${type}FailMsg`).text(result.fail_msg);
                if (result.fail_msg) {
                    $(`.title${type}FailMsg`).show();
                } else {
                    $(`.title${type}FailMsg`).hide();
                }
            },
            error: function (result) {
                // kendo.ui.progress($(document.body), false);
                callback(result);
            },
        });
        return rtrn;
    });

    $('#closeModelConformation').kendoWindow(openCenterWindow('Discard Email'));

    function removeTagifyForcecfully() {
        if (tagify1) {
            tagify1.removeAllTags();
        }
        if (tagify3) {
            tagify3.removeAllTags();
        }

        /*var tagify41 = new Tagify(
            document.querySelector(studNameListForEmailId),
        );
        tagify41.removeAllTags();

        var tagify42 = new Tagify(
            document.querySelector(studNameListForLetterId),
        );
        tagify42.removeAllTags();*/
    }

    $('body').on('click', '.discard-yes', function (e) {
        confirmEmail = true;
        $('#closeModelConformation').data('kendoWindow').close();
        $('#sendMailStudentModal').data('kendoWindow').close();
        $('#selectedStudents').trigger('click');
        $('.resetEmail').trigger('click');
        $('#email_cc').html('');
        $('#email_bcc').html('');
        $(document).find('#emailccbox').hide();
        $(document).find('#emailbccbox').hide();

        /*var tagifyRmTag = new Tagify(
            document.querySelector("#student_name_remove_email_list"),
        );
        tagifyRmTag.removeAllTags();

        let inputV2 = document.querySelector("#other_student_name_list");
        tagifyV2 = new Tagify(inputV2, setTagifyPropertyWithoutCourseStudent());
        tagifyV2.removeAllTags();*/

        removeTagifyForcecfully();
        /*var tagify35 = new Tagify(
            document.querySelector(studNameListForEmailId),
        );
        tagify35.removeAllTags();*/

        studentGridResetAfterDiscard();
    });

    $('body').on('click', '.removeBtn', function () {
        let studId = $(this).attr('id');
        let dataRow = $(document)
            .find('.scout_' + studId)
            .parents('tr');
        dataRow.each(function () {
            if ($(this).hasClass('k-state-selected')) {
                $(this).find('.k-checkbox').trigger('click');
            }
        });
        /*var tagify35 = new Tagify(
            document.querySelector("#student_name_remove_email_list"),
        );
        tagify35.removeTags(studId);*/

        selectedStudent = selectedStudent.filter((student) => student.id != studId);
        selectedStudent = selectedStudent.filter((student) => student.id != '0');

        $('#OtherEmailStudentListModal_wnd_title').text(
            'All ' + selectedStudent.length + ' Email Recipients'
        );
        // setTagifyData(tagify35, selectedStudent, true);

        tagify1 = new Tagify(document.querySelector(studNameListForEmailId));
        tagify1.removeTags(studId);
        setTagifyData(tagify1, selectedStudent, true);

        tagify3 = new Tagify(document.querySelector(studNameListForLetterId));
        tagify3.removeTags(studId);
        setTagifyData(tagify3, selectedStudent, true);
    });

    $('#statusForSendEmailModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            confirmEmail = true;
            $('.resetEmail').trigger('click');
            $('#sendMailStudentModal').data('kendoWindow').close();
            $('#selectedStudents').trigger('click');
        });

    $('body').on('click', '.discard-no', function (e) {
        $('#closeModelConformation').data('kendoWindow').close();
    });

    $('body').on('click', '.closeModelLetterParameterModal', function (e) {
        $('#letterParameterModal').data('kendoWindow').close();
    });

    $('#sendMailStudentModal')
        .data('kendoWindow')
        .bind('open', function (e) {
            attachedFiles = [];
            confirmEmail = false;
        });

    $('#sendMailStudentModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            if (!confirmEmail) {
                kendowindowOpen('#closeModelConformation');
                confirmEmail = false;
                e.preventDefault();
            }
        });

    /*$(document).on("change", "#course_list_for_email", function (e) {
        var selectedCourseId = $("#course_list_for_email")
            .data("kendoDropDownList")
            .value();

        let selectedStudentTags = getSelectedStudentTags();
        generateSpecialStudentListDropdownForEmail( selectedStudentTags, studNameListForEmailId );

        $.ajax({
            type: "POST",
            url: site_url + "api/find-student-id-with-course",
            dataType: "json",
            data: { course_id: selectedCourseId, studIds: selectedStudentTags },
            success: function (response) {
                let resData = response.data.withoutCourseStudentIds;
                let resDataLen = response.data.withoutCourseStudentIds.length;
                let inputV2 = document.querySelector("#student_name_remove_email_list");
                tagifyV2 = new Tagify(inputV2, setTagifyPropertyWithoutCourseStudent());
                tagifyV2.removeAllTags();
                setTagifyData(tagifyV2, resData);
                manageActionTagify(tagifyV2);
                $("#notReceivedEmailStudentListModal").find(".tagify__input").hide();
                $("#notReceivedEmailStudentListModal_wnd_title").text(resDataLen + " Students not receiving email");

                $(".notReceivedEmailStudentCount").text(resDataLen);
                if (resDataLen <= 0) {
                    $(".notReceivedEmailStudentListBtn").hide();
                } else {
                    $(".notReceivedEmailStudentListBtn").show();
                }
                //   setWithoutCourseIdStudentModal(response.data.withoutCourseStudentIds);
            },
        });
    });*/

    function manageActionTagify(tagifyV3) {
        tagifyV3.on('remove', function (e) {
            var removedTag = e.detail.data.value;
            tagify1 = new Tagify(document.querySelector(studNameListForEmailId));
            tagify1.removeTags(removedTag);

            selectedStudent = selectedStudent.filter((student) => student.id != removedTag);
            selectedStudent = selectedStudent.filter((student) => student.id != '0');

            $('#OtherEmailStudentListModal_wnd_title').text(
                'All ' + selectedStudent.length + ' Email Recipients'
            );
            setTagifyData(tagify1, selectedStudent, true);

            tagify3 = new Tagify(document.querySelector(studNameListForLetterId));
            tagify3.removeTags(removedTag);
            setTagifyData(tagify3, selectedStudent, true);

            // tagify35.removeAllTags();
            /*$("#notReceivedEmailStudentListModal_wnd_title").text(
                tagifyV2.value.length + " Students not receiving email",
            );*/
            /*$(".notReceivedEmailStudentCount").text(tagifyV2.value.length);
            if (tagifyV2.value.length <= 0) {
                $(".notReceivedEmailStudentListBtn").hide();
            } else {
                $(".notReceivedEmailStudentListBtn").show();
            }*/

            $(document).find('#selected_title').text(`${selectedStudent.length} students selected`);
            // $(document).find('.totalStudent').text(`${selectedStudent.length}`);

            $('#studentSearchFromTags').val('').trigger('keyup');
            let dataRow = $(document)
                .find('.scout_' + removedTag)
                .parents('tr');
            dataRow.each(function () {
                if ($(this).hasClass('k-state-selected')) {
                    $(this).find('.k-checkbox').trigger('click');
                }
            });
        });
    }

    $(document).on('click', '.removeEmailNotSendStudents', function () {
        let inputV2 = document.querySelector('#other_student_name_list');
        var tagifyV2 = new Tagify(inputV2, setTagifyPropertyWithoutCourseStudent());
        var values = tagifyV2.value.map(function (item) {
            return item.value;
        });

        tagifyV2.removeAllTags();

        removeTagifyForcecfully();

        /*$("#notReceivedEmailStudentListModal_wnd_title").text(
            "0 Students not receiving email",
        );
        $(".notReceivedEmailStudentCount").text(0);
        $(".notReceivedEmailStudentListBtn").hide();*/

        $('#OtherEmailStudentListModal').data('kendoWindow').close();
        /*selectedStudent.forEach(function (removedTag) {
            let dataRow = $(document)
                .find(".scout_" + removedTag.id)
                .parents("tr");
            dataRow.each(function () {
                if ($(this).hasClass("k-state-selected")) {
                    $(this).find(".k-checkbox").trigger("click");
                }
            });
        });
        let grid = $("#studentList").data("kendoGrid");
        let data = grid.dataSource.view();
        data.forEach((item) => {
            let row = grid.table.find("tr[data-uid='" + item.uid + "']");
            grid.clearSelection(row);
        });*/
        $('#selectedStudents').trigger('click');
    });

    /*$("#notReceivedEmailStudentListModal").kendoWindow(
        defaultWindowSlideFormat("12 Students not receiving email", 30),
    );

    $("body").on("click", ".notReceivedEmailStudentListBtn", function () {
        kendowindowOpen("#notReceivedEmailStudentListModal");
    });*/

    /*jQuery("#studentSearchFromTags").keyup(function () {
        var searchText = jQuery(this).val();
        jQuery("tags.student_name_remove_email_list tag").each(function () {
            if (jQuery(this).text().search(new RegExp(searchText, "i")) < 0) {
                jQuery(this).hide();
            } else {
                jQuery(this).show();
            }
        });
    });*/

    jQuery('#otherStudentSearchFromTags').keyup(function () {
        var searchText = jQuery(this).val();
        var visibleTags = [];

        // Loop through each tag to check the search text and show/hide them accordingly
        jQuery('tags.other_student_name_list tag').each(function () {
            if (jQuery(this).text().search(new RegExp(searchText, 'i')) < 0) {
                jQuery(this).hide();
                jQuery(this).removeClass('border-bottom');
            } else {
                jQuery(this).show();
                visibleTags.push(jQuery(this)); // Store visible tags in an array
            }
        });

        // Remove border-bottom from all tags
        jQuery('tags.other_student_name_list tag').removeClass('border-bottom');

        // Add border-bottom only to the last visible tag
        if (visibleTags.length > 0) {
            visibleTags[visibleTags.length - 1].addClass('border-bottom');
        }
    });

    $('#OtherEmailStudentListModal').kendoWindow(
        defaultWindowSlideFormat('12 Students not receiving email', 30)
    );

    $('body').on('click', '.showOthersStudentList', function (e) {
        let selectedStudentTags = getSelectedStudentTags();
        var inputV3 = document.querySelector('#other_student_name_list');

        if (typeof tagifyV3 != 'undefined') {
            tagifyV3.destroy();
        }

        var tagifyV3 = new Tagify(inputV3, setTagifyPropertyWithoutCourseStudent());

        setTagifyDataV2(tagifyV3, selectedStudentTags, true);
        $('#OtherEmailStudentListModal_wnd_title').text(
            'All ' + tagifyV3.value.length + ' Email Recipients'
        );
        manageActionTagify(tagifyV3);
        kendowindowOpen('#OtherEmailStudentListModal');
    });

    function setTagifyPropertyWithoutCourseStudent() {
        return {
            delimiters: ',| ',
            trim: false,
            tagTextProp: 'name',
            skipInvalid: true,
            dropdown: {
                closeOnSelect: false,
                enabled: 1,
                position: 'text',
                classname: 'student-list',
                searchKeys: ['name'],
            },
            whitelist: defaultWhitelist,
            //whitelist: selectedStudent,
            placeholder: 'Search Student Here...',
            enforceWhitelist: true,
            templates: {
                tag: tagTemplateStudentList,
                dropdownItem: suggestionItemTemplateEmail,
                dropdownItemNoMatch: function (data) {
                    return `<div class='${this.settings.classNames.dropdownItem}' value="noMatch" tabindex="0" role="option">
                                No student found for: <strong>${data.value}</strong>
                            </div>`;
                },
            },
        };
    }

    function setEmailTemplateSelectionData(dataItems) {
        let insertTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
            data: dataItems,
            schema: {
                model: {
                    id: 'id',
                    hasChildren: 'hasChildren',
                    children: 'sub_list',
                },
            },
        });
        $('#insertTemplatePanelBar').kendoPanelBar({
            template: kendo.template($('#emailPanelBarTemplate').html()),
            dataSource: insertTemplateSidebarMenu,
        });
    }

    function setLetterTemplateSelectionData(dataItems) {
        let insertLetterTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
            data: dataItems,
            schema: {
                model: {
                    id: 'id',
                    hasChildren: 'hasChildren',
                    children: 'sub_list',
                },
            },
        });
        $('#insertLetterTemplatePanelBar').kendoPanelBar({
            template: kendo.template($('#letterPanelBarTemplate').html()),
            dataSource: insertLetterTemplateSidebarMenu,
        });
    }
});

function studentGridResetAfterDiscard() {
    if (isSelectAll) {
        isSelectAll = isSelectAll ? false : true;
        let grid = $('#studentList').data('kendoGrid');
        let data = grid.dataSource.view();
        data.forEach((item) => {
            let row = grid.table.find("tr[data-uid='" + item.uid + "']");
            if (isSelectAll) {
                grid.select(row);
            } else {
                grid.clearSelection(row);
            }
        });
    }
}

function updateURLParameter(key, value) {
    const url = new URL(window.location.href);
    // if (key != "student_intake") {
    if (value) {
        url.searchParams.set(key, value);
    } else {
        url.searchParams.delete(key);
    }
    // url.searchParams.set(key, value);
    const newURL = url.href;
    history.pushState({ path: newURL }, '', decodeURIComponent(newURL));
    $.cookie('UrlWithFilter', newURL);
    // }
}

function objectToCommaSeparatedQueryString(obj) {
    return Object.keys(obj)
        .filter((key) => {
            // Exclude blank values
            let value = Array.isArray(obj[key]) ? obj[key].join(',') : obj[key];
            updateURLParameter(encodeURIComponent(key), value);
            return value !== '';
        })
        .map((key) => {
            let value = Array.isArray(obj[key]) ? obj[key].join(',') : obj[key];
            updateURLParameter(encodeURIComponent(key), value);
            return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
        })
        .join('&');
}

function getQueryParams() {
    var queryString = window.location.search.substring(1);
    let params = {};
    let pairs = queryString.split('&');
    pairs.forEach((pair) => {
        let [key, value] = pair.split('=');
        key = decodeURIComponent(key);
        value = decodeURIComponent(value);

        // Ensure blank values are also in array format
        if (value === '') {
            if (key === 'searchText') {
                value = '';
            } else {
                value = [''];
            }
        } else if (value.includes(',')) {
            value = value.replace(/\+/g, ' ').split(',');
        } else {
            value = [value.replace(/\+/g, ' ')];
        }

        params[key] = value;
    });
    return params;
}

function hideShowMoreFilterData(panelBarId, parentId) {
    let liCount = 3;
    let customUl = $(document)
        .find(panelBarId)
        .find('.filter-panel-title-' + parentId)
        .closest('li')
        .find('ul');
    customUl.addClass('custom-panel-size-ul');

    let cList = customUl.find('li:gt(' + liCount + ')');
    if (!customUl.hasClass('expanded')) {
        cList.hide();
    } else {
        cList.show();
    }

    if (cList.length > 0) {
        let tempLiHtmlMore =
            '<span class="k-link"><a class="text-sm leading-5 text-primary-blue-500" href="javascript:void(0);">See More</a></span>';
        let tempLiHtmlLess =
            '<span class="k-link"><a class="text-sm leading-5 text-primary-blue-500" href="javascript:void(0);">See Less</a></span>';
        customUl.append(
            $('<li class="expand p-1">' + tempLiHtmlMore + '</li>').click(function (event) {
                let expandible = $(this).closest('ul');
                expandible.toggleClass('expanded');
                if (!expandible.hasClass('expanded')) {
                    $(this).html(tempLiHtmlMore);
                } else {
                    $(this).html(tempLiHtmlLess);
                }
                cList.toggle();
                event.preventDefault();
            })
        );
    }
}

addModalClassToWindows([
    '#loaderForEmail',
    '#loaderForLetter',
    '#emailTemplatesModal',
    '#letterTemplatesModal',
    '#letterParameterModal',
    '#statusForSendEmailModal',
    '#closeModelConformation',
    '#studentInviteTemplatesModal',
]);

function startAjaxLoader() {
    $(document)
        .on('ajaxStart', function () {
            kendo.ui.progress($(document.body), true);
        })
        .on('ajaxStop', function () {
            kendo.ui.progress($(document.body), false);
        });
}

function stopAjaxLoader() {
    $(document)
        .on('ajaxStart', function () {
            kendo.ui.progress($(document.body), false);
        })
        .on('ajaxStop', function () {
            kendo.ui.progress($(document.body), false);
        });
}

/* Click for select All */
function manageFooterGridFilterText() {
    let totalStudents = $(document).find('.totalStudent').first().text();
    let filteredStudents = $(document).find('.filterStudCount').text();
    let selectTextForGridFooter = totalStudents == filteredStudents ? 'Deselect All' : 'Select All';
    $(document).find('.selectUnSelect').text(selectTextForGridFooter);
}

/* Change event for checkbox */
function onChangeForGridCheckbox(selectedCount) {
    $(document).find('.filterTextFooter').show();
    if ($(document).find('.selectUnSelect').text() == 'Deselect All') {
        //$(document).find('.totalStudent').text(selectedCount);
        $(document).find('.filterTextFooter').hide();
        $(document).find('.generalText').text(`Deselect All ${selectedCount} in all pages`);
        //isSelectAll = false;
    } else {
        $(document).find('.generalText').text('in all pages');
    }
}

$('body').on('click', '.selectAllStudents', function (e) {
    e.preventDefault();
    let totalStudents = $(document).find('.totalStudent').first().text();
    $(document).find('#selected_title').text(`${totalStudents} students selected`);

    isSelectAll = isSelectAll ? false : true;
    let grid = $('#studentList').data('kendoGrid');
    let data = grid.dataSource.view();
    data.forEach((item) => {
        let row = grid.table.find("tr[data-uid='" + item.uid + "']");
        if (isSelectAll) {
            grid.select(row);
        } else {
            grid.clearSelection(row);
        }
    });

    manageFooterGridFilterText();
});

$('body').on('click', '#showInActiveCourse', function (e) {
    let activeCourse = 0;
    if ($(this).is(':checked')) {
        activeCourse = 0;
    } else {
        activeCourse = 1;
    }

    ajaxActionV2(
        'api/get-courses-list-for-filter',
        'POST',
        { activeCourse: activeCourse },
        function (response) {
            if (response.data) {
                let courseCheckBoxGroup = $('#course_id').data('kendoCheckBoxGroup');
                courseCheckBoxGroup.destroy();
                $('#course_id').empty().kendoCheckBoxGroup({
                    items: response.data,
                });
                hideShowMoreFilterData(studentsFilterPanelBarId, 1);
            }
        }
    );
});
