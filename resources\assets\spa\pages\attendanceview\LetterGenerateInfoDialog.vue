<template>
    <Dialog
        v-if="props.visibleDialog"
        :title="dialogTitle"
        :width="600"
        @close="toggleDialog"
        :class="'k-modal-window'"
        :dialog-class="'tw-dialog custom-modal-wrapper'"
        append-to="body"
    >
        <div class="k-window-content" v-if="!showLoader">
            <div
                class="flex w-full flex-row py-3"
                v-if="responseData.success_msg"
            >
                <h3 class="text-green-600">
                    <span class="k-icon k-i-check mr-2"></span>
                    <span id="titleEmailSuccessMsg">{{
                        responseData.success_msg
                    }}</span>
                </h3>
                <div v-if="responseData.success_count">
                    <PrimaryButton @click="downloadLetterZipFile">
                        <div
                            class="font-['Roboto'] text-sm font-medium uppercase leading-none text-white"
                        >
                            Download
                        </div>
                    </PrimaryButton>
                </div>
            </div>
            <div
                class="titleEmailFailMsg flex w-full flex-row py-3"
                v-if="responseData.fail_msg"
            >
                <h3 class="text-red-500">
                    <span class="k-icon k-i-cancel mr-2"></span>
                    <span id="titleEmailFailMsg">{{
                        responseData.fail_msg
                    }}</span>
                </h3>
            </div>
            <div class="flex w-full flex-row">
                <div
                    v-if="
                        responseData.failData &&
                        responseData.failData.length > 0
                    "
                    data-role="grid"
                    class="k-grid k-widget k-grid-display-block"
                >
                    <div class="k-grid-header" style="padding-right: 17px">
                        <div class="k-grid-header-wrap k-auto-scrollable">
                            <table role="grid">
                                <thead role="rowgroup">
                                    <tr role="row">
                                        <th class="k-header">
                                            <a class="k-link" href="#">NAME</a>
                                        </th>
                                        <th
                                            class="k-header"
                                            data-role="columnsorter"
                                        >
                                            <a class="k-link" href="#"
                                                >REASON</a
                                            >
                                        </th>
                                    </tr>
                                </thead>
                                <tbody role="rowgroup">
                                    <tr
                                        class="k-master-row"
                                        v-for="(
                                            value, key
                                        ) in responseData.failData"
                                        :key="key"
                                    >
                                        <td class="" role="gridcell">
                                            <div
                                                class="stud_45 flex items-center"
                                            >
                                                <div
                                                    class="user-profile-pic h-6 w-6 rounded-full bg-blue-500"
                                                >
                                                    <span
                                                        class="text-xs leading-6"
                                                        >GM</span
                                                    >
                                                </div>
                                                &nbsp;
                                                <div
                                                    class="student-first-name action-div text-sm leading-4 text-gray-600"
                                                >
                                                    {{ value.name }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="" role="gridcell">
                                            {{ value.reason }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div
                style="float: right"
                class="flex justify-center gap-2 px-2 py-2"
            >
                <PrimaryButton
                    @click="downloadZip"
                    v-if="responseData.file_name"
                >
                    <div
                        class="font-['Roboto'] text-sm font-medium uppercase leading-none text-white"
                    >
                        Download
                    </div>
                </PrimaryButton>
                <PrimaryButton @click="toggleDialog">
                    <div
                        class="font-['Roboto'] text-sm font-medium uppercase leading-none text-white"
                    >
                        Done
                    </div>
                </PrimaryButton>
            </div>
        </div>
        <div
            v-else
            class="flex h-full flex-col items-center justify-start gap-6 self-stretch px-6 pb-8"
        >
            <icon name="loading-spinner" :width="'75'" :height="'50'" />
        </div>
        <!-- <div class="w-full h-full flex-col justify-start items-start inline-flex">
          <div v-if="responseData.success_msg" class="self-stretch h-full px-6 pb-8 flex-col justify-start items-center gap-6 flex">
            <div class="w-full h-[247px] flex-col justify-center items-start gap-2 inline-flex">
                <div class="self-stretch h-[247px] px-6 flex-col justify-start items-start gap-6 flex">
                    <div class="self-stretch h-6 flex-col justify-center items-start gap-2 flex">
                        <div class="w-full justify-start items-center gap-2 inline-flex">
                            <div class="w-6 h-6 pl-[3px] pr-[3.50px] pt-1.5 pb-[5px] justify-center items-center flex">
                                <img src="/v2/img/Checkmark.svg" class="" alt="overseasIcon">
                            </div>
                            <div class="text-gray-500 text-sm font-normal font-['Roboto'] leading-tight tracking-tight">{{ responseData.success_msg }}</div>
                        </div>
                    </div>
                    <div class="flex-col justify-start items-start gap-4 flex">
                        <div class="text-red-500 text-sm font-normal font-['Roboto'] leading-tight tracking-tight">Some Students were not sent letter.</div>
                            <div class="w-full bg-white border border-gray-200 justify-start items-start inline-flex">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Reason</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(value, key) in responseData.failData" :key="key">
                                            <th> {{ value.name }}</th>
                                            <th>{{ value.reason }}</th>
                                        </tr>
                                    </tbody>
                                </table>

                            </div>
                    </div>
                </div>
                </div>
          </div>
          <div v-else class="self-stretch h-full px-6 pb-8 flex-col justify-start items-center gap-6 flex">
            Please wait.....
          </div>
          <div v-if="responseData.success_msg" class="w-full px-6 py-4 bg-white shadow-inner justify-between items-center inline-flex">
              <div class="h-[34px] justify-end items-center gap-4 flex">
                  <PrimaryButton @click="downloadZip">
                      <div class="text-white text-sm font-medium font-['Roboto'] uppercase leading-none">Download Letter Zip</div>
                  </PrimaryButton>
              </div>
          </div>
      </div> -->
    </Dialog>
</template>

<script setup>
import { defineProps } from "vue";
import { Dialog } from "@progress/kendo-vue-dialogs";
import PrimaryButton from "@spa/components/Buttons/PrimaryButton.vue";
import Loader from "../../components/Loader/Spinner.vue";
import { downloadLetterPdfWithWatermark } from "@spa/services/attendance/attendanceview";

const props = defineProps({
    visibleDialog: Boolean,
    responseData: Array,
    dialogTitle: String,
    showLoader: Boolean,
});

const emits = defineEmits(["closeDialogEmit"]);

const toggleDialog = () => {
    emits("closeDialogEmit");
};
const downloadZip = () => {
    window.location.href = props.responseData.file_name;

    toggleDialog();
};
const downloadLetterZipFile = async () => {
    var response = await downloadLetterPdfWithWatermark();
    window.location.href = "/spa/download-letter-pdf/" + response.file;
};
</script>

<style></style>
