/* Pagination */

.k-pager-wrap {
    background-color: white;
    color: #6b7280;
}

.k-grid-pager {
    border-top: 1px solid #e5e7eb;
}

.k-pager-numbers .k-link {
    color: #6b7280;
}

.k-pager-numbers .k-link.k-state-selected {
    color: #1890ff;
    background-color: #e6f7ff;
}

.k-pager-numbers .k-link:hover {
    color: #1890ff;
    background-color: #e6f7ff;
}

.gridInfo .k-pager-info {
    /* display: contents; */
    width: 80%;
}

.gridPagination {
    border: none;
}

.k-pager-numbers .k-link,
.k-pager-numbers .k-link:hover,
.k-pager-numbers .k-state-selected {
    border-radius: 0;
    /*Use squares instead of circles*/
    border: 1px solid #bbbbbb;
}

/*Selected page number*/

.k-pager-numbers .k-state-selected {
    border-color: #bbbbbb;
}

/*Pager links*/

.k-pager-wrap > .k-link {
    border-radius: 0;
    border: 1px solid #bbbbbb;
}

.k-pager-wrap > .k-link:hover {
    border-color: #bbbbbb;
    background-color: #e6f7ff;
}

.k-pager-last {
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
}

.k-pager-first {
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
}

.k-grid-pager .k-link,
.k-grid-pager .k-pager-numbers {
    float: none;
}

/* Page Info */

.gridInfo .k-pager-info {
    /* display: contents; */
    width: 80%;
}

.gridInfo .k-pager-sizes {
    display: inline-flex;
    /* width: 24%; */
    /* border: 1px solid #c9c9c9; */
    border-radius: 0.25rem;
    /* height: 35px; */
}

.gridPagination {
    border: none;
}

.tw-scroll-pagination-loader {
    .k-loading-mask {
        top: unset !important;
        bottom: 0;
        height: 180px !important;
    }
    .k-loading-color {
        background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0) 17%,
            rgba(255, 255, 255, 1) 100%
        );
    }
}
