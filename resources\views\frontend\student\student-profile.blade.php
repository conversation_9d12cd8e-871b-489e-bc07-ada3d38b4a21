@extends('frontend.layouts.frontend')
@section('title',  $pagetitle  )
@section('content')
<script>
    var studentId = '<?php echo $studentId; ?>';
</script>
<style>
.unitBtn {float: left;width: 100%;}
.unBtn {display: inline-block;padding-left: 0; margin-left: -23px;}
table.table-custom tbody tr td.unitBtn { padding-left: 30px !important;}
.unitContent {width: 100%;display: none;background-color: #f1f1f1;padding: 10px;border: 1px solid #dfe2e5;}
.action-div {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
</style>
@section('content-header')

<!--<section class="content-header">
    <div class="text-left title-with-breadcrumb">
        <h2 class="page-title-text">Application Preview</h2>
        <ol class="breadcrumb">
            <li><a href="{{ route('user_dashboard') }}"><i class="fa fa-book"></i> Home</a></li>
            <li class="active">Apply Online</li>
        </ol>
        <div style="clear: both;"></div>
    </div>
</section>-->

@endsection

<section class="content box-info ">
    <div class="custom-header">
        <div class="row">
            <div class="col-md-10">
                <h3 class="box-title">Application Preview</h3>
            </div>
        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-4">
                <!-- Widget: user widget style 1 -->
                <div class="box box-widget widget-user">
                    <!-- Add the bg color to the header using any of the bg-* classes -->
                    <div class="widget-user-header bg-black" style="background: url('../dist/img/photo1.png') center center;">
                        &nbsp;
                    </div>
                    <!-- if($sessionPermissionData=="yes") -->
                    <!-- <a href="{{ route('student-profile-edit', array('id' => $studentProfile->id)) }}" class="user-edit-icon" title="Edit Profile">
                        <i class="fa fa-pencil-alt"></i>
                    </a>  -->
                    <a target="_blank" href="{{ $applicationurl.'/'.$studentProfile->uuid }}" class="user-edit-icon" title="Edit Profile">
                        <i class="fa fa-pencil-alt"></i>
                    </a>
                    <!-- endif -->

                    <div class="widget-user-image">
                        <img class="img-circle" id="stud-profile-pic-circle" src="{{ asset($profile_pic) }}" alt="Student Profile Picture">
                        <!--<img class="img-circle" src="../dist/img/user3-128x128.jpg" alt="User Avatar">-->
                        <!--<span data-toggle="modal" class="" data-id="{{ $studentId }}" data-target="#uploadPicModal">-->
                        <a href="javascript:;" data-toggle="tooltip" data-original-title="Update Picture" class="icon-action-mini icon-action-blue uploadStudPic" data-stud-id="{{ $studentId }}">
                            <i class="fa fa-pencil-alt"></i>
                        </a>
                        <!--</span>-->

                    </div>
                    <div class="widget-user-name-id">
                        <h3 class="widget-user-username action-div">{{ (empty($studentProfile->name_title) ? '' : $studentProfile->name_title) .' '. (empty($studentProfile->first_name) ? '' : $studentProfile->first_name) .' '. (empty($studentProfile->middel_name) ? '' : $studentProfile->middel_name) .' '. (empty($studentProfile->family_name) ? '' : $studentProfile->family_name) }}</h3>
                        <h5 class="widget-user-desc">ID: {{ empty($studentProfile->generated_stud_id) ? 'N/A' : $studentProfile->generated_stud_id }}</h5>

                        <ul class="icon-actions-set">
                            <li><a href="{{ route('student-profile-send-mail',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Email" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="far fa-envelope "></i></a></li>
                            <li><a href="{{ route('student-send-sms',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="SMS" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-commenting"></i></a></li>
                            <li><a href="{{ route('student-send-letter',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Letter" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="far fa-file"></i></a></li>
                            <li><a href="{{ route('student-course-information',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Course" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-graduation-cap"></i></a></li>
                            <li><a href="{{ route('student-document-upload',array('student_id' => $studentId, 'parent_id' => '0')) }}" class="icon-action-standard icon-action-blue" data-original-title="Upload" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-upload"></i></a></li>
                            <li><a href="{{ route('student-payment-summary',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Payment" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-dollar"></i></a></li>
                            <li><a href="{{ route('student-course-checklist',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Checklist" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <!--<li><a href="{{ route('student-subject-enrollment',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Result" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-course-deferral',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Defer" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-intervention', array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Intervention" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-communication-log',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Diary" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <BR>
                            <li><a href="{{ route('student-card',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Std Card" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-attendance-summary',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Attd" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-oshc',array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="OHSC" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-interview', array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Interview" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <BR>
                            <li><a href="{{ route('add-student-sanction', array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Sanction" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-training-plan', array('student_id' => $studentId, 'id' => '0')) }}" class="icon-action-standard icon-action-blue" data-original-title="Training Plan" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="{{ route('student-claim-tracking-list', array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Claim Tracking" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <BR>
                            <li><a href="{{ route('student-vet-fee-help-add', array('id' => $studentId)) }}" class="icon-action-standard icon-action-blue" data-original-title="Search" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <li><a href="#" class="icon-action-standard icon-action-blue" data-original-title="empty" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            <BR>
                            <li><a href="#" class="icon-action-standard icon-action-blue" data-original-title="empty" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check "></i></a></li>
                            -->
                        </ul>
                    </div>
                    <div class="user-details">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="label-text">EMAIL</div>
                                <div class="label-value">{{ empty($studentProfile->email) ? 'N/A' : $studentProfile->email }}</div>

                                <div class="label-text">Mobile</div>
                                <div class="label-value">{{ empty($studentProfile->current_mobile_phone) ? 'N/A' : $studentProfile->current_mobile_phone }}</div>

                                <div class="label-text">USI</div>
                                <div class="label-value">{{ empty($studentProfile->USI) ? 'N/A' : $studentProfile->USI }}</div>

                                @if($isAllowToSync)
                                <button type="button" class="syncToXero bg-blue-500 hover:bg-blue-700 text-white py-2 px-4 rounded" data-toggle="modal" data-target="#syncToXeroModel" data-student-id="{{ $studentId }}">
                                    Sync to Xero
                                </button>
                                @endif

                                @if($xeroContact)
                                    @if(!$xeroContact->xero_failed_at)
                                        @php
                                            $xeroSyncedAt = new DateTime($xeroContact->xero_synced_at);
                                            $formattedSyncedAt = $xeroSyncedAt->format('M j Y, h:i A');
                                        @endphp
                                        <div class="py-2">
                                            <button type="button" class="bg-green-500 text-white py-2 px-4 rounded" disabled>
                                                Synced to xero at {{ $formattedSyncedAt }}
                                            </button>
                                        </div>

                                        <div class="label-text">Xero ID</div>
                                        <div class="label-value">{{ $xeroContact->xero_contact_id }}</div>

                                        <div class="label-text">Xero Status</div>
                                        <div class="label-value">{{ $xeroContact->xero_contact_status }}</div>

                                        <div class="label-text flex space-x-4">
                                            <span class="pt-1.5"> Prepayment Received</span>
                                            <a class="refreshOutStandingBalanceIcon cursor-pointer" title="Refresh Outstanding Balance" data-student-id="{{ $studentProfile->id }}">
                                                <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-6 w-6" alt="Arrow Sync">
                                            </a>
                                        </div>
                                        <div class="label-value outStandingBalance">${{ number_format($xeroContact->outstanding_balance, 2) }}</div>

                                        <div class="label-text flex space-x-4">
                                            <span class="pt-1.5">Unallocated Credit</span>
                                            <a class="refreshUnallocatedCreditIcon cursor-pointer" title="Refresh Unallocated Balance" data-student-id="{{ $studentProfile->id }}">
                                                <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-6 w-6" alt="Arrow Sync">
                                            </a>
                                        </div>
                                        <div class="label-value outUnallocatedBalanceText">${{ number_format($xeroContact->unallocatedCredit, 2) }}</div>

                                    @else
                                        @php
                                            $xeroFailedAt = new DateTime($xeroContact->xero_failed_at);
                                            $formattedFailedAt = $xeroFailedAt->format('M j Y, h:i A');
                                        @endphp
                                        <div class="label-text pt-2">Sync Failed At</div>
                                        <div class="label-value">{{ $formattedFailedAt }}</div>

                                        <div class="label-text">Failed Reason</div>
                                        <div class="label-value">{{ !empty($xeroContact->xero_failed_message) ? $xeroContact->xero_failed_message : "Something want wrong" }}</div>
                                    @endif
                                @endif
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <section class="content-header" style="padding:0px">
                    @include('frontend.student.student-module-list')
                    <div style="clear: both;"></div>
                </section>

                <div class="table-responsive no-padding">
                    <table class="table table-hover table-custom">
                        <thead>
                            <tr>
                                <th colspan="4"> <h3 class="box-title no-margin">Courses</h3> </th>
                            </tr>
                        </thead>
                        <thead>
                            <tr>
                                <th> Courses </th>
                                <th> Offer Status </th>
                                <th> Status </th>
                                <th> Agent Name </th>
                                <th> Study Period </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if($studentCourses->count() > 0)
                            @foreach($studentCourses as $studCourses)
                            <tr>
                                <td>
                                    <div class="action-overlay">
                                        <!--<ul class="icon-actions-set">-->
                                        <ul class="icon-actions-set">
                                            <li><a href="javascript:;" data-student-id="{{ $studCourses->student_id }}" data-course-id="{{ $studCourses->course_id }}" class="link-black text-sm academic-summary" data-original-title="View Progress Summary" data-toggle="tooltip" data-load-remote="#"><i class="far fa fa-file-code"></i></a></li>
                                            <li><a href="javascript:;" data-student-id="{{ $studCourses->student_id }}" data-course-id="{{ $studCourses->course_id }}" class="link-black text-sm view_payment" data-original-title="View Payment Summary" data-toggle="tooltip" data-load-remote="#"><i class="fa fa fa-usd"></i></a></li>
                                            <li>
                                                <span data-toggle="modal" class="" data-id="" data-target="#statusHistoryModal">
                                                    <a href="javascript:;" data-student-id="{{ $studCourses->student_id }}" data-course-id="{{ $studCourses->student_course_id }}" class="link-black text-sm status_history" data-original-title="View Status Change History" data-toggle="tooltip" data-load-remote="#"><i class="fa fa fa-cog"></i></a>
                                                </span>
                                            </li>
                                            <li><a href="javascript:;" data-student-id="{{ $studCourses->student_id }}" data-course-id="{{ $studCourses->course_id }}" class="link-black text-sm course-academic-summary" data-original-title="View academic summary" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-edit"></i></a></li>
                                            <li>
                                                <span data-toggle="modal" class="courseDelete" data-id="{{ $studCourses->student_course_id }}">
                                                    <a class="link-black text-sm" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i> </a>
                                                </span>
                                            </li>
                                            <li><a href="{{ route('student-profile-checklist',array('id'=>$studCourses->student_course_id,'student_primary'=>$studentId) ) }}" class="link-black text-sm" data-original-title="Student Checklist" data-id="00" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-check"></i></a></li>
                                        </ul>
                                    </div>

                                    @php
                                    $applied_course = $studCourses->course_code .' : '.$studCourses->course_name;
                                    @endphp
                                    <span data-original-title="{{ $studCourses->course_name }}" data-toggle="tooltip">{{ (strlen($applied_course) > 25) ? (substr($applied_course, 0, 25).'...') : $applied_course }}</span>
                                    <div class='info-dis'>
                                        <label class="info-title">Campus :</label>
                                        <span class="info-text">{{ (strlen($studCourses->campus_name) > 15) ? (substr($studCourses->campus_name, 0, 15).'...') : $studCourses->campus_name }}</span>
                                    </div>
                                </td>
                                <td>{{ empty($studCourses->offer_status) ? 'NA' : $studCourses->offer_status }}</td>
                                <td>{{ empty($studCourses->status) ? 'NA' : $studCourses->status }}</td>
                                <td>{{ empty($studCourses->agency_name) ? 'NA' : $studCourses->agency_name }}</td>
                                <td>{{ date('d M y', strtotime($studCourses->start_date)) .' - '. date('d M y', strtotime($studCourses->finish_date)) }}</td>
                            </tr>
                            @endforeach
                            @else
                            <tr>
                                <td colspan="4" style="text-align: center;">
                                    <p style="color:red;">No Record Found</p>
                                </td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
                @if($pagination != "")
                <div class="box-footer clearfix text-center">
                    <ul class="pagination pagination-sm no-margin">
                        {!! $pagination !!}
                    </ul>
                </div>
                @endif
            </div>

            <div class="col-md-8">
                <div class="table-responsive no-padding">
                    <table id="studentTask" class="table table-hover table-custom">
                        <thead>
                            <tr>
                                <th colspan="4"> <h3 class="box-title no-margin">Task</h3> </th>
                            </tr>
                        </thead>
                        <thead>
                            <tr>
                                <th> Task Name </th>
                                <th> Due Date </th>
                                <th> Priority </th>
                                <th> Status </th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(count($studentTasks) > 0)
                            @foreach($studentTasks as $studentTask)
                            <tr data-id="{{ $studentTask['taskid'] }}">
                                <td>
                                    <div class="action-overlay">
                                        <!--<ul class="icon-actions-set">-->
                                        <ul class="icon-actions-set">
                                            <li>
                                                <span data-toggle="modal" class="deleteTask" data-id="{{ $studentTask['taskid'] }}" data-target="#deleteModalAjax">
                                                    <a href="javascript:;" data-id="{{ $studentTask['taskid'] }}"  class="link-black text-sm" data-original-title="Delete Task" data-toggle="tooltip" data-load-remote="#"><i class="fa fa-times"></i></a>
                                                </span>
                                            </li>
                                       </ul>
                                    </div>

                                    {{ $studentTask['title'] }}
                                </td>
                                <td>{{ ($studentTask['due_date'] != '') ? date('d M y', strtotime($studentTask['due_date'])) : '' }}</td>

                                <td><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{ $studentTask['priority_color']}}-100 text-{{ $studentTask['priority_color']}}-500">  {{ $studentTask['priority'] }} </span></td>
                                <td><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{ $studentTask['status_color']}}-100 text-{{ $studentTask['status_color']}}-500">  {{ $studentTask['status'] }} </span></td>

                            </tr>
                            @endforeach
                            @else
                            <tr>
                                <td colspan="4" style="text-align: center;">
                                    <p style="color:red;">No Record Found</p>
                                </td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                </div>

            </div>
            <div class="col-md-12 mt-50">
                <div class="nav-tabs-custom">
                    <ul class="nav nav-tabs nav-tabs-gradient">
                        <li class="active"><a href="#tab_1" data-toggle="tab">Result</a></li>
                        <li><a href="#tab_2" class="view_payment_tab" data-toggle="tab" data-student-id="{{$studentId}}" >Payment</a></li>
                        <li><a href="#tab_3" data-toggle="tab">Weekly timetable</a></li>
                        <li><a href="#tab_4" class="view_attendance_overview"  data-toggle="tab" data-student-id="{{$studentId}}" >Attendance overview</a></li>
                        <li><a href="#tab_5" data-toggle="tab">Emergency contact</a></li>
                        <li><a href="#tab_6" data-toggle="tab" id="tab_61">Activity Log</a></li>
                    </ul>
                    <div class="tab-content border-thin">
                        <!-- /.tab-pane1 -->
                        <div class="tab-pane active" id="tab_1">
                            <div class="box box-custom">
                                <div class="table-responsive no-padding">
                                    <table class="table table-hover table-custom">
                                        <thead>
                                            <tr>
                                                <th>Semester/Term</th>
                                                <!--<th>Course Stage</th>-->
                                                <!--<th>Campus</th>-->
                                                <th>Subject/Module</th>
                                                <th>Batch</th>
                                                <th>Activity Date</th>
                                                <th>Final Outcome</th>
                                            </tr>
                                        </thead>
                                        <tbody class="studentResultDetail">
                                            @if(count($arrSubjectUnitList) > 0)
                                                <div class="row" style="margin: 0 !important;border: 1px solid #e6eaee;padding: 12px 0;border-bottom: 0;">
                                                    <div class="col-sm-3 form-group col-sm-offset-7" style="margin-left:75%;z-index: 999">
                                                        <select id="select_course_id" class="form-control updateContent">
                                                            <!--<option value="select">Select Course</option>-->
                                                        @foreach($studentCourses as $studCourses)
                                                                <option value="{{$studCourses->course_id}}">{{$studCourses->course_code .' : '.$studCourses->course_name}}</option>
                                                        @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            @foreach($arrSubjectUnitList as $row)
                                            <tr  class="std studentData{{ $row['course_id'] }}">
                                                <td>
                                                    <div class="action-overlay">
                                                        <ul class="icon-actions-set">
                                                            <li><a href="{{ route('edit-student-result',array('id'=>$row['id'],'course_id'=>$row['course_id'])) }}" class="link-black text-sm" data-toggle="tooltip" data-original-title="Update student module/subject outcome"><i class="fa fa-file-code"></i></a></li>
                                                            <li><a href="{{ route('student-unit-enrollment',array('id'=>$row['id'])) }}" class="link-black text-sm" data-toggle="tooltip" data-original-title="Update student result unit outcome"><i class="fa fa-bar-chart"></i></a></li>
                                                            <li><a href="{{ route('edit-student-unit-activity',array('id'=>$row['id'])) }}" class="link-black text-sm" data-toggle="tooltip" data-original-title="Update student unit activity AVETMISS value"><i class="fa fa-gavel"></i></a></li>
                                                            @if(empty($row['batch']))
                                                            <li><a href="{{ route('student-assessment-details',array('id'=>$row['id'])) }}" class="link-black text-sm" data-toggle="tooltip" data-original-title="Manage assessment for Without Class Or Training Plan"><i class="fa fa-line-chart "></i></a></li>
                                                            <li>
                                                                <span data-toggle="modal" class="" data-id="" data-target="#batchModal">
                                                                    <a href="javascript:;"  class="link-black text-sm getBatchModal" data-id="{{ $row['id'] }}" data-toggle="tooltip" data-original-title="New enrolment with different batch"><i class="fa fa-cog"></i></a>
                                                                </span>
                                                            </li>
                                                            @else
                                                            <li>
                                                                {{ Form::model(array('method' => 'post'), ['class' => 'form-horizontal vertical-add-form','route' => array('task-entry')]) }}
                                                                <input type="hidden" name="subject_id" value="<?php echo $row['subject_id']; ?>" id="subject_id">
                                                                <input type="hidden" name="class_id" value="<?php echo $row['batch']; ?>" id="class_id">
                                                                <input type="hidden" name="course_type_id" value="<?php echo $row['course_type_id']; ?>" id="course_type_id">
                                                                <input type="hidden" name="semester_id" value="<?php echo $row['semester_id'] . "-" . $row['term']; ?>" id="semester_id">
                                                                <input type="hidden" name="year" value="<?php echo date('Y', strtotime($row['start_week'])); ?>" id="year">
                                                                <button class="button-fa" type="submit"><i class="fa fa-search "></i> </button>
                                                                {{ Form::close() }}
                                                            </li>
                                                            @endif

                                                            @if($row['flexible_attendance']=="1")
                                                            <li><a href="{{ route('student-flexible-timetable',array('id'=>$row['id'])) }}" class="link-black text-sm" data-toggle="tooltip" data-original-title="Flexible timetable selection"><i class="fa fa-calendar-plus"></i></a></li>
                                                            @endif
                                                            @if($sessionPermissionData=="yes")
                                                            <li>
                                                                <span data-toggle="modal" class="delete" data-id="{{ $row['id'] }}" data-target="#deleteModal">
                                                                    <a href="javascript:;" class="link-black text-sm" data-toggle="tooltip" data-original-title="Delete the selected row?"><i class="fa fa-times "></i>
                                                                    </a>
                                                                </span>
                                                            </li>
                                                            @endif
                                                        </ul>
                                                    </div>
                                                    {{ $row['semester_name'].'(Term '.$row['term'].')' }}
                                                </td>
                                                <!--<td>{{ $row['course_stage'] }}</td>-->
                                                <!--<td>{{ empty($row['campus_name']) ? '' : $row['campus_name'] }}</td>-->
                                                <td class="unitBtn" data-isHigherEd="{{ $row['isHigherEd'] }}" data-subjectId="{{ $row['subject_id'] }}" data-courseId="{{ $row['course_id'] }}" data-studentId="{{ $studentId }}">
                                                    <div class="unBtn" name="ubtm">
                                                        <i class="fa fa-plus "></i>
                                                    </div> &nbsp;
                                                    {{ $row['subject_code'].' : ' . $row['subject_name'] }}
                                                </td>
                                                <td>{{ empty($row['batch']) ? 'No Batch' : $row['batch'] }}</td>
                                                <td> @if(!empty($row['batch']))
                                                        {{ date('d-m-Y',strtotime($row['activity_start_date'])) }} to {{ date('d-m-Y',strtotime($row['activity_finish_date'])) }}
                                                     @else
                                                        {{ "No Batch Found" }}
                                                     @endif
                                                </td>
                                                <td>
                                                    @if($row['isHigherEd'])
                                                        {{ ($row['mark_outcome'] && $arrSelectMarksOutcome[$row['mark_outcome']]) ? $arrSelectMarksOutcome[$row['mark_outcome']] : 'N/A' }}
                                                    @else
                                                        {{ $arrSelectFinalOutcome[$row['final_outcome']] !="- - Select Final Outcome - -" ? $arrSelectFinalOutcome[$row['final_outcome']] : 'Enrolled' }}
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr class="unitContent">
                                                <td class="unitCon" colspan="6">
                                                    <strong>
                                                    <span class="unit_code">
                                                        <ul class="unit_data_list unitCompentencyDetail">

                                                        </ul>
                                                    </span></strong>
                                                </td>
                                            </tr>

                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                    @if(count($arrSubjectUnitList) > 0)
                                    <div class="col-md-offset-11 col-md-1">
                                        <a href="{{ route('student-subject-enrollment', array('id' => $studentId)) }}" class="" data-original-title="More results" data-toggle="tooltip">More>></a>
                                    </div>
                                    @else
                                    <div class="text-center" id="noRecords">
                                        <p style="color:red;">No Record Found</p>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <!-- /.tab-pane1 -->

                        <!-- /.tab-pane2 -->
                        <div class="tab-pane" id="tab_2">
                            <div class="box box-custom">
                                <div class="table-responsive no-padding">
                                    <div class="row" style="margin: 0 !important;border: 1px solid #e6eaee;padding: 12px 0;border-bottom: 0;">
                                        <div class="col-sm-3 form-group col-sm-offset-12" style="margin-left:75%;z-index: 999">
                                            <select id="payment_course_id" class="form-control updateContent">
                                                @foreach($studentCourses as $studCourses)
                                                    <option value="{{$studCourses->course_id}}">{{$studCourses->course_code .' : '.$studCourses->course_name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <table class="table table-hover table-custom">
                                        <thead>

                                        </thead>
                                        <tbody id="studentPaymentDetail"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <!-- /.tab-pane2 -->

                        <!-- /.tab-pane3 -->
                        <div class="tab-pane" id="tab_3">
                            <div class="box box-custom">
                                <div class="table-responsive no-padding">
                                    <table class="table table-hover table-custom">
                                        <thead>
                                            <tr>
                                                <th>Monday</th>
                                                <th>Tuesday</th>
                                                <th>Wednesday</th>
                                                <th>Thursday</th>
                                                <th>Friday</th>
                                                <th>Saturday</th>
                                                <th>Sunday</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                @foreach ( $arrDays as  $objArrDays )
                                                <td>
                                                    @if(!empty($arrTimeTableDay[$objArrDays]))

                                                    @for ($i = 0; $i < count($arrTimeTableDay[$objArrDays]); $i++)
                                                    <b class="error">S:</b>{{ $arrTimeTableDay[$objArrDays][$i]['subject_name']}}<br />
                                                    <b class="error">B:</b>{{ $arrTimeTableDay[$objArrDays][$i]['batch']}}<br />
                                                    <b class="error">R:</b> {{ $arrTimeTableDay[$objArrDays][$i]['room_id']}}<br />
                                                    <b class="error">T:</b>{{ $arrTimeTableDay[$objArrDays][$i]['start_time']}}-{{ $arrTimeTableDay[$objArrDays][$i]['finish_time']}}<br />
                                                    <b class="error">D:</b>{{ date("j M Y", strtotime($arrTimeTableDay[$objArrDays][$i]['start_week']))}} - {{ date("j M Y", strtotime($arrTimeTableDay[$objArrDays][$i]['end_week']))}}<br />
                                                    <span style="width: 100%;height:1px;background:#000;display: block;"> &nbsp; </span>
                                                    @endfor

                                                    @else
                                                    <b class="error">NC</b>
                                                    @endif
                                                </td>
                                                @endforeach
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <!-- /.tab-pane3 -->

                        <!-- /.tab-pane4 -->
                        <div class="tab-pane" id="tab_4">
                            <div class="box box-custom">
                                <div class="box box-custom" >
                                    <div class="row" style="margin: 0 !important;border: 1px solid #e6eaee;padding: 12px 0;border-bottom: 0;">
                                           <div class="col-sm-3 form-group col-sm-offset-7">
                                           @if(!empty($arrSemestor))
                                            {{ Form::select('semestor_id', $arrSemestor , null, array('class' => 'form-control updateContent', 'id' => 'semestor_id')) }}
                                           @endif
                                            </div>

                                             <div class="col-sm-2 form-group">
                                                 @if(!empty($arrTerm))
                                                {{ Form::select('term', $arrTerm , null, array('class' => 'form-control updateContent', 'id' => 'term')) }}
                                                @endif
                                            </div>

                                    </div>
                                    <div class="table-responsive no-padding" >
                                        <table class="table table-hover table-custom" width="50%" id="attendance-overview"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /.tab-pane4 -->

                        <!-- /.tab-pane5 -->
                        <div class="tab-pane" id="tab_5">
                            <div class="box box-custom">
                                <div class="table-responsive no-padding">
                                    <table class="table table-hover table-custom" width="50%">
                                        <tbody>
                                            <tr>
                                                <th class="text-right">Contact Type : </th>
                                                <td>Emergency</td>
                                            </tr>
                                            <tr>
                                                <th class="text-right">Person Name : </th>
                                                <td>{{ $StudentDetails[0]->emergency_contact_person }}</td>
                                            </tr>
                                            <tr>
                                                <th class="text-right">Phone : </th>
                                                <td>{{ $StudentDetails[0]->emergency_phone }}</td>
                                            </tr>
                                            <tr>
                                                <th class="text-right">Address : </th>
                                                <td>{{ $StudentDetails[0]->emergency_address }}</td>
                                            </tr>
                                            <tr>
                                                <th class="text-right">Relationship : </th>
                                                <td>{{ $StudentDetails[0]->emergency_relationship }}</td>
                                            </tr>
                                            <tr>
                                                <th class="text-right">Email : </th>
                                                <td>{{ $StudentDetails[0]->emergency_email }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <!-- /.tab-pane5 -->

                        <!-- /.tab-pane6 -->
                        <div class="tab-pane" id="tab_6">
                            <div class="box box-custom">
                                <div class="table-responsive no-padding">
                                    <div class="row" style="margin: 0 !important;border: 1px solid #e6eaee;padding: 12px 0;border-bottom: 0;">
                                        <div class="col-md-12">
                                        <div class="box box-custom">
                                            <div class="box-header">
                                                <h3 class="box-title" style="line-height: 30px !important;">Log Details</h3>
                                                <div class="icon-actions-set pull-right">
                                                    <a href="{{ route('student-communication-log',array('id' => $studentId)) }}" class="icon-action-title icon-action-blue ">
                                                        <i class="fa fa-plus "></i>
                                                    </a>
                                                </div>
                                                <div style="clear: both;"></div>
                                            </div>
                                            <div class="box-body border-top-1" style="background-color: #ececec !important;">

                                                @if(!empty($studentCommunicationLog))
                                                <table id="staffData" class="table table-hover table-custom">
                                                    <thead>
                                                        <tr>
                                                            <th>Date/Time Log</th>
                                                            <th>Added/Commented By</th>
                                                            <th>Type</th>
                                                            <th>Status</th>
                                                            <th>Comment Log</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($studentCommunicationLog as $key => $value)
                                                            @foreach($value as $values)
                                                            <tr>
                                                                <td>{{ date('d M Y', strtotime($key))}} {{ date('h:i:s A',strtotime($values['created_at'])) }}</td>
                                                                <td>{{ $values['user_name'] }}</td>
                                                                <td>{{ $values['type'] }}</td>
                                                                <td>{{ $values['status'] }}</td>
                                                                <td><?= $values['log'] ; ?></td>
                                                            </tr>
                                                            @endforeach
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                                @else
                                                <div class="text-center">
                                                    <p style="color:red;">No Record Found</p>
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /.tab-pane6 -->


                    </div>
                    <!-- /.tab-content -->
                </div>
            </div>


        </div>
    </div>
</section>

<div class="modal fade" id="deletecoursemodal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title">Delete Record</h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <p> You want to delete record. Are you sure?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                <button id="yesSure" class="btn btn-success" type="button" data-id="">Yes</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
<div class="modal fade" id="deleteModal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title">Delete Record</h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <p> You want to delete record. Are you sure?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                <button class="btn btn-success yes-sure" type="button" data-id={{ $studentId }}>Yes</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
<div class="modal fade" id="deleteModalAjax" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title">Delete Record</h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <p> You want to delete record. Are you sure?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                <button class="btn btn-success yes-sure-ajax" type="button" data-id="">Yes</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>

<div class="modal fade" id="uploadPicModal" role="dialog">
    {{ Form::open( array('method' => 'post', 'id' => 'studentProfilePic', 'files' => 'true', 'class' => 'form-horizontal vertical-add-form', 'route' => array('student-profile-picture', $studentId))) }}
    <?php // echo Form::model(array('method' => 'post'), ['class' => 'form-horizontal vertical-add-form', 'files' => 'true', 'id' => 'addCollegeMaterialsFormsUploads', 'action' => route('student-profile-picture', array('id' => $studentId))]); ?>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title"> Upload Student Picture </h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                <!--                        <div class="form-group">
                                                            <label for="images" class="control-label"> Please Upload Student Picture file with .jpg extension </label><br/><br/>
                                                            {{ Form::file('images[]', NULL,  array('class' => 'inputfile inputfile-2', 'id' => 'images')) }}
                                                        </div>-->
                                <div class="form-group margin-minus-15">
                                    <div class="input-group">
                                        <label for="images" class="control-label"> Please Upload Student Picture file with .jpg extension </label><br/><br/>
                                        <input type="file" name="images[]" id="images" class="inputfile inputfile-2" />
                                        <label for="images"><i class="fa fa-upload"></i><span class='file-name'> Choose a File&hellip;</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button"> Cancel </button>
                <input name="Uploads" class="btn btn-info" id="uploads" value="Uploads" type="submit">
            </div>
        </div>
    </div>
    {{ Form::close() }}
</div>

<div class="modal fade" id="courseActionModal" role="dialog">
    <div class="modal-dialog" style="width: 900px;">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title" id="courseActionTitle"></h4>
            </div>
            <div class="modal-body">
                {{ Form::hidden('studenIdHidden', $studentId  , array('class' => '', 'id' => 'studenIdHidden')) }}
                <div id="courseActionDetail"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="syncToXeroModel" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title">Sync to Xero</h4>
            </div>
            <div class="modal-body">
                <p> Are you sure you want to sync this student with XERO?</p>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                <button class="btn btn-success syncStudent" type="button">Yes</button>
            </div>
        </div>
    </div>
</div>

<style>

   #filesHtml {
        display: flex;
    }
    .fileinfo {
        display: inline-grid;
        margin: 10px;
    }
    .taskactionicon{
    background: #fff;
    border: 1px solid #dfe2e5;
    padding: 0;
    border-radius: 50px;
    text-align: center;
    vertical-align: middle;
    color: #6f8199;
    width: 30px;
    line-height: 30px;
    height: 30px;
    display: inline-block;
}

.title-date {
    padding: 15px 0 0;
}

.title-date h6 {
    color: #00075a;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 600;

}

.date-icon-box {
    text-align: center;
    margin: 0 auto;
    position: relative;
}

.date-icon-box::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 25px;
    background-color: gray;
    left: 50%;
    transform: translate(-50%);
    bottom: -25px;
}
/* selector + [data-readmore-toggle], selector[data-readmore] {
  display: block;
  width: 100%;
} */
.top-line {
    position: relative;
}

.top-line::before {
    content: '';
    position: absolute;
    width: 0px;
    height: 0px;
    background-color: gray;
    left: 50%;
    transform: translate(-50%);
    top: -14px;
}

.date-icon-box p {
    font-size: 13px;
    font-weight: 500;
    color: #1f68a4;
    line-height: 16px !important ;
}

.date-icon-box .icon-box {
    width: 43px;
    height: 43px;
    background-color: rgb(9 ,176, 57);
    margin: 1px auto 0;
    line-height: 47px;
    border-radius: 50%;
}

.date-icon-box .icon-box i {
    color: #fff;
    font-size: 20px;
}

.update-box {
    width: 100%;
    min-height: 80px;
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid rgb(209, 209, 209);
    margin-bottom : 10px;
    position: relative;
}

.c-image {
    width: 50px;
    height: 50px;
    margin: 0 50px;
    border-radius: 50%;
}

.c-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    margin-top:10px;
}
.profile-name-box p{
    margin-top:10px !important;
}
.update-box .type {
    font-size: 14px;
    font-weight: 700;
    color: rgb(34 177 200);
}

.update-box .update-content p:nth-child(2) {
    font-size: 12px;
    color: #4a5b8f;
}

.name-text p{
    width: 100px !important;
}

.blue-text p:nth-child(1){
    color: rgb(62, 122, 201);
}

.plug-box {
    background-color: orange !important;
}

.lock-box {
    background-color: rgb(132, 148, 163) !important;
}

.button-footer {
    margin: 0 auto;
    text-align: center;
}

.profile-name-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 15%;
}

@media screen and (max-width:1200px){
    .update-box::after{
        left: -55px;
    }
}


@media screen and (max-width:992px){
    .update-box::after{
        left: -75px;
    }
    .profile-name-box {
        width: 20%;
    }
    .name-text {
        width: 25%;
    }
}

@media screen and (max-width:768px){
    .c-image {
        margin: 0 20px;
    }
    .update-box::after{
        left: -61px;
    }
    .update-content {
        width: 50%;
    }
}

</style>
<style>
    .error {
        border: 1px solid red !important;
    }
    #staffData p{
        margin: 0px !important;
    }
</style>
<script>
            var filepath = '{{  $filepath }}';
       </script>
@endsection

