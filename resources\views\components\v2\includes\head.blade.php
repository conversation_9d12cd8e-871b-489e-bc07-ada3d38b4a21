<meta charset="UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<title>GALAXY 360 | @yield('title')</title>
<meta name="keywords" content="@yield('keywords')">
<meta name="description" content="@yield('description')">
<!-- Tell the browser to be responsive to screen width -->
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="shortcut icon" type="image/png" href="{{ versioned_asset('dist/img/galaxy360-favicon.png') }}" />

{{-- Instead of download the font use cdn so googleapi font is good here--}}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link
    href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=block"
    rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.0.6/css/all.css" rel="stylesheet">

@if(config('features.tailwind'))
{{--
<link rel="stylesheet" href="{{ asset(mix('build/css/tailwind.css')) }}" /> --}}
@vite(['resources/assets/sass/tailwind.css'])
@else
<link href="{{ versioned_asset('newdev/css/tailwind.css') }}" rel="stylesheet" />
@endif

@if(isset($optimized) && empty($optimized) && !$optimized)
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/all.min.css">
<!-- Bootstrap 3.3.6 -->

{{--
<link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}"> --}}
<link rel="stylesheet" href="{{ asset('plugins/select2/select2.min.css') }}">

{{--
<link rel="stylesheet" href="{{ asset('dist/css/AdminLTE.min.css') }}"> --}}
{{--
<link rel="stylesheet" href="{{ asset('dist/css/style.css') }}"> --}}
<link rel="stylesheet" href="{{ url('plugins/iCheck/all.css') }}">
<!-- bootstrap datepicker -->
<link rel="stylesheet" href="{{ url('plugins/datepicker/datepicker3.css') }}">

<!-- bootstrap timepicker -->
<link rel="stylesheet" href="{{ url('plugins/timepicker/bootstrap-timepicker.min.css') }}">
<link rel="stylesheet" href="{{ url('plugins/toastr/toastr.min.css') }}">

<link rel="stylesheet" href="{{ asset('dist/css/jquery.dataTables.css') }}">
<link rel="stylesheet" href="{{ url('plugins/daterangepicker/daterangepicker.css') }}">

<link rel="stylesheet" href="{{ asset('dist/css/skins/_all-skins.min.css') }}">

<link rel="stylesheet" href="{{ asset('dist/css/selectric.css') }}">
{{--
<link rel="stylesheet" href="{{ asset('dist/css/newdesigntailwind.css') }}"> --}}

@endif

<!--For Autocomplete-->

@if(empty($newKendo) || !$newKendo)
<link rel="stylesheet" href="{{ asset('v2/css/menu.css') }}">
<link rel="stylesheet" href="{{ asset('v2/css/kendo.common.min.css') }}">
@endif
{{--
<link rel="stylesheet" href="{{ asset('v2/css/kendo.default-v2.min.css') }}" /> --}}
{{--
<link rel="stylesheet" href="{{ asset('build/css/kendo-theme.css') }}"> --}}
@vite(['resources/assets/sass/kendo-theme.scss'])
{{--
<link rel="stylesheet" href="https://unpkg.com/@progress/kendo-font-icons/dist/index.css" /> --}}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@progress/kendo-font-icons@4.0.0/dist/index.min.css" />

@if (config('features.sidebar'))
@vite(['resources/assets/sass/main.scss'])
@endif
{{ $cssHeader }}

@livewireStyles