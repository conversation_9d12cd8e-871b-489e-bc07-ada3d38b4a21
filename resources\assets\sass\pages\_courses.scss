.gradientbackground {
    background: linear-gradient(270deg, #06b6d4 20.35%, #1e93ff 75.64%);
    /* margin-bottom: -3px; */
}
.active-checked.active-blue span:after {
    content: "";
    position: absolute;
    width: 1.8rem;
    height: 1.8rem;
    background-color: transparent;
    border: 2px solid #fff;
    z-index: 22;
    left: 0.5px;
    top: 0.5px;
    border-radius: 50%;
}

#courses-spa {
    // .k-checkbox::before {
    //   content: "" !important;
    // }
    // .k-checkbox:checked,
    // .k-checkbox.k-checked,
    // .k-radio:checked,
    // .k-radio.k-checked {
    //   border-color: 1px solid var(--primary-blue-500, #1890ff);
    //   background-color: var(--primary-blue-500, #1890ff);
    //   color: white;
    // }
}
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
    opacity: 0;
}

.course-pagination {
    .k-pager {
        background-color: transparent;
        border-width: 0;
        padding: 0;

        &.k-pager-md .k-pager-numbers-wrap .k-button,
        .k-pager-numbers .k-link,
        .k-pager-nav {
            min-width: 2.5rem;
            height: 2.25rem;
            background-color: var(--color-white) !important;
        }
    }

    .k-pager-nav {
        border: 1px solid var(--color-gray-500);
    }
}

#course_types {
    li {
        padding-left: 16px;
        border: 1px solid #e5e7eb;
        border-bottom: 0px;
        display: flex;
        align-items: center;
        &:first-child {
            border-radius: 5px 5px 0 0;
        }

        &:last-child {
            border-radius: 0 0 5px 5px;
            border-bottom: 1px solid #e5e7eb;
        }

        &.active-blue {
            background-color: #e6f7ff;
            // border-color: #91d5ff;
        }

        .k-radio-label {
            width: 100%;
            padding: 16px 16px 16px 12px;
            display: inline-block;
        }
    }
}

.course-checkbox {
    .k-form-field {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-direction: row-reverse;

        .k-label {
            margin-top: -0.5rem;
        }
    }
}

#courses-spa {
    .k-form .k-form-field > .k-label.font-medium {
        // font-size: var(--fs-base) !important;
    }

    .k-form .k-label,
    .k-button.k-button-md {
        font-weight: 500;
    }
}

.course-list {
    &__header {
        .k-dropdownlist {
            height: 2.25rem;
        }
    }

    &__action-menu {
        .k-animation-container {
            min-width: 175px;
            min-height: 150px;
        }
    }
}

#course_types_label {
    &.k-label {
        font-size: 1rem;
        margin-bottom: 1rem;
    }
}

.option-menu {
    &.k-dropdown-button {
        .k-button,
        .k-button:hover {
            padding: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--color-gray-200);
            background-color: white;
            &:hover {
                --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
                box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
                    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            }

            .k-font-icon {
                width: 1.25rem;
                height: 1.25rem;
                &::before {
                    color: var(--color-gray-400);
                    margin-top: 2px;
                }
            }
            .k-button-text {
                display: none;
            }
        }
    }
}

.tw-radio-group {
    li:has(> input[tabindex="0"], > input:checked) {
        background-color: var(--color-primary-blue-50);
    }
    .k-radio + .k-radio-label {
        font-weight: 500;
        color: var(--color-gray-700);
    }
}

.tw-listbox {
    .k-listbox {
        position: initial;
    }
    .k-listbox.k-listbox-toolbar-right .k-listbox-toolbar {
        margin-left: 0;
        float: none;
    }
    .k-listbox.k-listbox-actions-right .k-listbox-actions,
    .k-listbox.k-listbox-toolbar-right .k-listbox-toolbar {
        position: absolute;
        top: 0;
        right: -3rem;
    }

    .k-listbox .k-list-scroller {
        border-radius: 0.5rem;
    }

    .k-list-md .k-list-item {
        padding: 0.75rem 1.5rem;
        box-shadow: 0px -1px 0px 0px #e5e7eb inset;
    }
    .k-list {
        padding: 0;
    }

    .k-list.k-list-md
        .k-list-content
        .k-list-ul
        .k-list-item.k-selected::after {
        display: none;
    }
    .k-listbox .k-list-scroller .k-list {
        overflow-y: auto;
    }
    &__left-panel,
    &__right-panel {
        .k-listbox {
            height: auto;
            min-height: 100px;
        }
    }

    &__right-panel {
        .k-listbox {
            flex-grow: 1;
        }
    }
}

.tw-draggable-list-item {
    &.active {
        background-color: var(--color-gray-100);
    }
}

.tw-suffix-textbox {
    .k-dropdownlist.k-picker-solid {
        border: none;
        color: var(--color-gray-500);
    }
    .k-picker-solid:focus,
    .k-picker-solid.k-focus,
    .k-input-solid:focus,
    .k-input-solid.k-focus {
        box-shadow: none;
    }

    .k-picker-md .k-input-inner {
        padding-inline: 0;
    }
}

.UnitsAdded {
    & > div:nth-last-child(2) .border-b {
        border-bottom: none;
    }
}

.SubjectsAdded {
    & > div:last-child .border-b {
        border-bottom: none;
    }
}

#results_calculation_methods_label + {
    .k-radio-list-horizontal,
    .k-radio-list.k-list-horizontal {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 0.5rem;
    }
}

.k-autocomplete {
    min-height: 36px;
}

.tw-divider {
    height: 1px;
    width: 100%;
    background-color: var(--color-gray-200);
    margin-block: 1.5rem;
}

.course-table + div {
    .tw-table-alternate {
        &__row {
            &:nth-child(odd) {
                background-color: var(--color-gray-50);
            }
        }
    }
}

.tw-mark {
    &,
    & > div {
        width: 100%;
    }
}
