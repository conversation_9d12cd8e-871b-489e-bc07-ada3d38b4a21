// -----------------------------------------------------------------------------
// This file contains all application-wide Sass variables.
// -----------------------------------------------------------------------------





/// Regular font family
/// @type List
$text-font-stack: Roboto, sans-serif !default;



/// Copy text color
/// @type Color
/// 
$text-color: rgb(34, 34, 34) !default;
$colors: (
  'primary-blue-50': #e6f7ff,
  'primary-blue-100': #bae7ff,
  'primary-blue-200': #91d5ff,
  'primary-blue-300': #69c0ff,
  'primary-blue-400': #40a9ff,
  'primary-blue-500': #1890ff,
  'primary-blue-600': #096dd9,
  'primary-blue-700': #0050b3,
  'primary-blue-800': #003a8c,
  'primary-blue-900': #002766,
  
  // ### Red
  
  'red-50': #fef2f2,
  'red-100': #fee2e2,
  'red-200': #fecaca,
  'red-300': #fca5a5,
  'red-400': #f87171,
  'red-500': #ef4444,
  'red-600': #dc2626,
  'red-700': #b91c1c,
  'red-800': #991b1b,
  'red-900': #7f1d1d,
  
  // ### Neutral
  
  'white': #fff,
  
  // ### Pink
  
  'pink-50': #fdf2f8,
  'pink-100': #fce7f3,
  'pink-200': #fbcfe8,
  'pink-300': #f9a8d4,
  'pink-400': #f472b6,
  'pink-500': #ec4899,
  'pink-600': #db2777,
  'pink-700': #be185d,
  'pink-800': #9d174d,
  'pink-900': #831843,
  
  
  // ### Border Colors
  
  'border-color-text-color-tertiary': #475272,
  'border-color-smooth': #dfdfdf,
  'border-color-text-color-secondary': #252f4a,
  
  // ### Purple
  
  'purple-50': #f5f3ff,
  'purple-300': #c4b5fd,
  'purple-400': #a78bfa,
  'purple-700': #6d28d9,
  'purple-900': #4c1d95,
  'purple-800': #5b21b6,
  'purple-600': #7c3aed,
  'purple-500': #8b5cf6,
  'purple-100': #ede9fe,
  'purple-200': #ddd6fe,
  
  // ### Cyan
  
  'cyan-50': #ecfeff,
  'cyan-100': #cffafe,
  'cyan-200': #a5f3fc,
  'cyan-300': #67e8f9,
  'cyan-400': #22d3ee,
  'cyan-500': #06b6d4,
  'cyan-600': #0891b2,
  'cyan-700': #0e7490,
  'cyan-800': #155e75,
  'cyan-900': #164e63,
  
  
  // ### Light blue
  
  'light-blue-50': #f0f9ff,
  'light-blue-100': #e0f2fe,      
  'light-blue-200': #bae6fd,
  'light-blue-300': #7dd3fc,
  'light-blue-400': #38bdf8,
  'light-blue-500': #0ea5e9,
  'light-blue-600': #0284c7,
  'light-blue-900': #0c4a6e,
  'light-blue-700': #0369a1,
  'light-blue-800': #075985,
  
  // ### Indigo
  
  'indigo-50': #eef2ff,
  'indigo-100': #e0e7ff,
  'indigo-200': #c7d2fe,
  'indigo-300': #a5b4fc,
  'indigo-400': #818cf8,
  'indigo-500': #6366f1,
  'indigo-600': #4f46e5,
  'indigo-700': #4338ca,
  'indigo-800': #3730a3,
  'indigo-900': #312e81,
  
  // ### Yellow
  
  'yellow-50': #fffbeb,
  'yellow-100': #fef3c7,
  'yellow-200': #fde68a,
  'yellow-300': #fcd34d,
  'yellow-400': #fbbf24,
  'yellow-500': #f59e0b,
  'yellow-600': #d97706,
  'yellow-700': #b45309,
  'yellow-800': #92400e,
  'yellow-900': #78350f,
  
  // ### Gray
  
  'gray-5': #e0e0e0,
  'gray-50': #f9fafb,
  'gray-100': #f3f4f6,
  'gray-200': #e5e7eb,
  'gray-300': #d1d5db,
  'gray-400': #9ca3af,
  'gray-500': #6b7280,
  'gray-600': #4b5563,
  'gray-700': #374151,
  'gray-800': #1f2937,
  'gray-900': #111827,
  'color-gray-100': #fafafa,
  
  // ### Bluegray
  
  'bluegray-50': #f8fafc,
  'bluegray-100': #f1f5f9,
  'bluegray-200': #e2e8f0,
  'bluegray-300': #cbd5e1,
  'bluegray-400': #94a3b8,
  'bluegray-500': #64748b,
  'bluegray-600': #475569,
  'bluegray-700': #334155,
  'bluegray-800': #1e293b,
  'bluegray-900': #0f172a,
  
  // ### Orange
  
  'orange-50': #fff7ed,
  'orange-100': #ffedd5,
  'orange-200': #fed7aa,
  'orange-300': #fdba74,
  'orange-400': #fb923c,
  'orange-500': #f97316,
  'orange-600': #ea580c,
  'orange-700': #c2410c,
  'orange-900': #7c2d12,
  
  // ### Green
  'green-50': #ecfdf5,
  'green-100': #d1fae5,
  'green-500': #10b981,
  'green-700': #047857,
  'green-800': #065f46,
  'green-teal-500': #14b8a6,
);


/* primary color */
$brand-color: color('primary-blue-500');

/// Container's maximum width
/// @type Length
$max-width: 1180px !default;





/// Breakpoints map
/// @prop {String} keys - Keys are identifiers mapped to a given length
/// @prop {Map} values - Values are actual breakpoints expressed in pixels
$breakpoints: (
  'small': 320px,
  'medium': 768px,
  'large': 1024px,
) !default;






/// Relative or absolute URL where all assets are served from
/// @type String
/// @example scss - When using a CDN
///   $base-url: 'https://cdn.example.com/assets/';
$base-url: '/assets/' !default;
