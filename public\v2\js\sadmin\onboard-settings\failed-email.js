$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $("#loader").kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };
    $("#viewFaildEmailModal").kendoWindow(
        openCenterWindow("Failed Email Detail", 36, 10, 32),
    );

    $("#failedEmailList").kendoGrid({
        dataSource: customDataSource("api/failed-email-data", {
            sender: { type: "string" },
            receiver: { type: "string" },
            subject: { type: "string" },
            updated_at: { type: "date" },
        }),
        pageable: customPageableArr(),
        dataBound: function (e) {
            setTimeout(function () {
                setFilterIcon("#failedEmailList");
            }, 100);
        },
        filterable: false,
        sortable: true,
        resizable: true,
        columns: [
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: sender #</div>",
                field: "sender",
                title: "SENDER",
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: receiver #</div>",
                field: "receiver",
                title: "RECEIVER",
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: subject #</div>",
                field: "subject",
                title: "SUBJECT",
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(updated_at, \"dd MMM yyyy\") #</div>",
                field: "created_date",
                title: "DATE",
            },
            {
                width: 80,
                field: "action",
                title: "ACTION",
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                template: function (dataItem) {
                    // return failedEmailListManageAction(dataItem.id);
                    return manageActionColumn(dataItem.id);
                },
            },
        ],
        noRecords: noRecordTemplate(),
    });
    customGridHtml("#failedEmailList");
    initializeActionMenu("failedEmailList", "failedEmailListActionMenu");

    $("body").on("click", ".viewFaildEmailBtn", function (e) {
        e.preventDefault();

        ajaxActionV2(
            "api/failed-email-detail",
            "POST",
            { primaryId: $(this).attr("data-id") },
            function (response) {
                console.log(response);
                kendoWindowOpen("#viewFaildEmailModal");
                $("#faildEmailReason").html(
                    "<span class='text-gray-900 text-lg'>Reason:</span> " +
                        response.data.error_message,
                );
                $("#faildEmailDetail").html(
                    "<span class='text-gray-900 text-lg'>Email Detail:</span>" +
                        response.data.content,
                );
            },
        );
    });
    $("#failedEmailList").kendoTooltip({
        filter: "td .action-only",
        position: "bottom-left",
        // showOn: "click",
        content: function (e) {
            let dataItem = $("#failedEmailList")
                .data("kendoGrid")
                .dataItem(e.target.closest("tr"));
            return kendo.template($("#failedEmailActionTemplate").html())({
                id: dataItem.id,
            });
        },
    });
});

$("#viewFaildEmailModal").kendoWindow(openCenterWindow("Failed Email Detail"));

addModalClassToWindows(["#viewFaildEmailModal"]);

function failedEmailListManageAction(id, content) {
    return (
        '<div class="actionButtonHide action-only action-div flex justify-start items-center space-x-1">' +
        '<div class="action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer"  data-id = "' +
        id +
        '"><span class="k-icon k-i-more-horizontal"></span></div>' +
        "</div>"
    );
}

function openCenterWindow(titleText, widthVal = 34, topVal = 25, leftVal = 33) {
    return {
        title: titleText,
        width: widthVal + "%",
        // height: "70%",
        actions: ["close"],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: topVal + "%",
            left: leftVal + "%",
        },
        animation: defaultCloseAnimation(),
    };
}
function kendoWindowOpen(windowID) {
    let kendoWindow = $(document).find(windowID);
    console.log(kendoWindow);
    kendoWindow.getKendoWindow().open();
    kendoWindow
        .parent("div")
        .find(".k-window-titlebar")
        .addClass(
            "titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500",
        )
        .find(".k-window-title")
        .addClass("text-lg font-medium leading-normal text-white");
}
