<?php

namespace App\Http\Controllers\v2\api;

use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\v2\CampusVenue;
use App\Model\v2\ClassDefaultTimetable;
use App\Model\v2\Classroom;
use App\Model\v2\CollegeCampus;
use App\Model\v2\Semester;
use App\Model\v2\SemesterDivision;
use App\Model\v2\Staff;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\SubjectMaterial;
use App\Model\v2\TeacherMatrix;
use App\Model\v2\Timetable;
use App\Model\v2\TimetableBreaktime;
use App\Model\v2\TimetableDetail;
use App\Repositories\TimetableRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Session;

class TimeTableApiController extends BaseController
{
    use CommonTrait;
    use ResponseTrait;

    /**
     * Get session data with cache fallback
     *
     * @param  string  $key
     * @return mixed
     */
    private function getSessionData(Request $request, $key)
    {
        // Try to get from session first
        // $sessionData = $request->session()->get($key);

        // if ($sessionData !== null) {
        //     return $sessionData;
        // }

        // Fallback to cache if session fails
        $cacheKey = $key.'_'.auth()->id();
        $cacheData = Cache::get($cacheKey);

        if ($cacheData !== null) {
            // Also store back in session for future requests
            $request->session()->put($key, $cacheData);
            $request->session()->save();

            return $cacheData;
        }

        return null;
    }

    /**
     * Store session data with cache backup
     *
     * @param  string  $key
     * @param  mixed  $data
     * @return void
     */
    private function storeSessionData(Request $request, $key, $data)
    {
        // Store in session
        $request->session()->put($key, $data);
        $request->session()->save();

        // Also store in cache as backup (1 hour expiry)
        $cacheKey = $key.'_'.auth()->id();
        Cache::put($cacheKey, $data, 3600);
    }

    protected $timeTable;

    protected $timeTableDetail;

    protected $semester;

    protected $semesterDivision;

    protected $timeTableBreakTime;

    protected $classDefaultTimetable;

    protected $studentSubjectEnrolment;

    protected $campus;

    protected $staff;

    public function __construct(
        Timetable $timeTable,
        TimetableDetail $timeTableDetail,
        Semester $semester,
        SemesterDivision $semesterDivision,
        TimetableBreaktime $timeTableBreakTime,
        ClassDefaultTimetable $classDefaultTimetable,
        StudentSubjectEnrolment $studentSubjectEnrolment,
        CollegeCampus $campus,
        Staff $staff
    ) {
        $this->timeTable = new TimetableRepository($timeTable);
        $this->timeTableDetail = new TimetableRepository($timeTableDetail);
        $this->semester = new TimetableRepository($semester);
        $this->semesterDivision = new TimetableRepository($semesterDivision);
        $this->timeTableBreakTime = new TimetableRepository($timeTableBreakTime);
        $this->classDefaultTimetable = new TimetableRepository($classDefaultTimetable);
        $this->studentSubjectEnrolment = new TimetableRepository($studentSubjectEnrolment);
        $this->campus = new TimetableRepository($campus);
        $this->staff = new TimetableRepository($staff);
    }

    public function attendanceData(Request $request)
    {
        $allAttendanceData = $this->timeTable->getAttendanceData($request);
        $data['total'] = $this->timeTable->getAttendanceData($request, true);
        $finalData = [];
        foreach ($allAttendanceData as $att) {
            $getStudentsByBatch = $this->studentSubjectEnrolment->getStudentsOfBatchNew($att['subject_id'], $att['semester_id'], $att['term'], $att['batch']);
            $att['class_capacity'] = $getStudentsByBatch;
            $finalData[] = $att;
        }
        $data['data'] = $finalData;

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTimetableDashboard(Request $request)
    {
        // $isRedisConnection = Helpers::isRedisReady();
        // if($isRedisConnection){
        $cacheKeySuffixText = Config::get('constants.cacheKeySuffix.timetableDashboard');
        $tenantId = tenant('id');
        $cacheKey = $tenantId.$cacheKeySuffixText;

        $cacheData = Cache::get($cacheKey);
        if ($cacheData === null || empty(json_decode($cacheData))) {
            $this->updateTimeTableDashboardCacheData();
            $data = json_encode($this->timeTableDetail->getRoomInfoV2($request));
        } else {
            $filterArray = ($request->input()) ? $request->input() : [];
            $postFilter['timetable_date'] = $timetable_date = $filterArray['date'];
            $postFilter['campus_id'] = $campus_id = $filterArray['campus_id'];
            $teacher_ids = ! empty($filterArray['teacher_ids']) ? $filterArray['teacher_ids'] : '';
            $collegeId = Auth::user()->college_id;
            // Get ALl Room List
            $getAllRoomList = CampusVenue::from('rto_venue as rtr')
                ->join('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id')
                ->select('rtc.id as room_id', 'rtc.room_name')
                ->where('rtr.campus_id', '=', $campus_id)
                ->where('rtr.college_id', '=', $collegeId)
                ->get()->toArray();
            // Filter Data
            $arrData = json_decode($cacheData, true);
            $search = ['campus_id' => $campus_id, 'timetable_date' => $timetable_date];
            $filterArrayData = array_values(
                array_filter($arrData, function ($v) use ($search) {
                    return $v['campus_id'] == $search['campus_id'] &&
                    ($v['timetable_date'] == $search['timetable_date']);
                }
                )
            );
            if ($teacher_ids != 'All' && ! empty($filterArray['teacher_ids'])) {
                $teacherIdArray = $filterArray['teacher_ids'];
                $filterArrayData = array_filter($filterArrayData, function ($number) use ($teacherIdArray) {
                    return in_array($number['teacher_id'], $teacherIdArray);
                });
            }
            // For Day View
            $i = 0;
            $roomDetailsRoomId = [];
            foreach ($filterArrayData as $rd) {
                $roomDetailsRoomId[$rd['room_id']][] = $rd;
            }
            $roomDetailsRoomIdFinalArray = [];
            foreach ($filterArrayData as $fad) {
                if (! in_array($fad['room_name'], array_column($roomDetailsRoomIdFinalArray, 'room'))) {
                    $roomDetailsRoomIdFinalArray[$i]['room'] = $fad['room_name'];
                    $roomDetailsRoomIdFinalArray[$i]['roomdetails'] = array_key_exists($fad['room_id'], $roomDetailsRoomId) ? $roomDetailsRoomId[$fad['room_id']] : [];
                    $i++;
                } else {
                    // $roomDetailsRoomIdFinalArray[$i]['room'] = $fad['room_name'];
                    // $roomDetailsRoomIdFinalArray[$i]['roomdetails'] = [];
                    // $i++;
                }
            }

            $dayFinalArray = [];
            $f = 0;
            foreach ($getAllRoomList as $rdf) {
                if (! empty($roomDetailsRoomIdFinalArray)) {
                    foreach ($roomDetailsRoomIdFinalArray as $r) {

                        if ($rdf['room_name'] == $r['room']) {

                            $dayFinalArray[$f]['room'] = $rdf['room_name'];
                            $dayFinalArray[$f]['roomdetails'] = $r['roomdetails'];
                        } else {

                            if (isset($dayFinalArray[$f]['roomdetails']) && count($dayFinalArray[$f]['roomdetails']) > 0) {
                                //
                            } else {
                                $dayFinalArray[$f]['room'] = $rdf['room_name'];
                                $dayFinalArray[$f]['roomdetails'] = [];
                            }
                        }

                    }
                } else {
                    $dayFinalArray[$f]['room'] = $rdf['room_name'];
                    $dayFinalArray[$f]['roomdetails'] = [];
                }

                $f++;
            }

            // Week Start
            // Filter Data
            $weekDetails = [];
            $first_day_of_week = date('Y-m-d', strtotime('monday this week', strtotime($timetable_date)));
            $last_day_of_week = date('Y-m-d', strtotime('sunday this week', strtotime($timetable_date)));
            $dateRangeList = $this->getDateRangeArr($first_day_of_week, $last_day_of_week);
            $search = ['campus_id' => $campus_id, 'timetable_date_start' => $first_day_of_week, 'timetable_date_end' => $last_day_of_week];
            $filterArrayDataWeek = array_values(
                array_filter($arrData, function ($v) use ($search) {
                    return $v['campus_id'] == $search['campus_id'] &&
                    ($v['timetable_date'] >= $search['timetable_date_start'] && $v['timetable_date'] <= $search['timetable_date_end']);
                }
                )
            );
            if ($teacher_ids != 'All' && ! empty($filterArray['teacher_ids'])) {
                $teacherIdArray = $filterArray['teacher_ids'];
                $filterArrayDataWeek = array_filter($filterArrayDataWeek, function ($number) use ($teacherIdArray) {
                    return in_array($number['teacher_id'], $teacherIdArray);
                });
            }
            foreach ($filterArrayDataWeek as $rd) {
                $weekDetails[$rd['room_name']][] = $rd;
            }
            $otherRoomWeek = [];
            foreach ($getAllRoomList as $rdf) {
                if (! array_key_exists($rdf['room_name'], $weekDetails)) {
                    $otherRoomWeek[$rdf['room_name']] = [];
                } else {
                    $otherRoomWeek[$rdf['room_name']] = $weekDetails[$rdf['room_name']];
                }
            }

            // dd($otherRoomWeek);
            // $finalArrWeek = array_merge($otherRoomWeek,$weekDetails);
            $finalArrWeek = $otherRoomWeek;
            $wdsDetail = [];
            foreach ($finalArrWeek as $kr => $wds) {
                if (empty($wds)) {
                    foreach ($dateRangeList as $weekDate) {
                        $wdsDetail[$kr][$weekDate] = [];
                    }
                }
                foreach ($wds as $dws) {
                    foreach ($dateRangeList as $weekDate) {
                        if ($dws['timetable_date'] != $weekDate) {
                            if (isset($wdsDetail[$dws['room_name']][$weekDate]) && count($wdsDetail[$dws['room_name']][$weekDate]) > 0) {
                            } else {
                                $wdsDetail[$dws['room_name']][$weekDate] = [];
                            }
                        } else {
                            $wdsDetail[$dws['room_name']][$weekDate][] = $dws;
                        }
                    }
                }
            }

            $p = 0;
            $l = 0;
            $weekFinalDetailArray = [];
            foreach ($wdsDetail as $k1 => $w1) {
                foreach ($w1 as $k2 => $w2) {
                    $weekFinalDetailArray[$l]['week'][$p]['room'] = $k1;
                    $weekFinalDetailArray[$l]['week'][$p]['date'] = $k2;
                    $weekFinalDetailArray[$l]['week'][$p]['weekdetails'] = $w2;
                    $p++;
                }
                $p = 0;
                $l++;
            }
            // Week View End
            $finalArr['dayView'] = $dayFinalArray;
            $finalArr['weekView'] = $weekFinalDetailArray;
            $data = json_encode($finalArr);
        }

        // }else{
        //     $data = json_encode($this->timeTableDetail->getRoomInfoV2($request));
        // }
        return $this->successResponse('Timetable Dashboard data found successfully', 'dashboarddata', json_decode($data));
    }

    public function dashboardFilter($post)
    {
        $checkStudentRedis = Redis::get(tenant('id').'.timetable_dashboard_data');
        $arrStudentRedis = json_decode($checkStudentRedis, true);
        $arrayFilter = $arrData = $arrStudentRedis;
        $outputData = [];
        if (isset($post) && Arr::accessible($post)) {
            foreach ($post as $key => $value) {
                $dataArrOr[] = $this->findWhere($arrData, [$key => trim($value)]);
            }
            $arrData = Arr::collapse($dataArrOr);
        }
        if (! empty($outputData)) {
            $arrData = Arr::collapse($outputData);
        }
        $dataArr = array_unique($arrData, SORT_REGULAR);
        // foreach ($dataArr as $key=>$student){
        //     $dataArr[$key]['profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_picture']);
        // }
        $data['data'] = $dataArr;
        $data['total'] = count($dataArr);
        unset($dataArr, $arrStudentRedis);

        return $data;
    }

    public function getDateRangeArr($first, $last, $step = '+1 day', $format = 'Y-m-d')
    {
        $dates = [];
        $current = strtotime($first);
        $last = strtotime($last);
        while ($current <= $last) {
            $dates[] = date($format, $current);
            $current = strtotime($step, $current);
        }

        return $dates;
    }

    public function findWhere($array, $matching)
    {
        $dataReturn = [];
        foreach ($array as $item) {
            $is_match = true;
            foreach ($matching as $key => $value) {
                if (is_object($item)) {
                    if (! isset($item->$key)) {
                        $is_match = false;
                        break;
                    }
                } else {
                    if (! isset($item[$key])) {
                        $is_match = false;
                        break;
                    }
                }
                if (is_object($item)) {
                    if ($value != $item->$key) {
                        $is_match = false;
                        break;
                    }
                } else {
                    if ($item[$key] != $value) {
                        $is_match = false;
                        break;
                    }
                }
            }
            if ($is_match) {
                $dataReturn[] = $item;
            }
        }

        return $dataReturn;
    }

    public function updateTimeTableDashboardCacheData()
    {
        $timeTableDashboardData = [
            'college_id' => Auth::user()->college_id,
            'tenant_id' => tenant('id'),
        ];
        event(new \App\Events\TimeTableDashboardEvent($timeTableDashboardData));
    }

    public function trainerFullName(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'is_active' => 1,
        ];
        $staffData = $this->staff->Where($whereArr)->select(DB::raw("CONCAT(first_name,' ',last_name) as trainer_name"))->get()->toarray();
        $data = [];
        foreach ($staffData as $staffName) {
            $data[] = $staffName['trainer_name'];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getTeacherFilter(Request $request)
    {
        if ($request->input('id')) {
            $data = $this->timeTableDetail->getTeacherFilterList($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->getTeacherFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCalenderType(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'course_type_id' => $request->course_type,
        ];
        $calendarType = $this->semester->Where($whereArr)->groupBy('calendar_type')->get(['calendar_type as calendar', 'calendar_type']);
        $arrCalendarType = Config::get('constants.arrCalendarType');

        // dd($calendarType,$arrCalendarType);
        if (count($calendarType) > 0) {
            $data = [];
            foreach ($calendarType as $key => $value) {
                // $data[] = ['value'=> $calendarType[$key]['calendar'] , 'text' => $arrCalendarType[$calendarType[$key]['calendar_type']]];
                if (isset($calendarType[$key]['calendar'], $calendarType[$key]['calendar_type'])) {
                    $data[] = [
                        'value' => $calendarType[$key]['calendar'],
                        'text' => $arrCalendarType[$calendarType[$key]['calendar_type']] ?? '',
                    ];
                }
            }
        } else {
            $data[] = ['value' => '0', 'text' => 'No Calendar Type Found'];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getYearFromCalenderType(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'course_type_id' => $request->course_type,
            'calendar_type' => $request->calendar_id,
        ];
        $data = $this->semester->Where($whereArr)->select('year as value', 'year as text')->orderBy('year', 'DESC')->groupBy('year')->get()->toarray();

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getSubjectList(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'course_type_id' => $request->course_type,
            'calendar_type' => $request->calendar_id,
            'course_id' => $request->course_id,
        ];
        $data = $this->semester->getSubjectListv2($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getTimetableSemester(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'course_type_id' => $request->course_type_id,
            'calendar_type' => $request->calendar_type,
            'year' => $request->year,
        ];
        $data = $this->semester->Where($whereArr)->select('id as value', 'semester_name as text')->get()->toarray();

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getDefaultTimes(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'course_type_id' => $request->course_type,
        ];
        $defaultTimetables = $this->classDefaultTimetable->Where($whereArr)->get()->toArray();
        if (! empty($defaultTimetables) && $defaultTimetables != '') {
            foreach ($defaultTimetables as $defaultTimetable) {
                $data[] = ['value' => date('h:i A', strtotime($defaultTimetable['start_time'])).' - '.date('h:i A', strtotime($defaultTimetable['finish_time'])),
                    'text' => date('h:i A', strtotime($defaultTimetable['start_time'])).' - '.date('h:i A', strtotime($defaultTimetable['finish_time']))];
            }
        } else {
            $data[] = ['value' => 'not_available', 'text' => 'Default Timetable Data Not Found'];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function weekStartByTerm(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'semester_id' => $request->semester_id,
            'term' => $request->term,
        ];
        $weekLists = $this->semesterDivision->Where($whereArr)->get();
        if (count($weekLists) > 0) {
            foreach ($weekLists as $weekList) {
                $newStartDate = date('d-m-Y', strtotime($weekList['week_start']));
                $data[] = ['is_holiday' => $weekList['is_holiday'], 'value' => $weekList['week_period'].'/'.$weekList['week_start'], 'text' => '(WeekID:'.$weekList['week_period'].')'.$newStartDate.''];
            }
        } else {
            $data[] = ['value' => '0', 'text' => 'No Start Week Record Found'];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function weekEndByTerm(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'semester_id' => $request->semester_id,
            'term' => $request->term,
        ];
        $weekStartDate = explode('/', $request->weekStartDate);
        if (empty($weekStartDate) || ! isset($weekStartDate[1]) || $weekStartDate[1] == '') {
            $weekStartDate[1] = date('Y-m-d');
        }
        $weekLists = $this->semesterDivision->Where($whereArr)->where('week_finish', '>', $weekStartDate[1])->get();
        if (count($weekLists) > 0) {
            foreach ($weekLists as $weekList) {
                $newFinishDate = date('d-m-Y', strtotime($weekList['week_finish']));
                $data[] = ['is_holiday' => $weekList['is_holiday'], 'value' => $weekList['week_period'].'/'.$weekList['week_finish'], 'text' => '(WeekID:'.$weekList['week_period'].')'.$newFinishDate.''];
            }
        } else {
            $data[] = ['value' => '0', 'text' => 'No End Week Record Found'];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    private function getTeacherFilterCategory()
    {
        return [
            ['id' => 1, 'hasChild' => true, 'text' => 'Search Trainer', 'expanded' => true],
        ];
    }

    public function viewAllTimetableData(Request $request)
    {
        $allTimeTableData = $this->timeTable->viewAllTimetableData($request);
        $data['total'] = $this->timeTable->viewAllTimetableData($request, true);
        $finalData = [];
        foreach ($allTimeTableData as $att) {
            $getStudentsByBatch = $this->studentSubjectEnrolment->getStudentsOfBatchNew($att['subject_id'], $att['semester_id'], $att['term'], $att['batch']);
            $att['class_capacity'] = $getStudentsByBatch;
            $finalData[] = $att;
        }
        $data['data'] = $finalData;

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAllBatchForFilter(Request $request)
    {
        $allTimeTableData = $this->timeTable->viewAllTimetableData($request);
        $data['total'] = $this->timeTable->viewAllTimetableData($request, true);
        $finalData = [];
        foreach ($allTimeTableData as $att) {
            $getStudentsByBatch = $this->studentSubjectEnrolment->getStudentsOfBatchNew($att['subject_id'], $att['semester_id'], $att['term'], $att['batch']);
            $att['class_capacity'] = $getStudentsByBatch;
            $finalData[] = $att;
        }
        $data['data'] = $finalData;

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function timetableDefaultData(Request $request)
    {
        $data['data'] = $this->classDefaultTimetable->timetableDefaultData($request);
        $data['total'] = $this->classDefaultTimetable->timetableDefaultData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function attendanceFilterHtmlData(Request $request)
    {
        if ($request->input('id')) {
            $data = $this->timeTable->attendanceFilterHtmlData($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->getAttendanceFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function viewAllTimeTableBatch(Request $request)
    {
        $data = $this->timeTable->viewAllTimeTableBatch();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function viewAllTimeTableUnit(Request $request)
    {
        $data = $this->timeTable->viewAllTimeTableUnit();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function viewAllTimeTableLocation(Request $request)
    {
        $data = $this->timeTable->viewAllTimeTableLocation();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceCampus(Request $request)
    {
        $whereArr = ['college_id' => $request->user()->college_id, 'status' => '1'];
        $data = $this->timeTable->printAttendanceCampus($whereArr);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceSemester(Request $request)
    {
        $data = $this->timeTable->printAttendanceSemester();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceSubject(Request $request)
    {
        $data = $this->timeTable->printAttendanceSubject();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceBatch(Request $request)
    {
        $data = $this->timeTable->printAttendanceBatch();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceClassRoom(Request $request)
    {
        $data = $this->timeTable->printAttendanceClassRoom();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceStartWeek(Request $request)
    {
        $data = $this->timeTable->printAttendanceStartWeek();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceEndWeek(Request $request)
    {
        $data = $this->timeTable->printAttendanceEndWeek();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceCourseType(Request $request)
    {
        $data = $this->timeTable->printAttendanceCourseType($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function printAttendanceTrainer(Request $request)
    {
        $data = $this->timeTable->printAttendanceTrainer($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function calendarViewHtmlData(Request $request)
    {
        if ($request->input('id')) {
            $data = $this->timeTable->calendarViewHtmlData($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->calendarViewFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    private function calendarViewFilterCategory()
    {
        return [
            ['id' => 1, 'hasChild' => true, 'text' => 'Rooms',           'expanded' => true],
            ['id' => 2, 'hasChild' => true, 'text' => 'Trainer',         'expanded' => true],
        ];
    }

    private function getAttendanceFilterCategory()
    {
        return [
            ['id' => 1, 'hasChild' => true, 'text' => 'Select Campus',  'expanded' => true],
            ['id' => 2, 'hasChild' => true, 'text' => 'Course Type',    'expanded' => true],
            ['id' => 3, 'hasChild' => true, 'text' => 'Trainer',        'expanded' => true],
            ['id' => 4, 'hasChild' => true, 'text' => 'Course By',      'expanded' => true],
            ['id' => 5, 'hasChild' => true, 'text' => 'Week Range',     'expanded' => true],
        ];
    }

    public function showTeacherBySubject(Request $request)
    {
        $getCousreAndUnitDetails = $this->getSessionData($request, 'course_and_unit');

        // Validate session data and required keys
        if (! $getCousreAndUnitDetails || ! isset($getCousreAndUnitDetails['subject_id'])) {
            return $this->errorResponse('Session data not found or subject_id missing. Please start the timetable creation process again.');
        }

        $subjectId = $getCousreAndUnitDetails['subject_id'];
        if ($request->input('id')) {
            $data = $this->timeTable->showTeacherBySubject($request, $subjectId);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->showTeacherBySubjectList();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    private function showTeacherBySubjectList()
    {
        return [
            ['id' => 1, 'hasChild' => true, 'text' => 'Showing Trainers For Selected Unit', 'expanded' => true],
        ];
    }

    public function viewTimetableFilterHtmlData(Request $request)
    {
        if ($request->input('id')) {
            $data = $this->timeTable->viewTimetableFilterHtmlData($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->viewTimetableFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseSubjectList(Request $request)
    {
        $data = $this->timeTable->getCourseSubjectList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    private function viewTimetableFilterCategory()
    {
        return [
            ['id' => 1, 'hasChild' => true, 'expanded' => true, 'text' => 'Batch Start-End Date'],
            ['id' => 2, 'hasChild' => true, 'expanded' => true, 'text' => 'Location'],
            ['id' => 3, 'hasChild' => true, 'expanded' => true, 'text' => 'Trainer'],
            ['id' => 4, 'hasChild' => true, 'expanded' => false, 'text' => 'Unit'],
            ['id' => 5, 'hasChild' => true, 'expanded' => false, 'text' => 'Batch'],
        ];
    }

    public function getBreakTimeRange(Request $request)
    {
        $getTimetable = $this->timeTable->getData(['college_id' => $request->user()->college_id, 'id' => $request->timetable_id], ['start_time', 'finish_time']);
        if ($request->break_from) {
            $starttime = $request->break_from;
            $endtime = $getTimetable[0]['finish_time'];
        } elseif ($request->default_start_time) {
            $starttime = date('H:i', strtotime($request->default_start_time));
            $endtime = date('H:i', strtotime($request->default_finish_time));
        } elseif ($request->default_break_start) {
            $starttime = $request->default_break_start;
            $endtime = date('H:i', strtotime($request->default_finish_time));
        } elseif ($request->default_break_start_2) {
            $starttime = $request->default_break_start_2;
            $endtime = date('H:i', strtotime($request->default_finish_time));
        } else {
            $starttime = $getTimetable[0]['start_time'];
            $endtime = $getTimetable[0]['finish_time'];
        }
        $duration = '15';
        $start_time = strtotime($starttime);
        $end_time = strtotime($endtime);
        $add_mins = $duration * 60;
        $i = 0;
        while ($start_time <= $end_time) {
            $data[] = ['value' => date('H:i', $start_time), 'text' => date('H:i', $start_time)];
            $start_time += $add_mins;
        }
        $i++;

        // dd($data);
        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherList(Request $request)
    {
        $data = $this->timeTable->getReplacementTeacherList($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherYear(Request $request)
    {
        $data = $this->timeTable->Where(['college_id' => $request->user()->college_id])->whereNotNull('year')->groupBy('year')->orderBy('id', 'DESC')->select('year as value', 'year as text')->get()->toArray();

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherCalenderType(Request $request)
    {
        $calendarType = $this->timeTable->Where(['college_id' => $request->user()->college_id])->groupBy('calendar_id')->get('calendar_id as calendar', 'calendar_id');
        $arrCalendarType = Config::get('constants.arrCalendarType');
        if (count($calendarType) > 0) {
            foreach ($calendarType as $key => $value) {
                $data[] = ['value' => $calendarType[$key]['calendar'], 'text' => $arrCalendarType[$calendarType[$key]['calendar']]];
            }
        } else {
            $data = [];
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherSemester(Request $request)
    {
        $data = $this->timeTable->getReplacementTeacherSemester($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherTerm(Request $request)
    {
        $data = $this->timeTable->Where(['college_id' => $request->user()->college_id, 'semester_id' => $request->semester_id])->groupBy('term')->select('term as value', 'term as text')->get()->toArray();

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherSubject(Request $request)
    {
        $data = $this->timeTable->getReplacementTeacherSubject($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementNewTeacherList(Request $request)
    {
        $data = $this->timeTable->getReplacementNewTeacherList($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherBatch(Request $request)
    {
        // semester term year
        $data = $this->timeTable->Where(['college_id' => $request->user()->college_id, 'subject_id' => $request->subject_id, 'teacher_id' => $request->teacher_id,
            'semester_id' => $request->semester_id, 'term' => $request->term, 'year' => $request->year])->groupBy('batch')->select('batch as value', 'batch as text')->get()->toArray();

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function replacementTeacherClassRoom(Request $request)
    {
        // semester term year
        $data = $this->timeTable->getReplacementClassRoom($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getTimetableStatusData(Request $request)
    {
        $data = $request->input('data');
        $getCousreAndUnitDetails = $this->getSessionData($request, 'course_and_unit');
        if (isset($getCousreAndUnitDetails) && $data['flag'] == 'true') {
            if (isset($getCousreAndUnitDetails['week_start'])) {
                $startWeek = $getCousreAndUnitDetails['week_start'];
                $startWeek = explode('/', $startWeek);
                $startDate = date('d M Y', strtotime($startWeek[1]));
                $endWeek = $getCousreAndUnitDetails['week_end'];
                $endWeek = explode('/', $endWeek);
                $EndDate = date('d M Y', strtotime($endWeek[1]));
                $startTime = $getCousreAndUnitDetails['start_time'];
                $endTime = $getCousreAndUnitDetails['end_time'];
            }
            $subjectName = (isset($getCousreAndUnitDetails['subject_name'])) ? substr($getCousreAndUnitDetails['subject_name'], 0, 30).'..' : 'Not Selected';
            $Dates = (isset($getCousreAndUnitDetails['week_start'])) ? $startDate.' - '.$EndDate.', '.$startTime.'-'.$endTime : 'Not Selected';
            $trainers = (isset($getCousreAndUnitDetails['teacher_name'])) ? $getCousreAndUnitDetails['teacher_name'] : 'Not Selected';
            $studentBatch = (isset($getCousreAndUnitDetails['new_batch'])) ? $getCousreAndUnitDetails['new_batch'] : 'Not Selected';
            $finalArr = [
                [
                    'status' => $subjectName,
                ],
                [
                    'status' => $Dates,
                ],
                [
                    'status' => $trainers,
                ],
                [
                    'status' => $studentBatch,
                ],
                [
                    'status' => '',
                ],
            ];
        } else {
            $finalArr = [[
                'status' => 'Not Selected',
            ],
                [
                    'status' => 'Not Selected',
                ],
                [
                    'status' => 'Not Selected',
                ],
                [
                    'status' => 'Not Selected',
                ],
                [
                    'status' => '',
                ]];
        }

        return $this->successResponse('Data found successfully', 'data', $finalArr);
    }

    public function getTimetableBatch(Request $request)
    {
        $getCousreAndUnitDetails = $this->getSessionData($request, 'course_and_unit');

        // Validate session data and required keys
        if (! $getCousreAndUnitDetails || ! isset($getCousreAndUnitDetails['subject_id']) || ! isset($getCousreAndUnitDetails['semester_id'])) {
            return $this->errorResponse('Session data not found or required fields missing. Please start the timetable creation process again.');
        }

        $whereArr = [
            'college_id' => $request->user()->college_id,
            'subject_id' => $getCousreAndUnitDetails['subject_id'],
            'semester_id' => $getCousreAndUnitDetails['semester_id'],
        ];
        $data = $this->timeTable->Where($whereArr)->select('batch as value', 'batch as text')->get()->toarray();

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getAssessorList(Request $request)
    {
        $getCousreAndUnitDetails = $this->getSessionData($request, 'course_and_unit');

        // Validate session data and required keys
        if (! $getCousreAndUnitDetails || ! isset($getCousreAndUnitDetails['subject_id'])) {
            return $this->errorResponse('Session data not found or subject_id missing. Please start the timetable creation process again.');
        }

        $subjectId = $getCousreAndUnitDetails['subject_id'];
        $data = $this->timeTable->getAssessorList($request, $subjectId);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function deleteTimetableData(Request $request)
    {

        $collegeId = $request->user()->college_id;
        $timetableArr = [
            'college_id' => $request->user()->college_id,
            'id' => $request->id,
        ];
        $getTimetableBatchDetail = $this->timeTable->Where($timetableArr)->get()->toArray();
        $batchArr = [
            'college_id' => $request->user()->college_id,
            'batch' => $getTimetableBatchDetail[0]['batch'],
        ];
        $enrolmentDetail = $this->studentSubjectEnrolment->Where($batchArr)->get()->toArray();
        if (isset($enrolmentDetail) && empty($enrolmentDetail)) {
            $deleteBreak = TimetableBreaktime::where('time_table', $request->id)->where('college_id', $collegeId)->delete();
            $deleteTimetable = Timetable::where('id', $request->id)->where('college_id', $collegeId)->delete();
            $getDashboardData = TimetableDetail::where('timetable_id', $request->id)->delete();
            $this->updateTimeTableDashboardCacheData();
            $result = ['status' => 'success', 'message' => 'Timetable Details Delete Successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'This Batch is enroll in some student.'];
        }

        return $this->successResponse('data found successfully', 'data', $result);
    }

    public function deleteDefaultTimetableData(Request $request)
    {
        $data = $this->classDefaultTimetable->delete($request->id);

        return $this->successResponse('Data Deleted Successfully', 'data', $data);
    }

    public function timetableCampusList(Request $request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'status' => '1',
        ];
        // $dataadd = array(['value' => "all",'text' => 'All Campus']);
        $data = $this->campus->Where($whereArr)->get(['id as value', 'name as text'])->toArray();

        // return $this->successResponse('Data found successfully','data',array_merge($dataadd, $data));
        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        $data = $request->input('data');
        switch ($action) {
            case 'store_course_and_unit':
                $this->storeSessionData($request, 'course_and_unit', $data);
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                $result = ['status' => 'success', 'message' => 'Course And Subject Save Successfully'];
                echo json_encode($result);
                exit;
            case 'store_course_and_unit_date':
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                if ($data['default_timetable'] == 'Yes') {
                    $time = explode('-', $data['default_time']);
                    $data['start_time'] = $time[0];
                    $data['end_time'] = $time[1];
                }
                $finalArray = array_merge($oldSessionArray, $data);
                $this->storeSessionData($request, 'course_and_unit', $finalArray);
                $roomAvibilty = $this->getAvailableRoom($finalArray);
                $result = ['status' => 'success', 'message' => 'Date And Time Save Sucessfully', $roomAvibilty];
                echo json_encode($result);
                exit;
            case 'store_teachers':
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                $finalArray = array_merge($oldSessionArray, $data);
                $this->storeSessionData($request, 'course_and_unit', $finalArray);
                $teachersAvibility = $this->getAvailableTeacher($finalArray);
                $result = ['status' => 'success', 'message' => 'Trainer Save Sucessfully', $teachersAvibility];
                echo json_encode($result);
                exit;
            case 'save_class_break_time':
                $this->saveClassBreakTime($data);
                break;
            case 'save_default_time':
                $this->saveDefaultTime($data);
                break;
            case 'save_replacement_teacher':
                $this->saveReplacementTeacher($data);
                break;
            case 'save_room_and_get_details':
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                if (! $oldSessionArray) {
                    $result = ['status' => 'error', 'message' => 'Session data not found. Please start the timetable creation process again.'];
                    echo json_encode($result);
                    exit;
                }
                $finalArray = array_merge($oldSessionArray, $data);
                $this->storeSessionData($request, 'course_and_unit', $finalArray);
                $roomDetails = $this->getRoomDetails($request, $data);
                $result = ['status' => 'success', 'message' => 'Session Data Store Sucessfully', $roomDetails];
                echo json_encode($result);
                exit;
            case 'save_teacher_and_get_details':
                $roomDetails = $this->getTeacherDetails($request, $data);
                $result = ['status' => 'success', 'message' => 'Session Data Store Sucessfully', $roomDetails];
                echo json_encode($result);
                exit;
            case 'get_random_batch_name':
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                if (! $oldSessionArray) {
                    $result = ['status' => 'error', 'message' => 'Session data not found. Please start the timetable creation process again.'];
                    echo json_encode($result);
                    exit;
                }
                // dd($oldSessionArray);
                // Validate required keys for batch name generation
                $requiredKeys = ['subject_name', 'teacher_name', 'year_text'];
                $missingKeys = [];
                foreach ($requiredKeys as $key) {
                    if (! isset($oldSessionArray[$key])) {
                        $missingKeys[] = $key;
                    }
                }

                if (! empty($missingKeys)) {
                    $result = ['status' => 'error', 'message' => 'Required session data missing: '.implode(', ', $missingKeys).'. Please complete the timetable creation process.'];
                    echo json_encode($result);
                    exit;
                }

                $subjectName = explode(':', $oldSessionArray['subject_name']);
                $teacherFullName = explode('.', $oldSessionArray['teacher_name']);
                $teacherName = isset($teacherFullName[1]) ? str_replace(' ', '_', $teacherFullName[1]) : 'Teacher';
                $batchName = $oldSessionArray['year_text'].'_'.$subjectName[0].''.$teacherName;
                /*$whereArr = [
                    'college_id' => $request->user()->college_id,
                    'batch' => $batchName
                ];
                $getBatchCount = $this->timeTable->Where($whereArr)->groupBy('batch')->get()->count();*/

                $whereArr = ['college_id' => $request->user()->college_id];
                $getBatchCount = $this->timeTable->Where($whereArr)->where('batch', 'like', "%$batchName%")->groupBy('batch')->count();

                if ($getBatchCount == 0) {
                    $randomBatch = $batchName;
                } else {
                    $randomBatch = $batchName.'_'.$getBatchCount;
                }
                echo json_encode($randomBatch);
                exit;
            case 'save_student_batch':
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                if (! $oldSessionArray) {
                    $result = ['status' => 'error', 'message' => 'Session data not found. Please start the timetable creation process again.'];
                    echo json_encode($result);
                    exit;
                }
                $finalArray = array_merge($oldSessionArray, $data);
                $this->storeSessionData($request, 'course_and_unit', $finalArray);
                $result = ['status' => 'success', 'message' => 'Student Batch Save Sucessfully'];
                echo json_encode($result);
                exit;
            case 'get_confirmation_data':
                $courseAndUnitData = $this->getSessionData($request, 'course_and_unit');
                if (! $courseAndUnitData) {
                    $result = ['status' => 'error', 'message' => 'Session data not found. Please start the timetable creation process again.'];
                    echo json_encode($result);
                    exit;
                }

                // Validate required keys
                if (! isset($courseAndUnitData['days']) || ! isset($courseAndUnitData['week_start']) || ! isset($courseAndUnitData['week_end'])) {
                    $result = ['status' => 'error', 'message' => 'Required session data is missing. Please complete the timetable creation process.'];
                    echo json_encode($result);
                    exit;
                }

                $sessionData['data'] = $courseAndUnitData;
                $sessionData['days'] = implode(', ', $courseAndUnitData['days']);
                $weekStartArr = explode('/', $courseAndUnitData['week_start']);
                $weekEndArr = explode('/', $courseAndUnitData['week_end']);
                $sessionData['dates'] = date('d M Y', strtotime($weekStartArr[1])).' - '.date('d M Y', strtotime($weekEndArr[1])).' { Week(s):'.$weekStartArr[0].'-'.$weekEndArr[0].'}';
                $result = ['status' => 'success', 'message' => 'Student Batch Data Save Sucessfully', $sessionData];
                echo json_encode($result);
                exit;
            case 'save_timetable_data':
                $oldSessionArray = $this->getSessionData($request, 'course_and_unit');
                $finalArray = [];
                if (! empty($oldSessionArray)) {
                    $finalArray = array_merge($oldSessionArray, $data);
                } else {
                    $finalArray = $data;
                }
                $this->storeSessionData($request, 'course_and_unit', $finalArray);
                $storedData = $this->getSessionData($request, 'course_and_unit');
                $saveData = $this->saveTimetableData($request, $storedData);
                if ($saveData) {
                    // Clear both session and cache
                    $request->session()->forget('course_and_unit');
                    $cacheKey = 'course_and_unit_'.auth()->id();
                    Cache::forget($cacheKey);
                }
                $result = ['status' => 'success', 'message' => 'Student Batch Data Save Sucessfully', $saveData];
                echo json_encode($result);
                exit;
            case 'advancetimetable_calendar':
                $advanceCalendar = $this->getAdvanceTimeTableCalendar($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceCalendar];
                echo json_encode($result);
                exit;
            case 'advancetimetable_calendarv2':
                $advanceCalendar = $this->getAdvanceTimeTableCalendarV2($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceCalendar];
                echo json_encode($result);
                exit;
            case 'advancetimetable_calendar_show_more_data':
                $advanceCalendar = $this->getAdvanceTimeTableCalendarShowMoreData($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', 'data' => $advanceCalendar];
                echo json_encode($result);
                exit;
            case 'advancetimetable_room':
                $advanceRoom = $this->getAdvanceTimeTableRoom($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceRoom];
                echo json_encode($result);
                exit;
            case 'advancetimetable_roomv2':
                $advanceRoom = $this->getAdvanceTimeTableRoomv2($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceRoom];
                echo json_encode($result);
                exit;
            case 'advancetimetable_room_show_more_data':
                $advanceRoom = $this->getAdvanceTimeTableRoomShowMoreData($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceRoom];
                echo json_encode($result);
                exit;
            case 'advancetimetable_room_dropdown':
                $advanceRoom = $this->getAdvanceTimeTableRoomDropDown($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceRoom];
                echo json_encode($result);
                exit;
            case 'advancetimetable_teacher':
                $advanceTeacher = $this->getAdvanceTimeTableTeacher($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceTeacher];
                echo json_encode($result);
                exit;
            case 'advancetimetable_teacherV2':
                $advanceTeacher = $this->getAdvanceTimeTableTeacherV2($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceTeacher];
                echo json_encode($result);
                exit;
            case 'advancetimetable_teacher_dropdown':
                $advanceTeacher = $this->getAdvanceTimeTableTeacherDropDown($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $advanceTeacher];
                echo json_encode($result);
                exit;
            case 'get_student_list_by_batch':
                $studentList = $this->studentListByBatch($data);
                $result = ['status' => 'success', 'message' => 'Data found Sucessfully', $studentList];
                echo json_encode($result);
                exit;
        }
        exit;
    }

    public function saveClassBreakTime($data)
    {
        $collegeId = Auth::user()->college_id;
        if (strtotime($data['break_from']) == strtotime($data['break_to'])) {
            $result = ['status' => 'error', 'message' => 'Breakfrom And Breakto Time should not Be Equal'];
        } else {
            $updateTimetableData = $this->timeTable->update($data, $data['id']);
            $checkDuplicate = $this->timeTableBreakTime->getData(['college_id' => $collegeId, 'break_from' => $data['break_from'], 'break_to' => $data['break_to'], 'time_table' => $data['id']], ['id']);
            if (empty($checkDuplicate)) {
                $breaktimeData = [
                    'college_id' => $collegeId,
                    'time_table' => $data['id'],
                    'break_from' => $data['break_from'],
                    'break_to' => $data['break_to'],
                    'created_by' => Auth::user()->id,
                    'updated_by' => Auth::user()->id,
                ];
                $createTimeTableBreakTime = $this->timeTableBreakTime->create($breaktimeData);
            }
            $result = ['status' => 'success', 'message' => 'Class Break time Updated Successfully'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveDefaultTime($data)
    {
        $collegeId = Auth::user()->college_id;
        $data['start_time'] = date('H:i', strtotime($data['start_time']));
        $data['finish_time'] = date('H:i', strtotime($data['finish_time']));
        if ($data['is_break'] == 'true') {
            $data['break_from'] = (isset($data['break_from_2']) && $data['break_from_2'] != '') ? $data['break_from'].','.$data['break_from_2'] : $data['break_from'];
            $data['break_to'] = (isset($data['break_to_2']) && $data['break_to_2'] != '') ? $data['break_to'].','.$data['break_to_2'] : $data['break_to'];
        } else {
            $data['break_from'] = '00:00';
            $data['break_to'] = '00:00';
        }
        if ($data['break_from'] == $data['break_to'] && $data['is_break'] == 'true') {
            $result = ['status' => 'error', 'message' => 'BreakFrom And BreakTo Time should not equal'];
        } else {
            $DefaultTimetableData = [
                'college_id' => $collegeId,
                'course_type_id' => $data['course_type_id'],
                'start_time' => $data['start_time'],
                'finish_time' => $data['finish_time'],
                'break_from' => $data['break_from'],
                'break_to' => $data['break_to'],
                'created_by' => Auth::user()->id,
                'updated_by' => Auth::user()->id,
            ];
            $createDefaultTimetable = $this->classDefaultTimetable->create($DefaultTimetableData);
            $result = ['status' => 'success', 'message' => 'Default Timetable Set successfully'];
        }
        echo json_encode($result);
        exit;
    }

    public function getAdvanceTimeTableRoom($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $month = $data['month'];
        $year = $data['year'];
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $rowQuery = CampusVenue::from('rto_venue as rtr')
            ->join('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id');
        if (isset($data['room_id']) && $data['room_id'] != 'all') {
            $rowQuery->whereIn('rtc.id', $data['room_id']);
        }
        if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
            $rowQuery->where('rtr.campus_id', $data['campus_id']);
        }
        $getRooms = $rowQuery->select('rtc.id as classroomId', 'rtc.room_id', 'rtc.room_name', 'rtc.max_capacity')->get()->toArray();
        $roomsData = [];
        $availableRecord = [];
        foreach ($getRooms as $room) {
            $roomsData[] = [
                'text' => $room['room_name'],
                'value' => $room['classroomId'],
                'color' => $room['max_capacity'],
            ];
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.room_id', '=', $room['classroomId']);
            if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
                $validRecordQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
            }
            $validRecord = $validRecordQuery->select(DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
                'rtd.timetable_date', 'rtd.start_time', 'rtd.end_time', 'rtd.id',
                'rtd.day', 'rtd.room_id', 'rt.subject_id')->get()->toArray();
            if (count($validRecord) > 0) {
                $availableRecord[] = $validRecord;
            }
        }
        $finalArray = [];
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $values) {
                $finalArray[] = [
                    'MeetingID' => $values['id'],
                    'RoomID' => $values['room_id'],
                    'Title' => $values['subject_name'].' '.$values['start_time'].' '.$values['end_time'],
                    'Start' => $values['timetable_date'],
                    'End' => $values['timetable_date'],
                    'IsAllDay' => true,
                    'Description' => null,
                    'Day' => $values['day'],
                ];
            }
        }
        $array = [
            'rooms' => $roomsData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableRoomv2($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $month = $data['month'];
        $year = $data['year'];
        $isWeekView = $data['isWeekView'];
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $rowQuery = CampusVenue::from('rto_venue as rtr')
            ->join('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id');
        if (isset($data['room_id']) && $data['room_id'] != 'all') {
            $rowQuery->whereIn('rtc.id', $data['room_id']);
        }
        if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
            $rowQuery->where('rtr.campus_id', $data['campus_id']);
        }
        $getRooms = $rowQuery->select('rtc.id as classroomId', 'rtc.room_id', 'rtc.room_name', 'rtc.max_capacity')->get()->toArray();
        $roomsData = [];
        $availableRecord = [];
        foreach ($getRooms as $room) {
            $roomsData[] = [
                'text' => $room['room_name'],
                'value' => $room['classroomId'],
                'color' => $room['max_capacity'],
            ];
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
                ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.room_id', '=', $room['classroomId'])
                ->orderBy('rtd.timetable_date', 'ASC');
            if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
                $validRecordQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
            }
            $validRecord = $validRecordQuery->select(
                DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
                'rtd.timetable_date',
                'rtd.id',
                'rs.subject_code',
                'rtd.start_time',
                'rtd.end_time',
                'rt.batch',
                'rt.class_type',
                'rt.end_week as end_date',
                'rsem.semester_name',
                'rsem.id',
                'rt.term',
                'rt.attendance_type as attendance_type',
                'rt.class_capacity',
                'rt.break_from',
                'rtd.day',
                'rt.break_to',
                'rtd.room_id',
                'rc.room_name',
                'rtd.teacher_id',
                'rt.subject_id'
            )->get()->toArray();
            $finalArr = [];
            $finalArr1 = [];
            // add Day Number in Main Array
            $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
            foreach ($validRecord as $key => $val) {
                $finalArr[$key] = $val;
                $finalArr[$key]['daynumber'] = $arr[$val['day']];
            }
            foreach ($finalArr as $valueFinalArr) {
                $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
            }
            // Make in Group
            $i = 0;
            $last = null;
            $output = [];
            foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
                foreach ($valueFinalArr1 as $n) {
                    if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                        $i++;
                    }
                    $output[$i][$keyFinalArray1][] = $n;
                    $last = $n['daynumber'];
                }
            }
            // Add End Date
            $finalArrWithFinalOutput = [];
            $l = 0;
            foreach ($output as $valueOutput) {
                foreach ($valueOutput as $keyOutput => $valOutput) {
                    $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                    $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                    $l++;
                }
            }
            $finalArrWithFinalOutput1 = [];
            foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
                foreach ($valueFinalOuput as $vfo) {
                    $finalArrWithFinalOutput1[] = $vfo;
                }
            }
            if (count($validRecord) > 0) {
                $availableRecord[] = $finalArrWithFinalOutput;
            }
        }$color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $val) {
                foreach ($val as $values) {
                    if ($k > 4) {
                        $k = 0;
                    }
                    $finalArray[] = [
                        'MeetingID' => $values['id'],
                        'RoomID' => $values['room_id'],
                        'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                        // 'Start'         => $values['timetable_date'],
                        // 'End'           =>  $values['timetable_date'],
                        'trainer_name' => $values['trainer_name'],
                        'subject_name' => $values['subject_name'],
                        'semester_name' => $values['semester_name'],
                        'semester' => $values['id'],
                        'batch' => $values['batch'],
                        'term' => $values['term'],
                        'attendance_type' => $values['attendance_type'],
                        'class_capacity' => $values['class_capacity'],
                        'break' => (! empty($values['break_from']) ? 'Yes' : 'No'),
                        'room_name' => $values['room_name'],
                        'color1' => $color1[$k],
                        'color2' => $color2[$k],
                        'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                        'Start' => $values['timetable_date'].' '.$values['start_time'],
                        'End' => $values['timetable_end_date'].' '.$values['end_time'],
                        'Start_end' => date('M-d', strtotime($values['timetable_date'])).' - '.date('M-d', strtotime($values['timetable_end_date'])),
                        'IsAllDay' => ! empty($isWeekView) ? true : false,
                        'Description' => null,
                        'Day' => $values['day'],
                    ];
                    $k++;
                }
            }
        }
        $array = [
            'rooms' => $roomsData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableRoomShowMoreData($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $month = $data['month'];
        $year = $data['year'];
        $isWeekView = $data['isWeekView'];
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $rowQuery = CampusVenue::from('rto_venue as rtr')
            ->join('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id');
        if (isset($data['room_id']) && $data['room_id'] != 'all') {
            $rowQuery->whereIn('rtc.id', $data['room_id']);
        }
        if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
            $rowQuery->where('rtr.campus_id', $data['campus_id']);
        }
        $getRooms = $rowQuery->select('rtc.id as classroomId', 'rtc.room_id', 'rtc.room_name', 'rtc.max_capacity')->get()->toArray();
        $roomsData = [];
        $availableRecord = [];
        foreach ($getRooms as $room) {
            $roomsData[] = [
                'text' => $room['room_name'],
                'value' => $room['classroomId'],
                'color' => $room['max_capacity'],
            ];
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
                ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.room_id', '=', $room['classroomId'])
                ->orderBy('rtd.timetable_date', 'ASC');
            if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
                $validRecordQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
            }
            $validRecord = $validRecordQuery->select(
                DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
                'rtd.timetable_date',
                'rtd.id',
                'rs.subject_code',
                'rtd.start_time',
                'rtd.end_time',
                'rt.batch',
                'rt.class_type',
                'rt.end_week as end_date',
                'rsem.semester_name',
                'rsem.id',
                'rt.term',
                'rt.attendance_type as attendance_type',
                'rt.class_capacity',
                'rt.break_from',
                'rtd.day',
                'rt.break_to',
                'rtd.room_id',
                'rc.room_name',
                'rtd.teacher_id',
                'rt.subject_id'
            )->get()->toArray();
            $finalArr = [];
            $finalArr1 = [];
            // add Day Number in Main Array
            $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
            foreach ($validRecord as $key => $val) {
                $finalArr[$key] = $val;
                $finalArr[$key]['daynumber'] = $arr[$val['day']];
            }
            foreach ($finalArr as $valueFinalArr) {
                $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
            }
            // Make in Group
            $i = 0;
            $last = null;
            $output = [];
            foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
                foreach ($valueFinalArr1 as $n) {
                    if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                        $i++;
                    }
                    $output[$i][$keyFinalArray1][] = $n;
                    $last = $n['daynumber'];
                }
            }
            // Add End Date
            $finalArrWithFinalOutput = [];
            $l = 0;
            foreach ($output as $valueOutput) {
                foreach ($valueOutput as $keyOutput => $valOutput) {
                    $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                    $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                    $l++;
                }
            }
            $finalArrWithFinalOutput1 = [];
            foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
                foreach ($valueFinalOuput as $vfo) {
                    $finalArrWithFinalOutput1[] = $vfo;
                }
            }
            if (count($validRecord) > 0) {
                $availableRecord[] = $finalArrWithFinalOutput;
            }
        }$color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $val) {
                foreach ($val as $values) {
                    if ($k > 4) {
                        $k = 0;
                    }
                    $finalArray[] = [
                        'MeetingID' => $values['id'],
                        'RoomID' => $values['room_id'],
                        'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                        // 'Start'         => $values['timetable_date'],
                        // 'End'           =>  $values['timetable_date'],
                        'trainer_name' => $values['trainer_name'],
                        'subject_name' => $values['subject_name'],
                        'semester_name' => $values['semester_name'],
                        'semester' => $values['id'],
                        'batch' => $values['batch'],
                        'term' => $values['term'],
                        'attendance_type' => $values['attendance_type'],
                        'class_capacity' => $values['class_capacity'],
                        'break' => (! empty($values['break_from']) ? 'Yes' : 'No'),
                        'room_name' => $values['room_name'],
                        'color1' => $color1[$k],
                        'color2' => $color2[$k],
                        'search_date' => $values['timetable_end_date'],
                        'search_date_start' => $values['timetable_date'],
                        'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                        'Start' => $values['timetable_date'].' '.$values['start_time'],
                        'End' => $values['timetable_end_date'].' '.$values['end_time'],
                        'Start_end' => date('M-d', strtotime($values['timetable_date'])).' - '.date('M-d', strtotime($values['timetable_end_date'])),
                        'IsAllDay' => ! empty($isWeekView) ? true : false,
                        'Description' => null,
                        'Day' => $values['day'],
                    ];
                    $k++;
                }
            }
        }
        $search_date = (empty($data['search_date'])) ? '' : date('Y-m-d', strtotime($data['search_date']));
        $f = [];
        if (! empty($search_date)) {
            $p = 0;
            foreach ($finalArray as $k => $val) {
                if ($val['search_date_start'] == $search_date) {
                    $f[$p] = $val;
                    $p++;
                }
                if (empty($f)) {
                    if ($val['search_date'] == $search_date) {
                        $f[$p] = $val;
                        $p++;
                    }
                }
            }
            $array = [
                'rooms' => $roomsData,
                'dataarray' => $f,
            ];

            return $array;
        }
        $array = [
            'rooms' => $roomsData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableRoomDropDown($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $month = $data['month'];
        $year = $data['year'];
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $rowQuery = CampusVenue::from('rto_venue as rtr')
            ->join('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id');
        if (isset($data['room_id']) && $data['room_id'] != 'all') {
            $rowQuery->where('rtc.id', $data['room_id']);
        }
        if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
            $rowQuery->where('rtr.campus_id', $data['campus_id']);
        }
        $getRooms = $rowQuery->select('rtc.id as classroomId', 'rtc.room_id', 'rtc.room_name', 'rtc.max_capacity')->get()->toArray();
        $roomsData = [];
        $availableRecord = [];
        foreach ($getRooms as $room) {
            $roomsData[] = [
                'text' => $room['room_name'],
                'value' => $room['classroomId'],
                'color' => $room['max_capacity'],
            ];
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
                ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.room_id', '=', $room['classroomId'])
                ->orderBy('rtd.timetable_date', 'ASC');
            if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
                $validRecordQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
            }
            $validRecord = $validRecordQuery->select(
                DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
                'rtd.timetable_date',
                'rtd.id',
                'rs.subject_code',
                'rtd.start_time',
                'rtd.end_time',
                'rt.batch',
                'rt.class_type',
                'rt.end_week as end_date',
                'rsem.semester_name',
                'rsem.id',
                'rt.term',
                'rt.attendance_type as attendance_type',
                'rt.class_capacity',
                'rt.break_from',
                'rtd.day',
                'rt.break_to',
                'rtd.room_id',
                'rc.room_name',
                'rtd.teacher_id',
                'rt.subject_id'
            )->get()->toArray();
            $finalArr = [];
            $finalArr1 = [];
            // add Day Number in Main Array
            $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
            foreach ($validRecord as $key => $val) {
                $finalArr[$key] = $val;
                $finalArr[$key]['daynumber'] = $arr[$val['day']];
            }
            foreach ($finalArr as $valueFinalArr) {
                $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
            }
            // Make in Group
            $i = 0;
            $last = null;
            $output = [];
            foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
                foreach ($valueFinalArr1 as $n) {
                    if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                        $i++;
                    }
                    $output[$i][$keyFinalArray1][] = $n;
                    $last = $n['daynumber'];
                }
            }
            // Add End Date
            $finalArrWithFinalOutput = [];
            $l = 0;
            foreach ($output as $valueOutput) {
                foreach ($valueOutput as $keyOutput => $valOutput) {
                    $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                    $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                    $l++;
                }
            }
            $finalArrWithFinalOutput1 = [];
            foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
                foreach ($valueFinalOuput as $vfo) {
                    $finalArrWithFinalOutput1[] = $vfo;
                }
            }
            if (count($validRecord) > 0) {
                $availableRecord[] = $finalArrWithFinalOutput;
            }
        }
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $val) {
                foreach ($val as $values) {
                    if ($k > 4) {
                        $k = 0;
                    }
                    $finalArray[] = [
                        'MeetingID' => $values['id'],
                        'RoomID' => $values['room_id'],
                        'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                        'trainer_name' => $values['trainer_name'],
                        'subject_name' => $values['subject_name'],
                        'semester_name' => $values['semester_name'],
                        'semester' => $values['id'],
                        'batch' => $values['batch'],
                        'term' => $values['term'],
                        'attendance_type' => $values['attendance_type'],
                        'class_capacity' => $values['class_capacity'],
                        'break' => (! empty($values['break_from']) ? 'Yes' : 'No'),
                        'room_name' => $values['room_name'],
                        'color1' => $color1[$k],
                        'color2' => $color2[$k],
                        'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                        'Start' => $values['timetable_date'].' '.$values['start_time'],
                        'End' => $values['timetable_end_date'].' '.$values['end_time'],
                        'Start_end' => date('M-d', strtotime($values['timetable_date'])).' - '.date('M-d', strtotime($values['timetable_end_date'])),
                        'IsAllDay' => false,
                        'Description' => null,
                        'Day' => $values['day'],
                    ];
                    $k++;
                }
            }
        }
        $array = [
            'rooms' => $roomsData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableTeacher($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $startDate = $data['start_date'];
        $endDate = $data['end_date'];
        $validRecord = [];
        $rowQuery = TeacherMatrix::from('rto_teacher_matrix as rtm')
            ->join('rto_staff_and_teacher as rst', 'rtm.teacher_id', '=', 'rst.id');
        if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
            $rowQuery->where('rst.id', $data['trainer_id']);
        }
        $teachersDetail = $rowQuery->select(DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"), 'rst.id as teacher_id')
            ->groupBy('rst.id')
            ->get()
            ->toArray();
        $teachersData = [];
        $availableRecord = [];
        foreach ($teachersDetail as $value) {
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.teacher_id', '=', $value['teacher_id']);
            if (isset($data['room_id']) && $data['room_id'] != 'all') {
                $validRecordQuery->where('rtd.room_id', $data['room_id']);
            }
            $validRecord = $validRecordQuery->select('rtd.id', 'rtd.teacher_id', 'rt.subject_id',
                'rtd.timetable_date', 'rtd.day', 'rtd.start_time', 'rtd.end_time',
                DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"))->get()->toArray();
            if (count($validRecord) > 0) {
                $availableRecord[] = $validRecord;
            }
        }
        $teacherHoursArr = [];
        $finalArray = [];
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $values) {
                $t1 = strtotime($values['start_time']);
                $t2 = strtotime($values['end_time']);
                $hours = ($t2 - $t1) / 3600;
                $finalArray[] = [
                    'MeetingID' => $values['id'],
                    'teachersID' => $values['teacher_id'],
                    'Title' => $values['subject_name'].' '.$values['start_time'].' '.$values['end_time'],
                    'Start' => $values['timetable_date'],
                    'End' => $values['timetable_date'],
                    'IsAllDay' => true,
                    'Description' => null,
                    'Day' => $values['day'],
                ];
                $teacherHoursArr[$values['teacher_id']][] = $hours;
            }
        }
        foreach ($teachersDetail as $value) {
            if (isset($teacherHoursArr[$value['teacher_id']])) {
                $sumOfHours = array_sum($teacherHoursArr[$value['teacher_id']]);
                $teachersData[] = [
                    'text' => $value['trainer_name'],
                    'value' => $value['teacher_id'],
                    'color' => ($sumOfHours < 40) ? $sumOfHours.'/40 hrs available' : $sumOfHours.'/'.$sumOfHours.' hrs available',
                ];
            } else {
                $teachersData[] = [
                    'text' => $value['trainer_name'],
                    'value' => $value['teacher_id'],
                    'color' => '00/40 hrs available',
                ];
            }
        }
        $array = [
            'teachers' => $teachersData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableTeacherV2($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $startDate = $data['start_date'];
        $endDate = $data['end_date'];
        $isWeekView = $data['isWeekView'];
        $validRecord = [];
        $rowQuery = TeacherMatrix::from('rto_teacher_matrix as rtm')
            ->join('rto_staff_and_teacher as rst', 'rtm.teacher_id', '=', 'rst.id');
        if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
            $rowQuery->whereIn('rst.id', $data['trainer_id']);
        }
        $teachersDetail = $rowQuery->select(DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"), 'rst.id as teacher_id')
            ->groupBy('rst.id')
            ->get()
            ->toArray();
        $teachersData = [];
        $availableRecord = [];
        foreach ($teachersDetail as $value) {
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->leftJoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
                ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.teacher_id', '=', $value['teacher_id'])
                ->orderBy('rtd.timetable_date', 'ASC');
            if (isset($data['room_id']) && $data['room_id'] != 'all') {
                $validRecordQuery->where('rtd.room_id', $data['room_id']);
            }
            //    $validRecord = $validRecordQuery->select('rtd.id','rtd.teacher_id','rt.subject_id',
            //                     'rtd.timetable_date','rtd.day','rtd.start_time','rtd.end_time',
            //                     DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
            //                     DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"))->get()->toArray();
            $validRecord = $validRecordQuery->select(
                DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
                'rtd.timetable_date',
                'rtd.id',
                'rs.subject_code',
                'rtd.start_time',
                'rtd.end_time',
                'rt.batch',
                'rt.class_type',
                'rt.end_week as end_date',
                'rsem.semester_name',
                'rsem.id',
                'rt.term',
                'rt.attendance_type as attendance_type',
                'rt.class_capacity',
                'rt.break_from',
                'rtd.day',
                'rt.break_to',
                'rtd.room_id',
                'rc.room_name',
                'rtd.teacher_id',
                'rt.subject_id'
            )->get()->toArray();
            $finalArr = [];
            $finalArr1 = [];
            // add Day Number in Main Array
            $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
            foreach ($validRecord as $key => $val) {
                $finalArr[$key] = $val;
                $finalArr[$key]['daynumber'] = $arr[$val['day']];
            }
            foreach ($finalArr as $valueFinalArr) {
                $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
            }
            // Make in Group
            $i = 0;
            $last = null;
            $output = [];
            foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
                foreach ($valueFinalArr1 as $n) {
                    if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                        $i++;
                    }
                    $output[$i][$keyFinalArray1][] = $n;
                    $last = $n['daynumber'];
                }
            }
            // Add End Date
            $finalArrWithFinalOutput = [];
            $l = 0;
            foreach ($output as $valueOutput) {
                foreach ($valueOutput as $keyOutput => $valOutput) {
                    $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                    $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                    $l++;
                }
            }
            $finalArrWithFinalOutput1 = [];
            foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
                foreach ($valueFinalOuput as $vfo) {
                    $finalArrWithFinalOutput1[] = $vfo;
                }
            }
            if (count($validRecord) > 0) {
                $availableRecord[] = $finalArrWithFinalOutput1;
            }
        }
        // dd($availableRecord);
        $teacherHoursArr = [];
        $finalArray = [];
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $k = 0;
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $values) {
                $t1 = strtotime($values['start_time']);
                $t2 = strtotime($values['end_time']);
                $hours = ($t2 - $t1) / 3600;
                if ($k > 4) {
                    $k = 0;
                }
                $finalArray[] = [
                    'MeetingID' => $values['id'],
                    'teachersID' => $values['teacher_id'],
                    'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                    'trainer_name' => $values['trainer_name'],
                    'subject_name' => $values['subject_name'],
                    'semester_name' => $values['semester_name'],
                    'semester' => $values['id'],
                    'batch' => $values['batch'],
                    'term' => $values['term'],
                    'attendance_type' => $values['attendance_type'],
                    'class_capacity' => $values['class_capacity'],
                    'break' => (! empty($values['break_from']) ? 'Yes' : 'No'),
                    'room_name' => $values['room_name'],
                    'color1' => $color1[$k],
                    'color2' => $color2[$k],
                    'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                    'Start' => $values['timetable_date'].' '.$values['start_time'],
                    'End' => $values['timetable_end_date'].' '.$values['end_time'],
                    'Start_end' => date('M-d', strtotime($values['timetable_date'])).' - '.date('M-d', strtotime($values['timetable_end_date'])),
                    'IsAllDay' => ! empty($isWeekView) ? true : false,
                    'Description' => null,
                    'Day' => $values['day'],
                ];
                $k++;
                $teacherHoursArr[$values['teacher_id']][] = $hours;
            }
        }
        foreach ($teachersDetail as $value) {
            if (isset($teacherHoursArr[$value['teacher_id']])) {
                $sumOfHours = array_sum($teacherHoursArr[$value['teacher_id']]);
                $teachersData[] = [
                    'text' => $value['trainer_name'],
                    'value' => $value['teacher_id'],
                    'color' => ($sumOfHours < 40) ? $sumOfHours.'/40 hrs available' : $sumOfHours.'/'.$sumOfHours.' hrs available',
                ];
            } else {
                $teachersData[] = [
                    'text' => $value['trainer_name'],
                    'value' => $value['teacher_id'],
                    'color' => '00/40 hrs available',
                ];
            }
        }
        $array = [
            'teachers' => $teachersData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableTeacherDropDown($data)
    {
        $collegeId = Auth::user()->college_id;
        $campusId = $data['campus_id'];
        $startDate = $data['start_date'];
        $endDate = $data['end_date'];
        $validRecord = [];
        $rowQuery = TeacherMatrix::from('rto_teacher_matrix as rtm')
            ->join('rto_staff_and_teacher as rst', 'rtm.teacher_id', '=', 'rst.id');
        if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
            $rowQuery->whereIn('rst.id', $data['trainer_id']);
        }
        $teachersDetail = $rowQuery->select(DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"), 'rst.id as teacher_id')
            ->groupBy('rst.id')
            ->get()
            ->toArray();
        $teachersData = [];
        $availableRecord = [];
        foreach ($teachersDetail as $value) {
            $validRecordQuery = TimetableDetail::from('rto_timetable_detail as rtd')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
                ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
                ->leftJoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
                ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
                ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
                ->where('rtd.teacher_id', '=', $value['teacher_id']);
            if (isset($data['room_id']) && $data['room_id'] != 'all') {
                $validRecordQuery->where('rtd.room_id', $data['room_id']);
            }
            $validRecord = $validRecordQuery->select(
                DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
                DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
                'rtd.timetable_date',
                'rtd.id',
                'rs.subject_code',
                'rtd.start_time',
                'rtd.end_time',
                'rt.batch',
                'rt.class_type',
                'rt.end_week as end_date',
                'rsem.semester_name',
                'rsem.id',
                'rt.term',
                'rt.attendance_type as attendance_type',
                'rt.class_capacity',
                'rt.break_from',
                'rtd.day',
                'rt.break_to',
                'rtd.room_id',
                'rc.room_name',
                'rtd.teacher_id',
                'rt.subject_id'
            )->get()->toArray();
            $finalArr = [];
            $finalArr1 = [];
            // add Day Number in Main Array
            $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
            foreach ($validRecord as $key => $val) {
                $finalArr[$key] = $val;
                $finalArr[$key]['daynumber'] = $arr[$val['day']];
            }
            foreach ($finalArr as $valueFinalArr) {
                $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
            }
            // Make in Group
            $i = 0;
            $last = null;
            $output = [];
            foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
                foreach ($valueFinalArr1 as $n) {
                    if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                        $i++;
                    }
                    $output[$i][$keyFinalArray1][] = $n;
                    $last = $n['daynumber'];
                }
            }
            // Add End Date
            $finalArrWithFinalOutput = [];
            $l = 0;
            foreach ($output as $valueOutput) {
                foreach ($valueOutput as $keyOutput => $valOutput) {
                    $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                    $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                    $l++;
                }
            }
            $finalArrWithFinalOutput1 = [];
            foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
                foreach ($valueFinalOuput as $vfo) {
                    $finalArrWithFinalOutput1[] = $vfo;
                }
            }
            if (count($validRecord) > 0) {
                $availableRecord[] = $finalArrWithFinalOutput;
            }
        }
        // dd($availableRecord);
        $teacherHoursArr = [];
        $finalArray = [];
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $k = 0;
        foreach ($availableRecord as $key => $value) {
            foreach ($value as $val) {
                foreach ($val as $values) {
                    $t1 = strtotime($values['start_time']);
                    $t2 = strtotime($values['end_time']);
                    $hours = ($t2 - $t1) / 3600;
                    if ($k > 4) {
                        $k = 0;
                    }
                    $finalArray[] = [
                        'MeetingID' => $values['id'],
                        'teachersID' => $values['teacher_id'],
                        'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                        'trainer_name' => $values['trainer_name'],
                        'subject_name' => $values['subject_name'],
                        'semester_name' => $values['semester_name'],
                        'semester' => $values['id'],
                        'batch' => $values['batch'],
                        'term' => $values['term'],
                        'attendance_type' => $values['attendance_type'],
                        'class_capacity' => $values['class_capacity'],
                        'break' => (! empty($values['break_from']) ? 'Yes' : 'No'),
                        'room_name' => $values['room_name'],
                        'color1' => $color1[$k],
                        'color2' => $color2[$k],
                        'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                        'Start' => $values['timetable_date'].' '.$values['start_time'],
                        'End' => $values['timetable_end_date'].' '.$values['end_time'],
                        'Start_end' => date('M-d', strtotime($values['timetable_date'])).' - '.date('M-d', strtotime($values['timetable_end_date'])),
                        'IsAllDay' => true,
                        'Description' => null,
                        'Day' => $values['day'],
                    ];
                    $k++;
                    $teacherHoursArr[$values['teacher_id']][] = $hours;
                }
            }
        }
        foreach ($teachersDetail as $value) {
            if (isset($teacherHoursArr[$value['teacher_id']])) {
                $sumOfHours = array_sum($teacherHoursArr[$value['teacher_id']]);
                $teachersData[] = [
                    'text' => $value['trainer_name'],
                    'value' => $value['teacher_id'],
                    'color' => ($sumOfHours < 40) ? $sumOfHours.'/40 hrs available' : $sumOfHours.'/'.$sumOfHours.' hrs available',
                ];
            } else {
                $teachersData[] = [
                    'text' => $value['trainer_name'],
                    'value' => $value['teacher_id'],
                    'color' => '00/40 hrs available',
                ];
            }
        }
        $array = [
            'teachers' => $teachersData,
            'dataarray' => $finalArray,
        ];

        return $array;
    }

    public function getAdvanceTimeTableCalendar($data)
    {
        $collegeId = Auth::user()->college_id;
        $month = isset($data['month']) ? $data['month'] : date('m');
        $year = isset($data['year']) ? $data['year'] : date('Y');
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $validRecord = [];
        $rowQuery = TimetableDetail::from('rto_timetable_detail as rtd')
            ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
            ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
            ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
            ->where('rt.college_id', $collegeId)
            ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
            ->orderBy('rtd.timetable_date', 'ASC');
        if (! isset($data['room_id']) && ! isset($data['trainer_id'])) {
            $rowQuery->limit('5');
        } else {
            if (isset($data['room_id']) && $data['room_id'] != 'all') {
                $rowQuery->whereIn('rtd.room_id', $data['room_id']);
            }
            if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
                $rowQuery->where('rt.campus_id', $data['campus_id']);
            }
            if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
                $rowQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
            }
        }
        $validRecord = $rowQuery->select(
            DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
            DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
            'rtd.timetable_date',
            'rs.subject_code',
            'rtd.start_time',
            'rtd.end_time',
            'rtd.room_id',
            'rtd.id',
            'rc.room_name',
            'rtd.teacher_id',
            'rt.subject_id'
        )->get()->toArray();
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($validRecord as $key => $values) {
            if ($k > 4) {
                $k = 0;
            }
            $finalArray[] = [
                'TaskId' => $values['id'],
                'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                'trainer_name' => $values['trainer_name'],
                'subject_name' => $values['subject_name'],
                'room_name' => $values['room_name'],
                'color1' => $color1[$k],
                'color2' => $color2[$k],
                'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                'Start' => $values['timetable_date'].' '.$values['start_time'],
                'End' => $values['timetable_date'].' '.$values['end_time'],
                'IsAllDay' => false,
            ];
            $k++;
        }

        return $finalArray;
    }

    public function splitArrayByDate($arr, $days)
    {
        $result = [];
        $subarr = [];
        for ($i = 0; $i < count($arr); $i++) {
            $subarr[] = $arr[$i];
            if (isset($arr[$i + 1]) && strtotime($arr[$i + 1]) - strtotime($arr[$i]) != $days * 24 * 60 * 60) {
                $result[] = $subarr;
                $subarr = [];
            }
        }
        if (! empty($subarr)) {
            $result[] = $subarr;
        }

        return $result;
    }

    public function getAdvanceTimeTableCalendarV2($data)
    {
        $collegeId = Auth::user()->college_id;
        $arrAttendanceType = Config::get('constants.arrAttendanceType');
        $month = isset($data['month']) ? $data['month'] : date('m');
        $year = isset($data['year']) ? $data['year'] : date('Y');
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $validRecord = [];
        $rowQuery = TimetableDetail::from('rto_timetable_detail as rtd')
            ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
            ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->leftJoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
            ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
            ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
            ->where('rt.college_id', $collegeId)
            ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
            ->orderBy('rtd.timetable_date', 'ASC');
        if (isset($data['room_id']) && $data['room_id'] != 'all') {
            $rowQuery->whereIn('rtd.room_id', $data['room_id']);
        }
        if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
            $rowQuery->where('rt.campus_id', $data['campus_id']);
        }
        if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
            $rowQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
        }
        $validRecord = $rowQuery->select(
            DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
            DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
            'rtd.timetable_date',
            'rs.subject_code',
            'rtd.start_time',
            'rtd.end_time',
            'rt.batch',
            'rt.class_type',
            'rt.end_week as end_date',
            'rsem.semester_name',
            'rsem.id',
            'rt.term',
            'rt.attendance_type as attendance_type',
            'rt.class_capacity',
            'rt.break_from',
            'rtd.day',
            'rt.break_to',
            'rtd.room_id',
            'rc.room_name',
            'rtd.teacher_id',
            'rt.subject_id'
        )->get()->toArray();
        $finalArr = [];
        $finalArr1 = [];
        // add Day Number in Main Array
        $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
        foreach ($validRecord as $key => $val) {
            $finalArr[$key] = $val;
            $finalArr[$key]['daynumber'] = $arr[$val['day']];
        }
        foreach ($finalArr as $valueFinalArr) {
            $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
        }
        // Make in Group
        $i = 0;
        $last = null;
        $output = [];
        foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
            foreach ($valueFinalArr1 as $n) {
                if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                    $i++;
                }
                $output[$i][$keyFinalArray1][] = $n;
                $last = $n['daynumber'];
            }
        }
        // Add End Date
        $finalArrWithFinalOutput = [];
        $l = 0;
        foreach ($output as $valueOutput) {
            foreach ($valueOutput as $keyOutput => $valOutput) {
                $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                $l++;
            }
        }
        $finalArrWithFinalOutput1 = [];
        foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
            foreach ($valueFinalOuput as $vfo) {
                $finalArrWithFinalOutput1[] = $vfo;
            }
        }
        // dd($finalArrWithFinalOutput1);
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($finalArrWithFinalOutput1 as $valFinal) {
            if ($k > 4) {
                $k = 0;
            }
            $finalArray[] = [
                'TaskId' => $valFinal['id'],
                'Title' => $valFinal['subject_name'].' | '.date('h:i A', strtotime($valFinal['start_time'])).' - '.date('h:i A', strtotime($valFinal['end_time'])).' | '.$valFinal['room_name'].' | '.$valFinal['trainer_name'],
                'trainer_name' => $valFinal['trainer_name'],
                'subject_name' => $valFinal['subject_name'],
                'semester_name' => $valFinal['semester_name'],
                'semester' => $valFinal['id'],
                'batch' => $valFinal['batch'],
                'term' => $valFinal['term'],
                'attendance_type' => $arrAttendanceType[$valFinal['attendance_type']],
                'class_capacity' => ! empty($valFinal['class_capacity']) ? $valFinal['class_capacity'] : '-',
                'break' => (! empty($valFinal['break_from']) ? 'Yes' : 'No'),
                'room_name' => $valFinal['room_name'],
                'color1' => $color1[$k],
                'color2' => $color2[$k],
                'timetable_time' => date('h:i A', strtotime($valFinal['start_time'])).' - '.date('h:i A', strtotime($valFinal['end_time'])),
                'Start' => $valFinal['timetable_date'].' '.$valFinal['start_time'],
                'End' => $valFinal['timetable_end_date'].' '.$valFinal['end_time'],
                'Start_end' => date('M-d', strtotime($valFinal['timetable_date'])).' - '.date('M-d', strtotime($valFinal['timetable_end_date'])),
                'IsAllDay' => false,
            ];
            $k++;
        }

        return $finalArray;
    }

    public function getAdvanceTimeTableCalendarShowMoreData($data)
    {
        $collegeId = Auth::user()->college_id;
        $month = isset($data['month']) ? $data['month'] : date('m');
        $year = isset($data['year']) ? $data['year'] : date('Y');
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $validRecord = [];
        $rowQuery = TimetableDetail::from('rto_timetable_detail as rtd')
            ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
            ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->leftjoin('rto_semester as rsem', 'rsem.id', '=', 'rt.semester_id')
            ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
            ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
            ->where('rt.college_id', $collegeId)
            ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
            ->orderBy('rtd.timetable_date', 'ASC');
        if (isset($data['room_id']) && $data['room_id'] != 'all') {
            $rowQuery->whereIn('rtd.room_id', $data['room_id']);
        }
        if (isset($data['campus_id']) && $data['campus_id'] != 'all') {
            $rowQuery->where('rt.campus_id', $data['campus_id']);
        }
        if (isset($data['trainer_id']) && $data['trainer_id'] != 'all') {
            $rowQuery->whereIn('rtd.teacher_id', $data['trainer_id']);
        }
        $validRecord = $rowQuery->select(
            DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
            DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
            'rtd.timetable_date',
            'rs.subject_code',
            'rtd.start_time',
            'rtd.end_time',
            'rt.batch',
            'rt.class_type',
            'rsem.semester_name',
            'rsem.id',
            'rtd.day',
            'rt.term',
            'rt.attendance_type as attendance_type',
            'rt.class_capacity',
            'rt.break_from',
            'rt.break_to',
            'rtd.room_id',
            'rc.room_name',
            'rtd.teacher_id',
            'rt.subject_id'
        )->get()->toArray();
        // dd($validRecord);
        $finalArr = [];
        $finalArr1 = [];
        // add Day Number in Main Array
        $arr = ['Monday' => '1', 'Tuesday' => '2', 'Wednesday' => '3', 'Thursday' => '4', 'Friday' => '5', 'Saturday' => '6', 'Sunday' => '7'];
        foreach ($validRecord as $key => $val) {
            $finalArr[$key] = $val;
            $finalArr[$key]['daynumber'] = $arr[$val['day']];
        }
        foreach ($finalArr as $valueFinalArr) {
            $finalArr1[$valueFinalArr['subject_code']][] = $valueFinalArr;
        }
        // Make in Group
        $i = 0;
        $last = null;
        foreach ($finalArr1 as $keyFinalArray1 => $valueFinalArr1) {
            foreach ($valueFinalArr1 as $n) {
                if (! is_null($last) && ($n['daynumber'] - $last != 1)) {
                    $i++;
                }
                $output[$i][$keyFinalArray1][] = $n;
                $last = $n['daynumber'];
            }
        }
        // Add End Date
        $finalArrWithFinalOutput = [];
        $l = 0;
        foreach ($output as $valueOutput) {
            foreach ($valueOutput as $keyOutput => $valOutput) {
                // dd($valOutput);
                $finalArrWithFinalOutput[$keyOutput][$l] = $valOutput[0];
                $finalArrWithFinalOutput[$keyOutput][$l]['timetable_end_date'] = $valOutput[count($valOutput) - 1]['timetable_date'];
                $l++;
            }
        }
        $finalArrWithFinalOutput1 = [];
        foreach ($finalArrWithFinalOutput as $valueFinalOuput) {
            foreach ($valueFinalOuput as $vfo) {
                $finalArrWithFinalOutput1[] = $vfo;
            }
        }
        // dd($finalArrWithFinalOutput1);
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($finalArrWithFinalOutput1 as $valFinal) {
            if ($k > 4) {
                $k = 0;
            }
            $finalArray[] = [
                'TaskId' => $valFinal['id'],
                'Title' => $valFinal['subject_name'].' | '.date('h:i A', strtotime($valFinal['start_time'])).' - '.date('h:i A', strtotime($valFinal['end_time'])).' | '.$valFinal['room_name'].' | '.$valFinal['trainer_name'],
                'trainer_name' => $valFinal['trainer_name'],
                'subject_name' => $valFinal['subject_name'],
                'semester_name' => $valFinal['semester_name'],
                'semester' => $valFinal['id'],
                'batch' => $valFinal['batch'],
                'term' => $valFinal['term'],
                'attendance_type' => $valFinal['attendance_type'],
                'class_capacity' => $valFinal['class_capacity'],
                'break' => (! empty($valFinal['break_from']) ? 'Yes' : 'No'),
                'room_name' => $valFinal['room_name'],
                'color1' => $color1[$k],
                'color2' => $color2[$k],
                'search_date' => $valFinal['timetable_end_date'],
                'search_date_start' => $valFinal['timetable_date'],
                'timetable_time' => date('h:i A', strtotime($valFinal['start_time'])).' - '.date('h:i A', strtotime($valFinal['end_time'])),
                'Start' => $valFinal['timetable_date'].' '.$valFinal['start_time'],
                'End' => $valFinal['timetable_end_date'].' '.$valFinal['end_time'],
                'Start_end' => date('M-d', strtotime($valFinal['timetable_date'])).' - '.date('M-d', strtotime($valFinal['timetable_end_date'])),
                'IsAllDay' => false,
            ];
            $k++;
        }
        // dd($finalArray);
        $search_date = (empty($data['search_date'])) ? '' : date('Y-m-d', strtotime($data['search_date']));
        $f = [];
        if (! empty($search_date)) {
            $p = 0;
            foreach ($finalArray as $k => $val) {
                if ($val['search_date_start'] == $search_date) {
                    $f[$p] = $val;
                    $p++;
                }
                if (empty($f)) {
                    if ($val['search_date'] == $search_date) {
                        $f[$p] = $val;
                        $p++;
                    }
                }
            }

            return $f;
        }

        return $finalArray;
    }

    /*
    to reduce the code redundancy
    to check the teacher and room available
    */
    public function checkAvailability($timeTableData, $checkfor = 'room')
    {
        $collegeId = Auth::user()->college_id;
        $day = $timeTableData['days'];
        if ($checkfor == 'rooms' && $timeTableData['default_timetable'] == 'Yes') {
            $defaulTime = explode(' - ', ($timeTableData['default_time']));
            $startTime = date('H:i:s', strtotime($defaulTime[0]));
            $finishTime = date('H:i:s', strtotime($defaulTime[1]));
        } else {
            $startTime = date('H:i:s', strtotime($timeTableData['start_time']));
            $finishTime = date('H:i:s', strtotime($timeTableData['end_time']));
        }
        $campusId = $timeTableData['campus_id'];
        $startWeek = $timeTableData['week_start'];
        $startWeek = explode('/', $startWeek);
        $startWeek = $startWeek[1];
        $endWeek = $timeTableData['week_end'];
        $endWeek = explode('/', $endWeek);
        $endWeek = $endWeek[1];
        $validRecord = [];
        $teachersId = $timeTableData['teacher_id'] ?? null;
        if ($checkfor == 'rooms') {
            /* get number of rooms for that campus */
            $dbData = CampusVenue::from('rto_venue as rtr')
                ->join('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id')
                ->select('rtc.id as classroomId', 'rtc.room_id', 'rtc.room_name', 'rtc.max_capacity')
                ->where('rtr.campus_id', '=', $campusId)->get()->toArray();
            $detailCheckField = 'room_id';
            $returnKeyValue = 'RoomID';
        } else {
            /* get all teachers */
            $dbData = Staff::whereIn('id', $teachersId)->where('college_id', '=', $collegeId)->get()->toArray();
            $detailCheckField = 'teacher_id';
            $returnKeyValue = 'teachersID';
        }
        $retrunData = [];
        $availbleRecord = [];
        $j = 1;
        foreach ($dbData as $key => $value) {
            if ($checkfor == 'rooms') {
                $returntext = $value['room_id'];
                $returnvalue = $value['classroomId'];
                $returncolor = $value['max_capacity'];
            } else {
                $returntext = $value['name_title'].' '.$value['first_name'].' '.$value['last_name'];
                $returnvalue = $value['id'];
                $returncolor = '24/40 hrs available';
            }
            $retrunData[] = [
                'text' => $returntext,
                'value' => $returnvalue,
                'color' => $returncolor,
            ];
            $validRecord = TimetableDetail::whereIn('day', $day)
                ->whereBetween('timetable_date', [$startWeek, $endWeek])
                ->where('start_time', '<=', $finishTime)
                ->where('end_time', '>', $startTime)
                        /*
                        commented this because above condition will find the overlapped time
                        ->where(function($query) use ($startTime, $finishTime) {
                                $query->where(function($query) use ($startTime, $finishTime) {
                                        $query->where('start_time', '<=', $startTime);
                                        $query->where('end_time', '>', $startTime);
                                    });
                                $query->orWhere(function($query) use ($startTime, $finishTime) {
                                        $query->where('start_time', '<', $finishTime);
                                        $query->where('end_time', '>=', $finishTime);
                                    });
                                    $query->orWhere(function($query) use ($startTime, $finishTime) {
                                        $query->where('start_time', '>=', $startTime);
                                        $query->where('end_time', '<=', $finishTime);
                                    });
                            })
                        */
                ->where($detailCheckField, '=', $returnvalue)
                ->count();
            /*
            if record is found there is any other timetable using the room in the selected time frame
            */
            $startDate = Carbon::parse($startWeek);
            $endDate = Carbon::parse($endWeek);
            while ($startDate->lte($endDate)) {
                $dayWiseDate = $startDate->toDateString();
                $dateWiseDay = $startDate->format('l');
                if (in_array($dateWiseDay, $day)) {
                    $availbleRecord[] = [
                        'MeetingID' => $j,
                        $returnKeyValue => $returnvalue,
                        'Title' => (! $validRecord) ? 'Available' : 'Unavailable', // if record found the room is occupied
                        'Start' => $dayWiseDate.' '.$startTime,
                        'End' => $dayWiseDate.' '.$finishTime,
                        'Onlydate' => $dayWiseDate,
                        'IsAllDay' => true,
                        'Description' => null,
                        'Day' => $dateWiseDay,
                    ];
                }
                $startDate->addDay();
            }
            $j++;
        }
        $array = [
            $checkfor => $retrunData,
            // 'dataarray' => array_merge($availbleRecord,$notAvailbleRecord), as we are using only single array no need to merge
            'dataarray' => $availbleRecord,
        ];

        return $array;
    }

    public function getAvailableRoom($timeTableData)
    {
        return $this->checkAvailability($timeTableData, 'rooms');
    }

    public function getAvailableTeacher($timeTableData)
    {
        return $this->checkAvailability($timeTableData, 'teachers');
    }

    public function saveReplacementTeacher($data)
    {
        $collegeId = Auth::user()->college_id;
        $startWeekId = explode('/', $data['replacement_start_week'])[0];
        $endWeekId = explode('/', $data['replacement_to_week'])[0];
        $getTimetableId = Timetable::where('college_id', $collegeId)
            ->where('teacher_id', $data['teacher_id'])
            ->where('year', $data['year_list'])
            ->where('semester_id', $data['replacement_semester'])
            ->where('term', $data['replacement_term'])
            ->where('subject_id', $data['replacement_subject'])
            ->where('batch', $data['replacement_batch'])
            ->where('start_week_id', $startWeekId)
            ->where('end_week_id', $endWeekId);
        if ($data['course_criteria'] == 'Specific Classes') {
            $classId = explode('-', $data['replacement_class']);
            $classroomID = $classId[0];
            $getday = explode('@', $classId[1]);
            $day = $getday[0];
            $getTimetableId->where('classroom_id', $classroomID);
            $getTimetableId->where('day', $day);
        }
        $timetableIdData = $getTimetableId->get(['id'])->toArray();
        for ($i = 0; $i < count($timetableIdData); $i++) {
            $objTeacherReplacement = Timetable::find($timetableIdData[$i]['id']);
            $objTeacherReplacement->teacher_id = $data['new_teacher_id'];
            $objTeacherReplacement->save();
        }
        $result = ['status' => 'success', 'message' => 'Teacher Replacement successfully'];
        echo json_encode($result);
        exit;
    }

    public function getRoomDetails($request, $data)
    {
        $getRoomsDetails = CampusVenue::from('rto_venue as rtr')
            ->leftJoin('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id')
            ->select('rtc.id as classroomId', 'rtc.room_id', 'rtc.room_name', DB::raw("CONCAT(rtr.venue_name,' ',rtr.state) as address"))
            ->where('rtc.id', '=', $data['classroom_id'])->get()->toArray();
        $getCousreAndUnitDetails = $this->getSessionData($request, 'course_and_unit');
        $startWeek = $getCousreAndUnitDetails['week_start'];
        $startWeek = explode('/', $startWeek);
        $endWeek = $getCousreAndUnitDetails['week_end'];
        $endWeek = explode('/', $endWeek);
        $recurringDateArr = $this->customDaysConvert($getCousreAndUnitDetails['days'], $startWeek[1], $endWeek[1]);
        $timeInfo[] = [
            'startDate' => date('d M Y', strtotime($startWeek[1])),
            'EndDate' => date('d M Y', strtotime($endWeek[1])),
            'singleDate' => $getCousreAndUnitDetails['days'][0].','.date('d M Y', strtotime($startWeek[1])),
            'startTime' => $getCousreAndUnitDetails['start_time'],
            'endTime' => $getCousreAndUnitDetails['end_time'],
            'days' => $getCousreAndUnitDetails['days'],
        ];
        $array = [
            'rooms' => $getRoomsDetails,
            'timeInfo' => $timeInfo,
            'recurringDateArr' => $recurringDateArr,
        ];

        return $array;
    }

    public function getTeacherDetails($request, $data)
    {
        $getStaffDetails = Staff::from('rto_staff_and_teacher as rst')
            ->select('rst.id as teacherId', DB::raw("CONCAT(rst.name_title,' ',rst.first_name,' ',rst.last_name) as teachername"))
            ->where('rst.id', '=', $data['teacher_id'])->get()->toArray();

        // Use our session handling method with cache fallback
        $getCousreAndUnitDetails = $this->getSessionData($request, 'course_and_unit');

        // Validate required session data
        if (! $getCousreAndUnitDetails) {
            throw new ApplicationException('Session data not found. Please start the timetable creation process again.');
        }

        // Check for required keys and provide defaults if missing
        $requiredKeys = ['week_start', 'week_end', 'days'];
        foreach ($requiredKeys as $key) {
            if (! isset($getCousreAndUnitDetails[$key])) {
                throw new ApplicationException("Required session data '$key' is missing. Please start the timetable creation process again.");
            }
        }

        $startWeek = $getCousreAndUnitDetails['week_start'];
        $startWeek = explode('/', $startWeek);
        $endWeek = $getCousreAndUnitDetails['week_end'];
        $endWeek = explode('/', $endWeek);

        // Handle classroom_id with proper validation
        $getRoomsDetails = [];
        if (isset($getCousreAndUnitDetails['classroom_id']) && ! empty($getCousreAndUnitDetails['classroom_id'])) {
            $getRoomsDetails = CampusVenue::from('rto_venue as rtr')
                ->leftJoin('rto_classroom as rtc', 'rtr.id', '=', 'rtc.venue_id')
                ->select('rtc.room_name')
                ->where('rtc.id', '=', $getCousreAndUnitDetails['classroom_id'])
                ->get()->toArray();
        }

        $recurringDateArr = $this->customDaysConvert($getCousreAndUnitDetails['days'], $startWeek[1], $endWeek[1]);

        // Safely access teacher name
        $data['teacher_name'] = isset($getStaffDetails[0]['teachername']) ? $getStaffDetails[0]['teachername'] : '';

        $finalArray = array_merge($getCousreAndUnitDetails, $data);
        // dd($finalArray);
        // Use our session handling method to store data
        $this->storeSessionData($request, 'course_and_unit', $finalArray);

        $array = [
            'roomInfo' => $getRoomsDetails,
            'teacherInfo' => $getStaffDetails,
            'recurringDateArr' => $recurringDateArr,
        ];

        return $array;
    }

    public function customDaysConvert($arr, $startDate, $endDate)
    {
        $days = [
            'Sunday',
            'Monday',
            'Tuesday',
            'Wednesday',
            'Thursday',
            'Friday',
            'Saturday',
        ];
        $resArr = [];
        foreach ($days as $k => $day) {
            if (in_array($day, $arr)) {
                $resArr[] = $k;
            }
        }
        $tm1 = strtotime($startDate);
        $tm2 = strtotime($endDate);
        for ($i = $tm1; $i <= $tm2; $i = $i + 86400) {
            if (in_array(date('w', $i), $resArr)) {
                $date = date('l, d F, Y', $i);
                $recurringDateArr[] = $date;
            }
        }

        return $recurringDateArr;
    }

    public function checkRequiredFields($keys, $array)
    {
        foreach ($keys as $key => $val) {
            if (! array_key_exists($key, $array)) {
                return false;
            }

        }

        return true;
    }

    public function getCheckRequiredFields($keys, $array)
    {
        $data = [];
        foreach ($keys as $key => $val) {
            if (! array_key_exists($key, $array)) {
                $data[] = $val;
            }

        }

        return $data;
    }

    public function saveTimetableData($request, $storedData)
    {
        $requiredFields = ['course_type' => 'Course Type', 'classroom_id' => 'Classroom', 'teacher_id' => 'Trainer', 'days' => 'Days', 'batch_radio' => 'Batch', 'start_time' => 'Start Time', 'end_time' => 'End Time', 'week_start' => 'Week Start', 'week_end' => 'Week End'];
        if (! $this->checkRequiredFields($requiredFields, $storedData)) {

            $validateKeys = $this->getCheckRequiredFields($requiredFields, $storedData);

            $result = ['status' => 'error', 'message' => 'Please fill this fields '.implode(', ', $validateKeys)];
            echo json_encode($result);
            exit;
        }
        DB::beginTransaction();
        try {
            /*
            get the config settins whether to split the timetable by day or not
            if false, only a single row will be created in rto_timetable table for a batch
            */
            $splitTimelines = config('features.splittimelines');
            $storedData['college_id'] = $request->user()->college_id;
            $venueId = Classroom::where('college_id', '=', $storedData['college_id'])->select('venue_id')->value('venue_id');
            DB::enableQueryLog();
            $storedData['class_capacity'] = Classroom::where('id', $storedData['classroom_id'])->value('max_capacity');
            $storedData['created_by'] = $request->user()->id;
            $storedData['updated_by'] = $request->user()->id;
            $storedData['course_type_id'] = $storedData['course_type'];
            $storedData['venue_id'] = $venueId;
            if ($storedData['batch_radio'] == 'new_batch') {
                $storedData['batch'] = $storedData['new_batch'];
            } else {
                $storedData['batch'] = $storedData['existing_batch'];
            }
            if ($storedData['default_timetable'] == 'Yes') {
                $time = explode('-', $storedData['default_time']);
                $storedData['start_time'] = date('H:i', strtotime($time[0]));
                $storedData['finish_time'] = date('H:i', strtotime($time[1]));
            } else {
                $storedData['start_time'] = date('H:i', strtotime($storedData['start_time']));
                $storedData['finish_time'] = date('H:i', strtotime($storedData['end_time']));
            }
            $explodeStartWeek = explode('/', ($storedData['week_start']));
            $storedData['start_week_id'] = $explodeStartWeek[0];
            $storedData['start_week'] = $explodeStartWeek[1];
            $explodeEndWeek = explode('/', ($storedData['week_end']));
            $storedData['end_week_id'] = $explodeEndWeek[0];
            $storedData['end_week'] = $explodeEndWeek[1];
            $storedData['census_date'] = date('Y-m-d', strtotime($request->data['census_date']));
            $timetables = [];

            if (! $splitTimelines) {
                /*
                for new timetable setting, a single timetable record will be created for all days
                */
                $storedData['day'] = $storedData['days'][0] ?? ''; // just get the first day selected
                $storedData['timetable_settings'] = json_encode(['days' => implode(',', $storedData['days'])]);
                $storedData['unified_timetable'] = true;
                Arr::forget($storedData, 'days');
                /*
                as timetable record will be a single and not specefic for a day, add it to all day key
                this will make the createTimetableDetails work for either of the scenarios
                */
                $timetables['all'] = $this->timeTable->create($storedData);
            } else {
                /*
                for old timetable setting, a different timetable record will be created for saperate days of a week
                */
                foreach ($storedData['days'] as $key => $value) {
                    $storedData['day'] = $value;
                    /*
                    the created timetable will be the array of day values
                    */
                    $timetables[$value] = $this->timeTable->create($storedData);
                }
            }
            $this->createTimetableDetails($timetables, $storedData);
            $blankdata = [];
            $request->session()->put('course_and_unit', $blankdata);
            $request->session()->save();
            $this->updateTimeTableDashboardCacheData();

            $detailArr = [
                'batch' => $storedData['batch'],
                'term' => $storedData['term'],
                'subjectId' => $storedData['subject_id'],
                'semesterId' => $storedData['semester_id'],
            ];

            $objSubjectMaterial = new SubjectMaterial;
            $objSubjectMaterial->createSubFolderForBatch($request->user()->college_id, $request->user()->id, $detailArr);

            DB::commit();
            $result = ['status' => 'success', 'message' => 'TimeTable Created successfully'];
            echo json_encode($result);
            exit;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function createTimetableDetails($timetables, $storedData)
    {
        if (empty($timetables) || ! is_array($timetables)) {
            return;
        }
        $details = [];
        foreach ($timetables as $key => $value) {
            // if timetable keys is all, we need to check the days by timetable_settings value
            $daysEnabled = explode(',', json_decode($value->timetable_settings ?? '{}')->days ?? '');
            $timetableId = $value->id;
            if (! $timetableId) {
                continue;
            }
            $startDate = Carbon::parse($storedData['start_week']);
            $endDate = Carbon::parse($storedData['end_week']);
            while ($startDate->lte($endDate)) {
                $dayWiseDate = $startDate->toDateString();
                $day = $startDate->format('l');
                if (($key == 'all' && in_array($day, $daysEnabled)) || $day == $key) {
                    $timeTableDetail = [
                        'timetable_id' => $timetableId,
                        'room_id' => $storedData['classroom_id'],
                        'teacher_id' => $storedData['teacher_id'],
                        'timetable_date' => $dayWiseDate,
                        'day' => $day,
                        'start_time' => $storedData['start_time'],
                        'end_time' => $storedData['finish_time'],
                        'break_from' => '00:00:00',
                        'break_to' => '00:00:00',
                    ];
                    $details[] = TimetableDetail::create($timeTableDetail);
                }
                $startDate->addDay();
            }
        }

        return $details;
    }

    public function studentListByBatch($data)
    {
        $getStudentsByBatch = $this->studentSubjectEnrolment->getStudentsOfBatch($data['subject_id'], $data['semester_id'], $data['term'], $data['batch']);

        return $getStudentsByBatch;
    }
}
