<script id="trainingPlanViewTemplate" type="text/html">
    <div class="inline-flex items-start justify-start p-6 bg-gray-100 w-full">
        <div class="inline-flex flex-col space-y-4 items-start justify-start w-full">
            <button data-id="#= data.plan_detail.id #" data-sc-id="#= data.student_detail.selectedStudCourseID #"
                class="openEditTrainingPlanBtn inline-flex space-x-2 items-center justify-center py-1.5 pl-2 pr-2.5 bg-primary-blue-50 border rounded-lg border-primary-blue-500">
                <img class="w-4 h-full" src="{{ asset('v2/img/plan_edit.svg') }}" />
                <p class="text-xs leading-5 text-primary-blue-500 uppercase">Edit this Training Plan</p>
            </button>
            <div
                class="flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
                <div class="flex flex-col items-start justify-start">
                    <div class="inline-flex space-x-5 items-center justify-start">
                        # if (data.student_detail.profile_picture == '') { let name =
                        data.student_detail.student_name.toUpperCase().split(/\s+/); let shortName = name[0].charAt(0) +
                        name[1].charAt(0); #
                        <div class="w-16 h-full rounded-full">
                            <div class='flex user-profile-pic flex-1 w-16 h-16 rounded-full  bg-blue-500 items-center'>
                                <span
                                    class='text-2xl flex justify-center items-center leading-6 px-1 w-full font-medium'>#=
                                    shortName #</span>
                            </div>
                        </div>
                        # } else { #
                        <div class="w-16 h-16 rounded-full">
                            <img class="w-full flex-1 rounded-full" src="#= data.student_detail.profile_picture #" />
                        </div>
                        # } #

                        <div class="inline-flex flex-col items-start justify-end">
                            <p class="text-sm font-bold leading-5 text-gray-900" id="studetName_view">#=
                                data.student_detail.student_name #</p>
                            <p class="text-xs leading-5 text-gray-400" id="gen_id_view">#=
                                data.student_detail.generated_stud_id #</p>
                        </div>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-full">
                    <p class="text-xs text-gray-700 font-medium leading-none" id="courseName_view">#=
                        data.student_detail.course_name #</p>
                    <div
                        class="flex items-center justify-center px-2.5 py-0.5 bg-#= data.student_detail.status_color #-100 rounded-lg">
                        <p
                            class="text-center text-#= data.student_detail.status_color #-900 text-sm font-normal leading-tight tracking-wide">
                            #= data.student_detail.status #</p>
                    </div>
                </div>
                <div class="flex flex-col justify-start w-full">
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Employer Name</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="employerName_view">#=
                                data.plan_detail.employer_name #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Contract Date</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="contract_date_view">#=
                                data.plan_detail.contractDateText #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Contract ID</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="contract_id_view">#=
                                data.plan_detail.contract_code #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Purchasing Contract Schedule ID
                            </p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="contract_schedule_id_view">#=
                                data.plan_detail.contract_schedule_id #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Apprenticeship Name</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="apprenticeship_name_view">#=
                                data.plan_detail.apprenticeship_name #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <p class="w-72 text-sm font-medium leading-5 text-gray-500">Status</p>
                            <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-100 rounded-lg">
                                <p class="text-xs leading-5 text-center text-primary-blue-800" id="status_view">#=
                                    data.plan_detail.statusText #</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Booking ID</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="booking_id_view">#=
                                data.plan_detail.booking_id #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Funding Source</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="funding_source_view">#=
                                data.plan_detail.fundingSourceText #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Venue Code</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="venue_code_view">#=
                                data.plan_detail.venue_code_text #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Australian Apprenticeship
                                Centre (AAC)</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="aac_view">#= data.plan_detail.aac #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Notes</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="note_view">#= data.plan_detail.note #
                            </p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Training Contract ID</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="training_contract_id_view">#=
                                data.plan_detail.training_contract_id #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Contract Type</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="contract_type_view">#=
                                data.plan_detail.contractTypeText #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Apprenticeship ID</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="apprenticeship_id_view">#=
                                data.plan_detail.apprenticeship_id #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Supervisor</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="supervisor_view">#=
                                data.plan_detail.supervisor#</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">State</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900" id="state_view">#=
                                data.plan_detail.stateText #</p>
                        </div>
                    </div>
                    <hr>
                    <div class="flex flex-col items-start justify-center py-4 w-full">
                        <div class="inline-flex space-x-4 items-center justify-end w-full">
                            <p class="w-1/2 text-sm font-medium leading-5 text-gray-500">Course Site ID</p>
                            <p class="w-1/2 text-sm leading-5 text-gray-900">#= data.plan_detail.course_site_name #</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>