<template>
    <div class="w-[472px] pr-[120px] justify-start items-center gap-8 inline-flex">
        <div class="h-5 justify-start items-center gap-1 flex">
            <div class="w-4 h-4 pl-1 pr-1 pt-1 pb-1 justify-center items-center flex">
                <img :src="icon" class="w-4 h-4">
            </div>
            <div class="grow shrink basis-0 text-gray-400 text-sm font-normal font-['Roboto'] leading-tight tracking-tight">{{ label }}</div>
        </div>
        <div class="h-5 justify-start items-center gap-1 flex">
            <div class="w-5 h-5 pl-1 pr-1 pt-1 pb-1 justify-center items-center flex"></div>
            <div class="text-gray-900 text-sm font-normal font-['Roboto'] leading-tight tracking-tight">{{ labelValue }}</div>
        </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  const props = defineProps({
    labelValue: String,
    label: String,
    icon: String,
  });
  </script>
  