<script id="editGeneralDetailsTemplate" type="text/html">
    <div id="1" class="w-full holder">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                <div class="inline-flex items-center justify-between">
                    <div class="flex space-x-2 items-center justify-start">
                        <p class="text-lg font-medium leading-6 text-gray-900">General Details</p>
                        <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                    </div>
                </div>
                #if (isScheduleExist){#
                <p class="text-xs italic text-red-500">Please delete the installment schedule before making any changes
                    to the offer. You can create the schedule again after making the changes.</p>
                <p class="text-xs italic text-red-500">The schedule has already been generated. Therefore, the agent can
                    not be changed.</p>
                #}#
            </div>
            <div class="flex space-x-4 my-5 items-center">
                <div class="flex w-16 h-16 display_profile_pic">
                    # if (arr.profile_pic == '') { let name = arr.full_name.toUpperCase().split(/\s+/); let shortName =
                    name[0].charAt(0) + name[1].charAt(0); #
                    <div class="rounded-md">
                        <div class='flex user-profile-pic w-16 h-16 !rounded-md bg-blue-500 items-center'><span
                                class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName
                                #</span></div>
                    </div>
                    # } else { #
                    <div class="w-16 h-16 rounded-md">
                        <img class="w-16 h-16 rounded-md object-cover object-top" src="#= arr.profile_pic #" />
                    </div>
                    # } #
                </div>
                <div class="flex flex-col items-start space-y-1 justify-center">
                    <p class="text-sm font-bold leading-5 text-gray-900">#= arr.full_name #</p>
                    <p class="text-xs leading-4 text-gray-400">#= arr.generated_stud_id #</p>
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">AVETMISS Claim (Funded)</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="is_claim" id="is_claim_edit" value="1" />
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Is Full qualification</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="is_qualification" id="is_qualification_edit" value="1" />
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Has the certificate been issued?</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="is_certificate" id="is_certificate_edit" value="1" />
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Certificate Issue Date</p>
                    <p class="text-sm leading-5 text-gray-500 w-full certificateIssueDateEdit">-</p>
                </div>
                <div class="inline-flex flex-col items-start justify-end w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Issued By</p>
                    <p class="text-sm leading-5 text-gray-500 w-full issuedByEdit">-</p>
                </div>
            </div>
            <div class="inline-flex items-center justify-start w-1/2">
                <div class="inline-flex flex-col items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700 w-full">Module of Delivery</p>
                    <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                </div>
                <div class="flex items-center justify-end customSwitchButton">
                    <input type="checkbox" name="mode_of_delivery" id="mode_of_delivery_edit" value="1" />
                </div>
            </div>
            <div class="inline-flex items-start justify-start w-full grid grid-cols-2 gap-4">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Offer Id</p>
                    <div class="w-full">
                        <input id="offer_id_edit" name="offer_id" />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start  w-full col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Course</p>
                    <div class="w-full">
                        <input id="course_id_edit" name="course_id" disabled />
                    </div>
                </div>
            </div>
            <div class="inline-flex items-start justify-start w-full grid grid-cols-2 gap-4">
                <div class="inline-flex flex-col space-y-1 items-start justify-start  w-full col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Which Campus?</p>
                    <div class="w-full">
                        <input id="campus_id_edit" name="campus_id" required data-message="Select Campus" />
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start  w-full col-span-1">
                    <p class="text-sm font-medium leading-5 text-gray-700">Result Calculation Method</p>
                    <div class="w-full">
                        <input id="res_cal_method_edit" name="res_cal_method" disabled />
                    </div>
                </div>
            </div>
            <div class="inline-flex space-x-4 items-start justify-start w-1/2">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <p class="text-sm font-medium leading-5 text-gray-700">Agent</p>
                    <div class="w-full">
                        <input id="agent_id_edit" name="agent_id" #if (isScheduleExist){# disabled #} else {# required
                            #}# data-message="Select Agent" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>